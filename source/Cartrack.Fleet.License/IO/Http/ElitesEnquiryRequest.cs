﻿using System.Text.Json.Serialization;

namespace Cartrack.Fleet.License.IO.Http;

public class ElitesEnquiryRequest {
    [<PERSON>son<PERSON>ropertyName("RequestHeader")]
    public ElitesRequestHeader RequestHeader { get; set; } = new();
    [JsonPropertyName("RequestBody")]
    public ElitesRequestBody RequestBody { get; set; } = new();

    public static ElitesEnquiryRequest Create(string driverIdNumber) {
        var requestDateTime = DateTime.Now;
        var request = new ElitesEnquiryRequest {
            RequestHeader = {
                MessageId = Guid.NewGuid().ToString(),
                GenerationDateTime = requestDateTime.ToString("yyyyMMddHHmmss")
            },
            RequestBody = {
                EnquiryRequest = new ElitesPromptData {
                    IdNo = driverIdNumber,
                    EnquiryDate = requestDateTime.ToString("yyyyMMdd")
                }
            }
        };

        return request;
    }
}