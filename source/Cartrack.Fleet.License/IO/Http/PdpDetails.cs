﻿using System.Text.Json.Serialization;

namespace Cartrack.Fleet.License.IO.Http;

public class PdpDetails {
    [JsonPropertyName("permitNo")] public string PermitNo { get; set; } = string.Empty;

    [JsonPropertyName("validity")] public string Validity { get; set; } = string.Empty;

    [JsonPropertyName("pdpStatus")] public string PdpStatus { get; set; } = string.Empty;

    [JsonPropertyName("joinDate")] public string JoinDate { get; set; } = string.Empty;

    [JsonPropertyName("issueDate")] public string IssueDate { get; set; } = string.Empty;

    [JsonPropertyName("division")] public string Division { get; set; } = string.Empty;

    [JsonPropertyName("pdpClasses")] public List<PdpClassItem> PdpClasses { get; set; } = [];

    [JsonPropertyName("RestrictedLicence")]
    public List<RestrictedLicenceItem> RestrictedLicence { get; set; } = [];
}