﻿using System.Text.Json.Serialization;

namespace Cartrack.Fleet.License.IO.Http;

public class ElitesQdl {
    [JsonPropertyName("idNo")] public string IdNo { get; set; } = string.Empty;

    [JsonPropertyName("enquiryDate")] public string EnquiryDate { get; set; } = string.Empty;

    [JsonPropertyName("totalDemeritPts")] public string TotalDemeritPts { get; set; } = string.Empty;

    [JsonPropertyName("qdlDetails")] public QdlDetails QdlDetails { get; set; } = new();

    [JsonPropertyName("pdlDetails")] public PdlDetails PdlDetails { get; set; } = new();

    [JsonPropertyName("suspension")] public Suspension Suspension { get; set; } = new();

    [JsonPropertyName("disqualification")] public Disqualification Disqualification { get; set; } = new();

    [JsonPropertyName("revocation")] public Revocation Revocation { get; set; } = new();

    [JsonPropertyName("driverTags")] public List<DriverTagsItem> DriverTags { get; set; } = [];
}