﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.License.Domain;
using Cartrack.Fleet.License.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.License.IO.Http;

[ApiController]
[Route("license")]
public partial class LicenseController(IMediator mediator, IUserRepository userRepository, IClientDriverRepository driverRepository, AppSettings settings)
    : ControllerBase {
}