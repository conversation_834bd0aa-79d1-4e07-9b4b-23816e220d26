﻿using System.Text.Json.Serialization;

namespace Cartrack.Fleet.License.IO.Http;

public class ElitesPdp {
    [JsonPropertyName("idNo")] public string IdNo { get; set; } = string.Empty;

    [JsonPropertyName("enquiryDate")] public string EnquiryDate { get; set; } = string.Empty;

    [JsonPropertyName("liableRevocation")] public List<LiableRevocationItem> LiableRevocation { get; set; } = [];

    [JsonPropertyName("eventLog")] public List<EventLogItem> EventLog { get; set; } = [];

    [JsonPropertyName("qdlConversion")] public List<QdlConversionItem> QdlConversion { get; set; } = [];

    [JsonPropertyName("pdpDetails")] public PdpDetails PdpDetails { get; set; } = new();
}