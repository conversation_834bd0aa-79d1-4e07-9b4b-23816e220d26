﻿using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.License.Domain;
using Cartrack.Fleet.License.IO.Http;
using Microsoft.EntityFrameworkCore;
using System.Globalization;

namespace Cartrack.Fleet.License.IO.Sql;

public class ElitesRepository(AppTfmsCustomDbContext dbContext) : IElitesRepository {
    private const string QDL = "QDL";
    private const string PDL = "PDL";
    private const string PDP = "PDP";

    public async Task<ElitesQdlTfms?> GetQdlLicense(string idNo) {
        var license = await dbContext.ElitesLicenseQdls.FirstOrDefaultAsync(t => t.IdNo == idNo);
        if (license is not null) {
            var elitesQdl = new ElitesQdlTfms {
                IdNo = license.IdNo,
                Disqualification =
                    new DisqualificationTfms {
                        DisqualificationStartDateUtc = license.DisqualificationStartDate?.ToUniversalTime() ?? DateTime.MinValue,
                        DisqualificationEndDateUtc = license.DisqualificationEndDate?.ToUniversalTime() ?? DateTime.MinValue
                    },
                Revocation =
                    new RevocationTfms {
                        RevocationStartDateUtc = license.RevocationStartDate?.ToUniversalTime() ?? DateTime.MinValue,
                        RevocationEndDateUtc = license.RevocationEndDate?.ToUniversalTime() ?? DateTime.MinValue
                    },
                Suspension =
                    new SuspensionTfms {
                        SuspensionStartDateUtc = license.SuspensionStartDate?.ToUniversalTime() ?? DateTime.MinValue,
                        SuspensionEndDateUtc = license.SuspensionEndDate?.ToUniversalTime() ?? DateTime.MinValue
                    },
                TotalDemeritPts = license.TotalDemeritPts,
                QdlDetails = new QdlDetailsTfms { QdlExpiryDateUtc = license.QdlExpiryDate?.ToUniversalTime() ?? DateTime.MinValue, QdlValidity = license.QdlValidity },
                PdlDetails = new PdlDetailsTfms { PdlExpiryDateUtc = license.PdlExpiryDate?.ToUniversalTime() ?? DateTime.MinValue, PdlValidity = license.PdlValidity }
            };

            var licenseClasses = await dbContext.ElitesLicenseClasses.Where(t => t.IdNo == idNo && (t.LicenseClassType == QDL || t.LicenseClassType == PDL)).ToArrayAsync();
            foreach (var lc in licenseClasses) {
                if (lc.LicenseClassType == QDL) {
                    elitesQdl.QdlDetails.QdlClasses.Add(new QdlClassItemTfms { LicenseClass = lc.LicenseClass, IssueDateUtc = lc.IssueDate?.ToUniversalTime() ?? DateTime.MinValue });
                }

                if (lc.LicenseClassType == PDL) {
                    elitesQdl.PdlDetails.PdlClasses.Add(new QdlClassItemTfms { LicenseClass = lc.LicenseClass, IssueDateUtc = lc.IssueDate?.ToUniversalTime() ?? DateTime.MinValue });
                }
            }

            return elitesQdl;
        }

        return null;
    }

    public async Task<ElitesPdpTfms?> GetPdpLicense(string idNo) {
        var license = await dbContext.ElitesLicensePdps.FirstOrDefaultAsync(t => t.IdNo == idNo);
        if (license is not null) {
            var elitesPdp = new ElitesPdpTfms {
                IdNo = idNo,
                PdpDetails = new PdpDetailsTfms {
                    IssueDateUtc = license.IssueDate?.ToUniversalTime(),
                    JoinDateUtc = license.JoinDate?.ToUniversalTime(),
                    PdpStatus = license.Status,
                    PermitNo = license.PermitNo ?? "",
                    Validity = license.Validity
                }
            };

            return elitesPdp;
        }

        return null;
    }

    public async Task<long> SavePdp(ElitesPdp? pdp, string raw) {
        if (pdp == null)
            return 0;
        
        var pdpDb = await dbContext.ElitesLicensePdps.FirstOrDefaultAsync(t => t.IdNo == pdp.IdNo);
        if (pdpDb == null) {
            pdpDb = new ElitesLicensePdp { CreateTs = DateTime.UtcNow };
            await dbContext.ElitesLicensePdps.AddAsync(pdpDb);
        }

        pdpDb.IdNo = pdp.IdNo;
        pdpDb.IssueDate = Utils.ParseDate(pdp.PdpDetails.IssueDate);
        pdpDb.JoinDate = Utils.ParseDate(pdp.PdpDetails.JoinDate);
        pdpDb.Status = pdp.PdpDetails.PdpStatus;
        pdpDb.PermitNo = pdp.PdpDetails.PermitNo;
        pdpDb.Division = pdp.PdpDetails.Division;
        pdpDb.Validity = pdp.PdpDetails.Validity;
        pdpDb.Raw = raw;

        var licClass = await dbContext.ElitesLicenseClasses.Where(t => t.IdNo == pdp.IdNo && t.LicenseClassType == PDP).ToArrayAsync();
        foreach (var lc in pdp.PdpDetails.PdpClasses) {
            var lcDb = licClass.FirstOrDefault(t => t.LicenseClass == lc.Class);
            if (lcDb == null) {
                dbContext.ElitesLicenseClasses.Add(new ElitesLicenseClass {
                    IdNo = pdp.IdNo, LicenseClass = lc.Class, LicenseClassType = PDP,
                    IssueDate = Utils.ParseDate(lc.IssueDate),
                    CreatedTs = DateTime.UtcNow
                });
            }
            else {
                lcDb.IssueDate = Utils.ParseDate(lc.IssueDate);
            }
        }

        await dbContext.SaveChangesAsync();
        return pdpDb.Id;
    }

    public async Task<long> SaveQdl(ElitesQdl? qdl, string raw) {
        if (qdl == null)
            return 0;
        
        var qdlDb = await dbContext.ElitesLicenseQdls.FirstOrDefaultAsync(t => t.IdNo == qdl.IdNo);
        if (qdlDb == null) {
            qdlDb = new ElitesLicenseQdl();
            await dbContext.ElitesLicenseQdls.AddAsync(qdlDb);
        }

        qdlDb.IdNo = qdl.IdNo;
        qdlDb.TotalDemeritPts = string.IsNullOrWhiteSpace(qdl.TotalDemeritPts) ? 0 : int.Parse(qdl.TotalDemeritPts);
        qdlDb.QdlValidity = qdl.QdlDetails.QdlValidity;
        qdlDb.QdlExpiryDate = Utils.ParseDate(qdl.QdlDetails.QdlExpiryDate);
        qdlDb.SuspensionStartDate = Utils.ParseDate(qdl.Suspension.SuspStartDate);
        qdlDb.SuspensionEndDate = Utils.ParseDate(qdl.Suspension.SuspEndDate);
        qdlDb.DisqualificationStartDate = Utils.ParseDate(qdl.Disqualification.DisqStartDate);
        qdlDb.DisqualificationEndDate =Utils. ParseDate(qdl.Disqualification.DisqEndDate);
        qdlDb.CreatedTs = DateTime.UtcNow;
        qdlDb.PdlValidity = qdl.PdlDetails.PdlValidity;
        qdlDb.PdlExpiryDate = Utils.ParseDate(qdl.PdlDetails.PdlExpiryDate);
        qdlDb.Raw = raw;

        var licClass = await dbContext.ElitesLicenseClasses.Where(t => t.IdNo == qdl.IdNo && t.LicenseClassType == QDL).ToArrayAsync();
        foreach (var lc in qdl.QdlDetails.QdlClasses) {
            var lcDb = licClass.FirstOrDefault(t => t.LicenseClass == lc.Class);
            if (lcDb == null) {
                dbContext.ElitesLicenseClasses.Add(new ElitesLicenseClass {
                    IdNo = qdl.IdNo, LicenseClass = lc.Class, LicenseClassType = QDL, 
                    IssueDate = Utils.ParseDate(lc.IssueDate),
                    CreatedTs = DateTime.UtcNow
                });
            }
            else {
                lcDb.IssueDate = Utils.ParseDate(lc.IssueDate);
            }
        }

        licClass = await dbContext.ElitesLicenseClasses.Where(t => t.IdNo == qdl.IdNo && t.LicenseClassType == PDL).ToArrayAsync();
        foreach (var lc in qdl.PdlDetails.PdlClasses) {
            var lcDb = licClass.FirstOrDefault(t => t.LicenseClass == lc.Class);
            if (lcDb == null) {
                dbContext.ElitesLicenseClasses.Add(new ElitesLicenseClass {
                    IdNo = qdl.IdNo, LicenseClass = lc.Class, LicenseClassType = PDL,
                    IssueDate = Utils.ParseDate(lc.IssueDate),
                    CreatedTs = DateTime.UtcNow
                });
            }
            else {
                lcDb.IssueDate = Utils.ParseDate(lc.IssueDate);
            }
        }

        await dbContext.SaveChangesAsync();
        return qdlDb.Id;
    }
}