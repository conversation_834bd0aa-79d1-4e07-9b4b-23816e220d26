﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.License.Domain;
using Cartrack.Fleet.License.IO.Http;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.License.IO.Sql;

public partial class ClientDriverRepository(AppSettings settings) : IClientDriverRepository {

    private AppFleetDbContext _context = new AppFleetDbContext(settings.ConnectionString);

    
    public async Task UpdateNormalLicense(string clientDriverId, ElitesQdlTfms qdl, CancellationToken token) {
        var driver = this._context.ClientDrivers.FirstOrDefault(t => t.ClientDriverId == clientDriverId);
        if (driver == null)
            return;
       
        var licenseTypes = this._context.DriverLicenseTypes.Where(t => t.UserId == driver.UserId).ToList();
        await this.UpdateLicenseValidity(qdl, token, driver);
        await this.UpdateDriverLicensesWithQdlDetails(clientDriverId, qdl, token, licenseTypes);
        await this.RemoveUnmatchedDriverLicenses(clientDriverId, qdl, token, licenseTypes);
    }

    public async Task UpdateSpecialLicense(string clientDriverId, ElitesPdpTfms pdp, CancellationToken token) {
        var driver = this._context.ClientDrivers.FirstOrDefault(t => t.ClientDriverId == clientDriverId);
        if (driver == null)
            return;
        
        var specialLicenseTypes = this._context.DriverSpecialLicenseTypes.Where(t => t.UserId == driver.UserId).ToList();
        await this.UpdateDriverLicensesWithPdpDetails(clientDriverId, pdp, token, specialLicenseTypes);
    }

    public Task<ClientDriver?> GetDriverByClientDriverId(string clientDriverId) {
        return this._context.ClientDrivers.FirstOrDefaultAsync(d => d.ClientDriverId == clientDriverId);
    }

    private async Task RemoveUnmatchedDriverLicenses(string clientDriverId, ElitesQdlTfms qdl, CancellationToken token, List<DriverLicenseType> licenseTypes) {
        var removeThis = new List<ClientDriverLicense>();
        foreach (var r in this._context.ClientDriverLicenses.Where(t => t.ClientDriverId == clientDriverId)) {
            var licClass = licenseTypes.FirstOrDefault(l => l.DriverLicenseTypeId == r.DriverLicenseTypeId);
            if (licClass != null) {
                if (qdl.QdlDetails.QdlClasses.All(q => q.LicenseClass != licClass.Name)) {
                    //Remove the license
                    removeThis.Add(r);
                }
            }
        }

        foreach (var r in removeThis)
            this._context.ClientDriverLicenses.Remove(r);

        await this._context.SaveChangesAsync(token);
    }
    
    private async Task RemoveUnmatchedDriverLicenses(string clientDriverId, ElitesPdpTfms qdl, CancellationToken token, List<DriverSpecialLicenseType> specialLicenseTypes) {
        var removeThis = new List<ClientDriverSpecialLicense>();
        foreach (var r in this._context.ClientDriverSpecialLicenses.Where(t => t.ClientDriverId == clientDriverId)) {
            var licClass = specialLicenseTypes.FirstOrDefault(l => l.DriverSpecialLicenseTypeId == r.DriverSpecialLicenseTypeId);
            if (licClass != null) {
                if (qdl.PdpDetails.PdpClasses.All(q => q.LicenseClass != licClass.LicenseName)) {
                    //Remove the license
                    removeThis.Add(r);
                }
            }
        }

        foreach (var r in removeThis)
            this._context.ClientDriverSpecialLicenses.Remove(r);

        await this._context.SaveChangesAsync(token);
    }

    private async Task UpdateDriverLicensesWithQdlDetails(string clientDriverId, ElitesQdlTfms qdl, CancellationToken token, List<DriverLicenseType> licenseTypes) {
        var licenses = this._context.ClientDriverLicenses.Where(t => t.ClientDriverId == clientDriverId).ToList();
        foreach (var q in qdl.QdlDetails.QdlClasses) {
            var licType = licenseTypes.FirstOrDefault(l => l.Name == q.LicenseClass);
            if (licType != null) {
                var existingLicense = licenses.FirstOrDefault(l => l.DriverLicenseTypeId == licType.DriverLicenseTypeId);
                if (existingLicense != null) {
                    existingLicense.LicenseValidStart = q.IssueDateUtc;
                    existingLicense.LicenseValidEnd = qdl.QdlDetails.QdlExpiryDateUtc;
                    existingLicense.SuspensionStartDate = qdl.Suspension?.SuspensionStartDateUtc;
                    existingLicense.SuspensionEndDate = qdl.Suspension?.SuspensionEndDateUtc;
                    existingLicense.DisqualificationStartDate = qdl.Disqualification?.DisqualificationStartDateUtc;
                    existingLicense.DisqualificationEndDate = qdl.Disqualification?.DisqualificationEndDateUtc;
                    existingLicense.RevocationStartDate = qdl.Revocation?.RevocationStartDateUtc;
                    existingLicense.RevocationEndDate = qdl.Revocation?.RevocationEndDateUtc;
                }
                else {
                    this._context.ClientDriverLicenses.Add(new ClientDriverLicense() {
                        ClientDriverId = clientDriverId, DriverLicenseTypeId = licType.DriverLicenseTypeId, LicenseValidStart = q.IssueDateUtc, LicenseValidEnd = qdl.QdlDetails.QdlExpiryDateUtc, 
                        SuspensionStartDate = qdl.Suspension.SuspensionStartDateUtc, SuspensionEndDate = qdl.Suspension.SuspensionEndDateUtc, 
                        DisqualificationStartDate = qdl.Disqualification.DisqualificationStartDateUtc, DisqualificationEndDate = qdl.Disqualification.DisqualificationEndDateUtc, 
                        RevocationStartDate = qdl.Revocation.RevocationStartDateUtc, RevocationEndDate = qdl.Revocation.RevocationEndDateUtc
                    });
                }
            }
        }

        await this._context.SaveChangesAsync(token);
    }
    
    private async Task UpdateDriverLicensesWithPdpDetails(string clientDriverId, ElitesPdpTfms pdp, CancellationToken token, List<DriverSpecialLicenseType> specialLicenseTypes) {
        var licenses = this._context.ClientDriverSpecialLicenses.Where(t => t.ClientDriverId == clientDriverId).ToList();
        foreach (var p in pdp.PdpDetails.PdpClasses) {
            var licType = specialLicenseTypes.FirstOrDefault(l => l.LicenseName == p.LicenseClass);
            if (licType != null) {
                var existingLicense = licenses.FirstOrDefault(l => l.DriverSpecialLicenseTypeId == licType.DriverSpecialLicenseTypeId);
                if (existingLicense != null) {
                    existingLicense.SpecialLicenseNumber = pdp.PdpDetails.PermitNo;
                    existingLicense.SpecialLicenseIssueDate = p.IssueDateUtc;
                }
                else {
                    this._context.ClientDriverSpecialLicenses.Add(new ClientDriverSpecialLicense() {
                        ClientDriverId = clientDriverId, 
                        DriverSpecialLicenseTypeId = licType.DriverSpecialLicenseTypeId, 
                        SpecialLicenseNumber = pdp.PdpDetails.PermitNo, 
                        SpecialLicenseIssueDate = p.IssueDateUtc
                    });
                }
            }
        }

        await this._context.SaveChangesAsync(token);
    }

    private async Task UpdateLicenseValidity(ElitesQdlTfms qdl, CancellationToken token, ClientDriver driver) {
        driver.LicenseValidEnd = qdl.QdlDetails.QdlExpiryDateUtc;
        await this._context.SaveChangesAsync(token);
    }
}