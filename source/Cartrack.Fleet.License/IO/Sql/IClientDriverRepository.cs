﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.License.Domain;

namespace Cartrack.Fleet.License.IO.Sql;

public interface IClientDriverRepository {
    Task UpdateNormalLicense(string clientDriverId, ElitesQdlTfms qdl, CancellationToken token);

    Task UpdateSpecialLicense(string clientDriverId, ElitesPdpTfms pdp, CancellationToken token);
    Task<ClientDriver?> GetDriverByClientDriverId(string requestClientDriverId);
}