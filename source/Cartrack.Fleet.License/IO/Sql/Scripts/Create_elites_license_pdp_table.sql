-- tfms_custom.elites_license_pdp definition

-- Drop table

-- DROP TABLE tfms_custom.elites_license_pdp;

CREATE TABLE tfms_custom.elites_license_pdp (
                                                id int8 GENERATED ALWAYS AS IDENTITY( INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE) NOT NULL,
                                                id_no varchar NOT NULL,
                                                create_ts timestamptz NOT NULL,
                                                permit_no varchar NULL,
                                                validity varchar NOT NULL,
                                                status varchar NOT NULL,
                                                join_date timestamptz NOT NULL,
                                                issue_date timestamptz NOT NULL,
                                                division varchar NULL,
                                                raw text NOT NULL,
                                                CONSTRAINT elites_license_pdp_pk PRIMARY KEY (id)
);