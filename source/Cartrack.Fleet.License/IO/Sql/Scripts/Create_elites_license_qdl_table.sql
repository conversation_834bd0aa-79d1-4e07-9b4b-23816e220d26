-- tfms_custom.elites_license_qdl definition

-- Drop table

-- DROP TABLE tfms_custom.elites_license_qdl;

CREATE TABLE tfms_custom.elites_license_qdl (
                                                id int8 GENERATED ALWAYS AS IDENTITY( INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE) NOT NULL,
                                                id_no varchar NOT NULL,
                                                total_demerit_pts int4 NULL,
                                                qdl_validity varchar NOT NULL,
                                                qdl_expiry_date timestamptz NULL,
                                                suspension_start_date timestamptz NULL,
                                                suspension_end_date timestamptz NULL,
                                                disqualification_start_date timestamptz NULL,
                                                disqualification_end_date timestamptz NULL,
                                                revocation_start_date timestamptz NULL,
                                                revocation_end_date timestamptz NULL,
                                                created_ts timestamptz NOT NULL,
                                                pdl_validity varchar NOT NULL,
                                                pdl_expiry_date timestamptz NULL,
                                                raw text NOT NULL,
                                                CONSTRAINT elites_license_pk PRIMARY KEY (id)
);