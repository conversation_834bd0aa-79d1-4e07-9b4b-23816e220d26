﻿using Cartrack.AppHost.Channels;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.License.Domain;
using Cartrack.Fleet.License.Features.GetPdpLicense;
using Cartrack.Fleet.License.Features.GetQdlLicense;
using Cartrack.Fleet.License.IO.Sql;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Cartrack.Fleet.License.Worker;

public class LicenseUpdateWorker(IChannel channel, AppSettings settings, ILogger<AppHost.WorkerHosting.Worker> logger, bool autoRestartUnlessStopped = false)
    : AppHost.WorkerHosting.Worker(logger, autoRestartUnlessStopped) {
    private ElitesRepository _repo = new(new AppTfmsCustomDbContext(settings.ConnectionString));
    private IClientDriverRepository _driverRepo = new ClientDriverRepository(settings);

    public override async Task DoWork(CancellationToken cancellationToken) {
        var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        await Task.WhenAll(SavePdp(cts.Token), SaveQdl(cts.Token));
        
        async Task SavePdp(CancellationToken token) {
            var pdpChannel = channel.GetChannel<ApiPdpEnquiryResponse>(token);
            await foreach (var pdpResponse in pdpChannel.TakeAll().WithCancellation(token)) {
                var js = JsonSerializer.Serialize(pdpResponse.Pdp);
                try {
                    await this._repo.SavePdp(pdpResponse.Pdp.ResponseBody.EnquiryResponse, js);
                    //Update the relevant fleet tables
                    var pdpTfms = ElitesPdpTfms.From(pdpResponse.Pdp.ResponseBody.EnquiryResponse);
                    if (pdpTfms != null)
                        await this._driverRepo.UpdateSpecialLicense(pdpResponse.ClientDriverId, pdpTfms, token);
                    
                }
                catch (Exception exc) {
                    logger.LogWarning(exc, "Error saving PDP");
                }
            }
        }
        
        async Task SaveQdl(CancellationToken token) {
            var qdlChannel = channel.GetChannel<ApiQdlEnquiryResponse>(token);
            await foreach (var qdlResponse in qdlChannel.TakeAll().WithCancellation(token)) {
                var js = JsonSerializer.Serialize(qdlResponse.Qdl);
                try {
                    //save the raw API response
                    await this._repo.SaveQdl(qdlResponse.Qdl.ResponseBody.EnquiryResponse, js);
                    
                    //Update the relevant fleet tables
                    var qdlTfms = ElitesQdlTfms.From(qdlResponse.Qdl.ResponseBody.EnquiryResponse);
                    if (qdlTfms != null)
                        await this._driverRepo.UpdateNormalLicense(qdlResponse.ClientDriverId, qdlTfms, token);
                }
                catch (Exception exc) {
                    logger.LogWarning(exc, "Error saving QDL");
                }
            }
        }
    }
}