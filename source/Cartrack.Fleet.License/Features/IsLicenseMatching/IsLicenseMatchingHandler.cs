﻿using Cartrack.AppHost;
using Cartrack.AppHost.Channels;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.License.Domain;
using Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;
using Cartrack.Fleet.License.IO.Http;
using Cartrack.Fleet.License.IO.Sql;
using Cartrack.Fleet.License.Worker;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.License.Features.IsLicenseMatching;

public class IsLicenseMatchingHandler(AppSettings appSettings, AppFleetDbContext fleetDbContext, IHttpContextAccessor context,  IClientDriverRepository driverRepo, ILogger<IsLicenseMatchingHandler> logger)
    : IRequestHandler<IsLicenseMatchingRequest, IsLicenseMatchingResponse> {
    
    private readonly IVehicleLicenseValidation[] _validators = [
        new Class3VehicleValidation(),
        new Class4VehicleValidation(),
        new Class2VehicleValidation(),
        new Class2AVehicleValidation(),
        new ClassEv2VehicleValidation(),
        new ClassEv2AVehicleValidation(),
        new ClassEv4FVehicleValidation(),
        new ClassEvAVehicleValidation(),
        new Class5VehicleValidation()
    ];
    
    public async Task<IsLicenseMatchingResponse> Handle(IsLicenseMatchingRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.ClientDriverId, nameof(request.ClientDriverId));
            Requires.IsTrue(() => request.VehicleId > 0, () => "VehicleId must be greater than 0");

            logger.LogInformation("[{TraceId}] Validating vehicle and driver licenses", context.HttpContext?.TraceIdentifier ?? "");
            
            var (isValid, err) = await Validate(request);
            if (!isValid) {
                logger.LogWarning("[{TraceId}] Vehicle and driver licenses do not match. Error is: {Error}", context.HttpContext?.TraceIdentifier ?? "", err);
            }
            
            var response = new IsLicenseMatchingResponse(new LicenseCheck(isValid, err));
            return response;
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error validating vehicle and driver licenses", context.HttpContext?.TraceIdentifier ?? "");
            return new IsLicenseMatchingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error validating vehicle and driver licenses", context.HttpContext?.TraceIdentifier ?? "");
            return new IsLicenseMatchingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private async Task<(bool, string)> Validate(IsLicenseMatchingRequest request) {
        var vehicleNormalLicenses =
            from v in fleetDbContext.VehicleDriverLicenses
            join l in fleetDbContext.DriverLicenseTypes on v.DriverLicenseTypeId equals l.DriverLicenseTypeId
            where v.VehicleId == request.VehicleId
            select new { VehicleId = request.VehicleId, DriverLicenseTypeId = v.DriverLicenseTypeId, Name = l.Name };

        var vehicleSpecialLicenses = 
            from v in fleetDbContext.VehicleSpecialLicenses
            join l in fleetDbContext.DriverSpecialLicenseTypes on v.SpecialLicenseTypeId equals l.DriverSpecialLicenseTypeId
            where v.VehicleId == request.VehicleId
            select new { VehicleId = request.VehicleId, SpecialLicenseTypeId = v.SpecialLicenseTypeId, Name = l.LicenseName };
        
        var vehicleNormalLicenseNames = await vehicleNormalLicenses.Select(v => v.Name).ToArrayAsync();
        var vehicleSpecialLicenseNames = await vehicleSpecialLicenses.Select(v => v.Name).ToArrayAsync();

        var driverNormalLicenseNames = 
            await fleetDbContext.ClientDriverLicenses
                .Where(v => v.ClientDriverId == request.ClientDriverId)
                .Select(v => v.DriverLicenseType.Name).ToArrayAsync();
        var driverSpecialLicenseNames = 
            await fleetDbContext.ClientDriverSpecialLicenses
                .Where(v => v.ClientDriverId == request.ClientDriverId)
                .Select(v => v.DriverSpecialLicenseType.LicenseName)
                .ToArrayAsync();

        foreach (var validator in this._validators) {
            if (validator.CanHandle(vehicleNormalLicenseNames, vehicleSpecialLicenseNames)) {
                return validator.Validate(driverNormalLicenseNames, driverSpecialLicenseNames);
            }
        }
        
        return (false, "Unexpected error. License validation failed.");
    }
}