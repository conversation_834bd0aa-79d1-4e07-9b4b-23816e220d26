﻿using Cartrack.Fleet.License.Features.IsLicenseMatching;
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.License.Domain;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.License.IO.Http;

public partial class LicenseController {
    
    [HttpPost]
    [Route("ismatching")]
    public async Task<ActionResult<IsLicenseMatchingResponse>> IsLicenseMatching([FromBody] IsLicenseMatchingRequest req) {
        var resp = await mediator.Send(req);
        return  resp.ToContentResult<IsLicenseMatchingResponse, LicenseCheck>(this.HttpContext);
    }
}