﻿using Cartrack.Fleet.License.Features.GetPdpLicense;
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.License.Domain;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.License.IO.Http;

public partial class LicenseController {
    
    [HttpGet]
    [Route("pdp/{clientDriverId}")]
    public async Task<ActionResult<GetPdpLicenseResponse>> GetPdpLicense(string clientDriverId) {
        var resp = await mediator.Send(new GetPdpLicenseRequest(clientDriverId));
        return  resp.ToContentResult<GetPdpLicenseResponse, ElitesPdpTfms>(this.HttpContext);
    }
}