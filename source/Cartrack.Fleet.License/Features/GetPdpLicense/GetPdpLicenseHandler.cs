﻿using Cartrack.AppHost;
using Cartrack.AppHost.Channels;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.License.Domain;
using Cartrack.Fleet.License.IO.Http;
using Cartrack.Fleet.License.IO.Sql;
using Cartrack.Fleet.License.Worker;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.License.Features.GetPdpLicense;

public class GetPdpLicenseHandler(AppSettings appSettings, IChannel channel, IElitesApiClient elitesClient, IHttpContextAccessor context,  IClientDriverRepository driverRepo, ILogger<GetPdpLicenseHandler> logger)
    : IRequestHandler<GetPdpLicenseRequest, GetPdpLicenseResponse> {
    public async Task<GetPdpLicenseResponse> Handle(GetPdpLicenseRequest request, CancellationToken cancellationToken) {
        var driver = await driverRepo.GetDriverByClientDriverId(request.ClientDriverId);
        if (driver is null) {
            return new GetPdpLicenseResponse(null, new ArgumentException($"Driver with id {request.ClientDriverId} not found")) { IsServerError = false };
        }
             
        var nric = AesEncryption.Decrypt(driver.IdNumber!, appSettings.TfmsEncryptionKey, appSettings.TfmsEncryptionIv);
        var pdp = await elitesClient.GetPdpLicense(ElitesEnquiryRequest.Create(nric!), cancellationToken);
        if (pdp is null) {
            return new GetPdpLicenseResponse(null){ IsServerError = true };
        }
       
        var ch = channel.GetChannel<ApiPdpEnquiryResponse>(cancellationToken);
        await ch.Write(new ApiPdpEnquiryResponse(request.ClientDriverId, pdp));
        
        var resp = ElitesPdpTfms.From(pdp.ResponseBody.EnquiryResponse);
        var response = new GetPdpLicenseResponse(resp);
        return response;
    }
}