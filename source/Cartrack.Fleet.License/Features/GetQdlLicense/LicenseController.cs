using Cartrack.Fleet.Common;
using Cartrack.Fleet.License.Domain;
using Cartrack.Fleet.License.Features.GetQdlLicense;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.License.IO.Http;

public partial class LicenseController {
    
    [HttpGet]
    [Route("qdl/{clientDriverId}")]
    public async Task<ActionResult<GetQdlLicenseResponse>> GetQdlLicense(string clientDriverId) {
        var resp = await mediator.Send(new GetQdlLicenseRequest(clientDriverId));
        return  resp.ToContentResult<GetQdlLicenseResponse, ElitesQdlTfms>(this.HttpContext);
    }
}