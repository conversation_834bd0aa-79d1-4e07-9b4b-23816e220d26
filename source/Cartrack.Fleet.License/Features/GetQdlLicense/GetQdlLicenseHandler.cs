﻿using Cartrack.AppHost.Channels;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.License.Domain;
using Cartrack.Fleet.License.IO.Http;
using Cartrack.Fleet.License.IO.Sql;
using Cartrack.Fleet.License.Worker;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.License.Features.GetQdlLicense;

public class GetQdlLicenseHandler(AppSettings appSettings, IChannel channel, IElitesApiClient elitesClient, IClientDriverRepository driverRepo, ILogger<GetQdlLicenseHandler> logger)
    : IRequestHandler<GetQdlLicenseRequest, GetQdlLicenseResponse> {
    public async Task<GetQdlLicenseResponse> Handle(GetQdlLicenseRequest request, CancellationToken cancellationToken) {
        var driver = await driverRepo.GetDriverByClientDriverId(request.ClientDriverId);
        if (driver is null) {
            return new GetQdlLicenseResponse(null, NotFoundException.Create($"Driver with id {request.ClientDriverId} not found")) { IsServerError = false };
        }
        
        var nric = AesEncryption.Decrypt(driver.IdNumber!, appSettings.TfmsEncryptionKey, appSettings.TfmsEncryptionIv);
        var qdl = await elitesClient.GetQdlLicense(ElitesEnquiryRequest.Create(nric), cancellationToken);
        if (qdl is null) {
            return new GetQdlLicenseResponse(null);
        }
        
        var ch = channel.GetChannel<ApiQdlEnquiryResponse>(cancellationToken);
        await ch.Write(new ApiQdlEnquiryResponse(request.ClientDriverId, qdl));
        
        var resp = ElitesQdlTfms.From(qdl.ResponseBody.EnquiryResponse);
        var response = new GetQdlLicenseResponse(resp);
        return response;
    }
}