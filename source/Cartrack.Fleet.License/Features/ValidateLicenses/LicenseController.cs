﻿using Cartrack.Fleet.License.Features.ValidateLicenses;
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.License.Domain;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.License.IO.Http;

public partial class LicenseController {
    
    [HttpPost]
    [Route("validate")]
    public async Task<ActionResult<ValidateLicensesResponse>> ValidateLicenses([FromBody] ValidateLicensesRequest req) {
        var resp = await mediator.Send(req);
        return  resp.ToContentResult<ValidateLicensesResponse, ValidateCheck>(this.HttpContext);
    }
}