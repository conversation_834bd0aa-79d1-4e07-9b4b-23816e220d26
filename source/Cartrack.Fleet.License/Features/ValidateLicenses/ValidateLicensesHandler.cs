﻿using Cartrack.AppHost;
using Cartrack.AppHost.Channels;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.License.Domain;
using Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;
using Cartrack.Fleet.License.IO.Http;
using Cartrack.Fleet.License.IO.Sql;
using Cartrack.Fleet.License.Worker;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.License.Features.ValidateLicenses;

public class ValidateLicensesHandler(IHttpContextAccessor context, ILogger<ValidateLicensesHandler> logger)
    : IRequestHandler<ValidateLicensesRequest, ValidateLicensesResponse> {
    
    private readonly IVehicleLicenseValidation[] _validators = [
        new Class3VehicleValidation(),
        new Class4VehicleValidation(),
        new Class2VehicleValidation(),
        new Class2AVehicleValidation(),
        new ClassEv2VehicleValidation(),
        new ClassEv2AVehicleValidation(),
        new ClassEv4FVehicleValidation(),
        new ClassEvAVehicleValidation(),
        new Class5VehicleValidation()
    ];
    
    public async Task<ValidateLicensesResponse> Handle(ValidateLicensesRequest request, CancellationToken cancellationToken) {
        try {
             
            logger.LogInformation("[{TraceId}] Validating vehicle and driver licenses. Registration:{Registration}, DriverId: {DriverId}", 
                request.VehicleRegistration,
                request.DriverId,
                context.HttpContext?.TraceIdentifier ?? "");
            logger.LogInformation("[{TraceId}] Comparing \nDriverLicenses:{DriverLicenses}\nVehicleLicenses:{VehicleLicenses}", 
                context.HttpContext?.TraceIdentifier ?? "",
                string.Join(",", request.DriverNormalLicenses.Concat(request.DriverSpecialLicenses)),
                string.Join(",", request.VehicleNormalLicenses.Concat(request.VehicleSpecialLicenses))
                );

            var (isValid, err) = Validate(request);
            if (!isValid) {
                logger.LogWarning("[{TraceId}] Vehicle and driver licenses do not match. Error is: {Error}", context.HttpContext?.TraceIdentifier ?? "", err);
            }
            
            var response = new ValidateLicensesResponse(new ValidateCheck(isValid, err));
            return response;
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error validating vehicle and driver licenses", context.HttpContext?.TraceIdentifier ?? "");
            return new ValidateLicensesResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error validating vehicle and driver licenses", context.HttpContext?.TraceIdentifier ?? "");
            return new ValidateLicensesResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private (bool, string) Validate(ValidateLicensesRequest request) {
        foreach (var validator in this._validators) {
            if (validator.CanHandle(request.VehicleNormalLicenses, request.VehicleSpecialLicenses)) {
                return validator.Validate(request.DriverNormalLicenses, request.DriverSpecialLicenses);
            }
        }
        
        return (false, "Unexpected error. License validation failed.");
    }
}