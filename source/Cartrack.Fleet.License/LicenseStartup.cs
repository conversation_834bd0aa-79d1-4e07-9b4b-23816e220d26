﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.License.IO.Http;
using Cartrack.Fleet.License.IO.Sql;
using Cartrack.Fleet.License.Worker;
using Microsoft.Extensions.DependencyInjection;

namespace Cartrack.Fleet.License;

public static class LicenseStartup {
    public static void Register(IServiceCollection builderServices, AppSettings appSetting) {
        //builderServices.AddScoped<IAuthRepository, AuthRepository>();
        builderServices.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<LicenseController>());
        builderServices.AddControllers().AddApplicationPart(typeof(LicenseController).Assembly);
        builderServices.AddScoped<IUserRepository, UserRepository>();
        builderServices.AddScoped<IClientDriverRepository, ClientDriverRepository>();
        builderServices.AddSingleton<IElitesApiClient, ElitesApiClient>();

        builderServices.AddHostedService<LicenseUpdateWorker>();
    }
}