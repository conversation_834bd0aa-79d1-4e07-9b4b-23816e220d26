﻿namespace Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;

public class ClassEvAVehicleValidation : IVehicleLicenseValidation {

    public bool CanHandle(string[] vehicleNormalLicenseNames, string[] vehicleSpecialLicenseNames) {
        var isClassEVAVehicle =
            vehicleNormalLicenseNames.Intersect(ScdfVehicleClasses.ClassEVA).Any() ||
            (vehicleSpecialLicenseNames.Contains(ScdfSpecialLicenses.RDL4) &&
             (
                 vehicleSpecialLicenseNames.Contains(ScdfSpecialLicenses.EV4) ||
                 vehicleSpecialLicenseNames.Contains(ScdfSpecialLicenses.EV4A) ||
                 vehicleSpecialLicenseNames.Contains(ScdfSpecialLicenses.EV3)
             )
            );

        return isClassEVAVehicle;
    }

    public (bool, string) Validate(string[] driverNormalLicenseNames, string[] driverSpecialLicenseNames) {
        var canDriveVehicle = 
            driverNormalLicenseNames.Intersect(ScdfVehicleClasses.ClassEVA).Any() ||
            (driverSpecialLicenseNames.Contains(ScdfSpecialLicenses.RDL4) &&
             (
                 driverSpecialLicenseNames.Contains(ScdfSpecialLicenses.EV4) || 
                 driverSpecialLicenseNames.Contains(ScdfSpecialLicenses.EV4A) ||
                 driverSpecialLicenseNames.Contains(ScdfSpecialLicenses.EV3)
             )
            );
            
        return (canDriveVehicle, canDriveVehicle ? string.Empty : $"Validation failed for class EVA vehicle. Driver must have a class 3, 3A, 3C or 3CA or (RDL4 + EV4) or (RDL4 + EV4A) or (RDL4 + EV3) license.");
    }
}