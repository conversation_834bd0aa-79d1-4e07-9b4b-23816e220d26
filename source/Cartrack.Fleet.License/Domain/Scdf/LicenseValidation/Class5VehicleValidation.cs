﻿namespace Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;

public class Class5VehicleValidation : IVehicleLicenseValidation {

    public bool CanHandle(string[] vehicleNormalLicenseNames, string[] vehicleSpecialLicenseNames) {
        var isClass5Vehicle =
            vehicleNormalLicenseNames.Intersect(ScdfVehicleClasses.Class5).Any() ||
            (
                vehicleSpecialLicenseNames.Contains(ScdfSpecialLicenses.RDL5) && 
                vehicleSpecialLicenseNames.Contains(ScdfSpecialLicenses.EV5) 
            );

        return isClass5Vehicle;
    }

    public (bool, string) Validate(string[] driverNormalLicenseNames, string[] driverSpecialLicenseNames) {
        var canDriveVehicle = 
            driverNormalLicenseNames.Intersect(ScdfVehicleClasses.Class5).Any() ||
            (
                driverSpecialLicenseNames.Contains(ScdfSpecialLicenses.RDL5) && 
                driverSpecialLicenseNames.Contains(ScdfSpecialLicenses.EV5)
            );
            
        return (canDriveVehicle, canDriveVehicle ? string.Empty : "Validation failed for class 5 vehicle. Driver must have a class 5 or (RDL5 + EV5) license.");
    }
}