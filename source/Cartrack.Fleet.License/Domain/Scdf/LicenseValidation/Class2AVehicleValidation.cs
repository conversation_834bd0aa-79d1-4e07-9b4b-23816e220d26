﻿namespace Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;

public class Class2AVehicleValidation : IVehicleLicenseValidation {
    public bool CanHandle(string[] vehicleNormalLicenseNames, string[] vehicleSpecialLicenseNames) {
        var isClass2AVehicle = vehicleNormalLicenseNames.Intersect(ScdfVehicleClasses.Class2A).Any() && vehicleSpecialLicenseNames.Length == 0;
        return isClass2AVehicle;
    }

    public (bool, string) Validate(string[] driverNormalLicenseNames, string[] driverSpecialLicenseNames) {
        var canDriveVehicle = driverNormalLicenseNames.Intersect(ScdfVehicleClasses.Class2A).Any();
        return (canDriveVehicle, canDriveVehicle ? string.Empty : $"Validation failed for class 2A vehicle. Driver does not have a class 2 or 2A license.");
    }
}