﻿namespace Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;

public class ClassEv4FVehicleValidation : IVehicleLicenseValidation {

    public bool CanHandle(string[] vehicleNormalLicenseNames, string[] vehicleSpecialLicenseNames) {
        var isClassEV4FVehicle =
            vehicleNormalLicenseNames.Intersect(ScdfVehicleClasses.ClassEV4F).Any() ||
            (vehicleSpecialLicenseNames.Contains(ScdfSpecialLicenses.RDL4) &&
             (
                 vehicleSpecialLicenseNames.Contains(ScdfSpecialLicenses.EV4F) ||
                 vehicleSpecialLicenseNames.Contains(ScdfSpecialLicenses.EV4FM)
             )
            );

        return isClassEV4FVehicle;
    }

    public (bool, string) Validate(string[] driverNormalLicenseNames, string[] driverSpecialLicenseNames) {
        var canDriveVehicle = 
            driverNormalLicenseNames.Intersect(ScdfVehicleClasses.ClassEV4F).Any() ||
            (driverSpecialLicenseNames.Contains(ScdfSpecialLicenses.RDL4) &&
             (
                 driverSpecialLicenseNames.Contains(ScdfSpecialLicenses.EV4F) || 
                 driverSpecialLicenseNames.Contains(ScdfSpecialLicenses.EV4FM)
             )
            );
            
        return (canDriveVehicle, canDriveVehicle ? string.Empty : $"Validation failed for class EV4F vehicle. Driver must have a class 4 or 4A or (RDL4 + EV4) or (RDL4 + EV4FM) license.");
    }
}