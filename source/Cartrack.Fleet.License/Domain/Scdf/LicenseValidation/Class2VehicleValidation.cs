﻿namespace Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;

public class Class2VehicleValidation : IVehicleLicenseValidation {
    public bool CanHandle(string[] vehicleNormalLicenseNames, string[] vehicleSpecialLicenseNames) {
        var isClass2Vehicle = vehicleNormalLicenseNames.Intersect(ScdfVehicleClasses.Class2).Any() && vehicleSpecialLicenseNames.Length == 0;;
        return isClass2Vehicle;
    }

    public (bool, string) Validate(string[] driverNormalLicenseNames, string[] driverSpecialLicenseNames) {
        var canDriveVehicle = driverNormalLicenseNames.Intersect(ScdfVehicleClasses.Class2).Any();
        return (canDriveVehicle, canDriveVehicle ? string.Empty : $"Validation failed for class 2 vehicle. Driver does not have a class 2 license.");
    }
}