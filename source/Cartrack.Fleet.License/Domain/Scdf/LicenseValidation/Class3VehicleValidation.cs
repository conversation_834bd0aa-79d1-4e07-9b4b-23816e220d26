﻿namespace Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;

public class Class3VehicleValidation : IVehicleLicenseValidation {
   
    public bool CanHandle(string[] vehicleNormalLicenseNames, string[] vehicleSpecialLicenseNames) {
        return vehicleNormalLicenseNames.Intersect(ScdfVehicleClasses.Class3).Any() && vehicleSpecialLicenseNames.Length == 0;;
    }

    public (bool, string) Validate(string[] driverNormalLicenseNames, string[] driverSpecialLicenseNames) {
        var canDriveVehicle = driverNormalLicenseNames.Intersect(ScdfVehicleClasses.Class3).Any();
        return (canDriveVehicle, canDriveVehicle ? string.Empty : $"Validation failed for class 3 vehicle. Driver must have a class 3, 3A, 3C or 3CA license.");
    }
}