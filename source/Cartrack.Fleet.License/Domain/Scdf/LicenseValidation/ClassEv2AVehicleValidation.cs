﻿namespace Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;

public class ClassEv2AVehicleValidation : IVehicleLicenseValidation {

    public bool CanHandle(string[] vehicleNormalLicenseNames, string[] vehicleSpecialLicenseNames) {
        var isClassEV2AVehicle = vehicleNormalLicenseNames.Intersect(ScdfVehicleClasses.ClassEV2A).Any() &&
                                 vehicleSpecialLicenseNames.Intersect(ScdfVehicleClasses.ClassEV2A).Any();
        return isClassEV2AVehicle;
    }

    public (bool, string) Validate(string[] driverNormalLicenseNames, string[] driverSpecialLicenseNames) {
        var canDriveVehicle = 
            driverNormalLicenseNames.Intersect(ScdfVehicleClasses.ClassEV2A).Any() &&
            driverSpecialLicenseNames.Intersect(ScdfVehicleClasses.ClassEV2A).Any();
            
        return (canDriveVehicle, canDriveVehicle ? string.Empty : $"Validation failed for class EV2A vehicle. Driver must have a class 2A and EV2A licenses.");
    }
}