﻿namespace Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;

public static class ScdfVehicleClasses{

    public static readonly string[] Class3 =  ["3", "3A", "3C", "3CA"];
    public static readonly string[] Class4 = ["4", "4A", ScdfSpecialLicenses.RDL4];
    public static readonly string[] Class2 = ["2"];
    public static readonly string[] Class2A = ["2", "2A"];
    public static string[] Class5 = ["5", ScdfSpecialLicenses.RDL5, ScdfSpecialLicenses.EV5];
    
    public static readonly string[] ClassEV2 = ["2", ScdfSpecialLicenses.EV2];
    public static readonly string[] ClassEV2A = ["2A", ScdfSpecialLicenses.EV2A];
    public static readonly string[] ClassEV4F = ["4", "4A", ScdfSpecialLicenses.RDL4, ScdfSpecialLicenses.EV4F, ScdfSpecialLicenses.EV4FM];
    public static readonly string[] ClassEVA = ["3", "3A", "3C", "3CA", ScdfSpecialLicenses.RDL4, ScdfSpecialLicenses.EV4, ScdfSpecialLicenses.EV4A, ScdfSpecialLicenses.EV3];
}