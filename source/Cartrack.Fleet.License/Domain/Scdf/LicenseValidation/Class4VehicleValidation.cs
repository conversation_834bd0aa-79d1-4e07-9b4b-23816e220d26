﻿namespace Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;

public class Class4VehicleValidation : IVehicleLicenseValidation {
    public bool CanHandle(string[] vehicleNormalLicenseNames, string[] vehicleSpecialLicenseNames) {
        var isClass4Vehicle = vehicleNormalLicenseNames.Intersect(ScdfVehicleClasses.Class4).Any() ||
                              vehicleSpecialLicenseNames.Intersect(ScdfVehicleClasses.Class4).Any();
        return isClass4Vehicle;
    }

    public (bool, string) Validate(string[] driverNormalLicenseNames, string[] driverSpecialLicenseNames) {
        var canDriveVehicle = 
            driverNormalLicenseNames.Intersect(ScdfVehicleClasses.Class4).Any() ||
            driverSpecialLicenseNames.Intersect(ScdfVehicleClasses.Class4).Any();
            
        return (canDriveVehicle, canDriveVehicle ? string.Empty : $"Validation failed for class 4 vehicle. Driver must have a class 4, 4A or RDL4 license.");
    }
}