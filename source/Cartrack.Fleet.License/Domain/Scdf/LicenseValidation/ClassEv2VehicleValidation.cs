﻿namespace Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;

public class ClassEv2VehicleValidation : IVehicleLicenseValidation {

    public bool CanHandle(string[] vehicleNormalLicenseNames, string[] vehicleSpecialLicenseNames) {
        var isClassEV2Vehicle = vehicleNormalLicenseNames.Intersect(ScdfVehicleClasses.Class2).Any() &&
                                vehicleSpecialLicenseNames.Intersect(ScdfVehicleClasses.ClassEV2).Any();
        return isClassEV2Vehicle;
    }

    public (bool, string) Validate(string[] driverNormalLicenseNames, string[] driverSpecialLicenseNames) {
        var canDriveVehicle = 
            driverNormalLicenseNames.Intersect(ScdfVehicleClasses.Class2).Any() &&
            driverSpecialLicenseNames.Intersect(ScdfVehicleClasses.ClassEV2).Any();
            
        return (canDriveVehicle, canDriveVehicle ? string.Empty : $"Validation failed for class EV2 vehicle. Driver must have a class 2 and EV2 licenses.");
    }
}