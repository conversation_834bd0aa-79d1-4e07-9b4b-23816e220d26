﻿using Cartrack.Fleet.License.IO.Http;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.License.Domain;

public class ElitesQdlTfms {
    public string IdNo { get; set; } = string.Empty;

    public int? TotalDemeritPts { get; set; }

    public QdlDetailsTfms QdlDetails { get; set; } = new();

    public PdlDetailsTfms PdlDetails { get; set; } = new();

    public SuspensionTfms Suspension { get; set; } = new();

    public DisqualificationTfms Disqualification { get; set; } = new();

    public RevocationTfms Revocation { get; set; } = new();

    public static ElitesQdlTfms? From(ElitesQdl? qdl) {
        if (qdl == null)
            return null;

        var tfms = new ElitesQdlTfms {
            IdNo = qdl.IdNo,
            TotalDemeritPts = string.IsNullOrWhiteSpace(qdl.TotalDemeritPts) ? 0 : int.Parse(qdl.TotalDemeritPts),
            QdlDetails = new QdlDetailsTfms() { QdlExpiryDateUtc = Utils.ParseDate(qdl.QdlDetails.QdlExpiryDate), QdlValidity = qdl.QdlDetails.QdlValidity },
            PdlDetails = new PdlDetailsTfms() { PdlExpiryDateUtc = Utils.ParseDate(qdl.PdlDetails.PdlExpiryDate), PdlValidity = qdl.PdlDetails.PdlValidity },
            Suspension = new SuspensionTfms() { SuspensionEndDateUtc = Utils.ParseDate(qdl.Suspension.SuspEndDate), SuspensionStartDateUtc = Utils.ParseDate(qdl.Suspension.SuspStartDate) },
            Disqualification = new DisqualificationTfms() {
                DisqualificationEndDateUtc = Utils.ParseDate(qdl.Disqualification.DisqEndDate), DisqualificationStartDateUtc = Utils.ParseDate(qdl.Disqualification.DisqStartDate)
            },
            Revocation = new RevocationTfms() { RevocationEndDateUtc = Utils.ParseDate(qdl.Revocation.RevEndDate), RevocationStartDateUtc = Utils.ParseDate(qdl.Revocation.RevStartDate) }
        };

        foreach (var q in qdl.QdlDetails.QdlClasses) {
            tfms.QdlDetails.QdlClasses.Add(new QdlClassItemTfms() { IssueDateUtc = Utils.ParseDate(q.IssueDate), LicenseClass = q.Class });
        }

        foreach (var p in qdl.PdlDetails.PdlClasses) {
            tfms.PdlDetails.PdlClasses.Add(new QdlClassItemTfms() { IssueDateUtc = Utils.ParseDate(p.IssueDate), LicenseClass = p.Class });
        }

        return tfms;
    }
}