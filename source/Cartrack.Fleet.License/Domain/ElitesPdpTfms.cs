﻿using Cartrack.Fleet.License.IO.Http;

namespace Cartrack.Fleet.License.Domain;

public class ElitesPdpTfms {
    public string IdNo { get; set; } = string.Empty;

    public PdpDetailsTfms PdpDetails { get; set; } = new();

    public static ElitesPdpTfms? From(ElitesPdp? pdp) {
        if (pdp == null)
            return null;
        
        var tfms = new ElitesPdpTfms() {
            IdNo = pdp.IdNo,
            PdpDetails = new PdpDetailsTfms() {
                Validity = pdp.PdpDetails.Validity,
                PermitNo = pdp.PdpDetails.PermitNo,
                PdpStatus = pdp.PdpDetails.PdpStatus,
                IssueDateUtc = Utils.ParseDate(pdp.PdpDetails.IssueDate),
                JoinDateUtc = Utils.ParseDate(pdp.PdpDetails.JoinDate),
                
            }
        };

        foreach (var p in pdp.PdpDetails.PdpClasses) {
            tfms.PdpDetails.PdpClasses.Add(new PdpClassItemTfms() {
                IssueDateUtc = Utils.ParseDate( p.IssueDate),
                LicenseClass = p.Class
            });
        }
        
        return tfms;
    }
}