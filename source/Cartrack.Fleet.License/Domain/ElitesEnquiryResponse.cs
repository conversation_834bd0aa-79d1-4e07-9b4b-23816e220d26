﻿using Cartrack.Fleet.License.IO.Http;

namespace Cartrack.Fleet.License.Domain;

public class ElitesEnquiryResponse {
    private readonly ElitesQdl qdlResponse;
    private readonly ElitesPdp pdpResponse;
    private readonly string driverId;

    public ElitesQdl QdlResponse => this.qdlResponse;

    public ElitesPdp PdpResponse => this.pdpResponse;

    public string ClientDriverId => this.driverId;
}