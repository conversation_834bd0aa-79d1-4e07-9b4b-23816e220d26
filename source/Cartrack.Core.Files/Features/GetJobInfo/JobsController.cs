﻿using Cartrack.Core.Files.Features.GetJobInfo;
using Cartrack.Core.Files.IO.Http.DataContracts;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Core.Files.IO.Http;
public partial class JobsController {

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult<GetJobInfoResponse>> Get(long id) {
        var response = await this._mediator.Send(new GetJobInfoRequest(id));
        return response.ToContentResult<GetJobInfoResponse, JobInfo>(this.HttpContext, response.Value);
    }
}
