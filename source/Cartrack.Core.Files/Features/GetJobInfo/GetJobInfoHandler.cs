﻿using Cartrack.AppHost.ServiceContracts;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.IO.Http.DataContracts;
using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.EntityFrameworkCore.Storage.Json;
using Constants = Cartrack.Core.Files.Domain.Constants;

namespace Cartrack.Core.Files.Features.GetJobInfo;


public class GetJobInfoHandler(
    AppSettings settings,
    IServiceContractDiscovery serviceContract,
    IHttpContextAccessor context,
    ILogger<GetJobInfoHandler> logger) : IRequestHandler<GetJobInfoRequest, GetJobInfoResponse> {
    public async Task<GetJobInfoResponse> Handle(GetJobInfoRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            var user = AuthClaims.From(context.HttpContext!.User);
            var storageSettings = settings.GetStorageSettings(user!);
            
            var jobWorker = serviceContract.Discover<IJobWorker>()!;
            var job = await jobWorker.GetJobInfo(request.JobId);
            if (job.Status == Constants.Completed && job.StatusDetails.StartsWith(storageSettings.RootPath)) {
                job.StatusDetails = UrlPathHelper.ToFileServiceUrl(settings.FsServerBaseUrl, storageSettings, job.StatusDetails);
            }
            
            return new GetJobInfoResponse(new JobInfo(job.Id, job.Status, job.CompletionLevel, job.JobDesc, job.StatusDetails, ""));
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} File copy failed", trace);
            return new GetJobInfoResponse(null, ex);
        }
    }
}