﻿using Cartrack.Fleet.Common;
using DataContracts_JobInfo = Cartrack.Core.Files.IO.Http.DataContracts.JobInfo;
using Http_DataContracts_JobInfo = Cartrack.Core.Files.IO.Http.DataContracts.JobInfo;
using IO_Http_DataContracts_JobInfo = Cartrack.Core.Files.IO.Http.DataContracts.JobInfo;
using JobInfo = Cartrack.Core.Files.IO.Http.DataContracts.JobInfo;

namespace Cartrack.Core.Files.Features.GetJobInfo;

public record GetJobInfoResponse(IO_Http_DataContracts_JobInfo? Value, Exception? Error = null) : BaseResponse<IO_Http_DataContracts_JobInfo>(Value, Error) {
}