﻿using Cartrack.AppHost;
using Cartrack.AppHost.ServiceContracts;
using Cartrack.Core.Files.Features.FileDownload;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Vehicle.IO.Http.Filters;
using MediatR;
using System.IO.Compression;

namespace Cartrack.Core.Files.Features.FileDownloadBulk;



public class FileDownloadBulkHandler(
    AppSettings settings,
    IStorageProviderFactory providerFactory,
    IServiceContractDiscovery serviceContract,
    IHttpContextAccessor context,
    ILogger<FileDownloadBulkHandler> logger) : IRequestHandler<FileDownloadBulkRequest, FileDownloadBulkResponse> {
    public async Task<FileDownloadBulkResponse> Handle(FileDownloadBulkRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            Requires.NotEmpty(request.Files, nameof(request.Files));
            
            var user = context.GetPermissionsContext()!.Claims;
            var storage = providerFactory.Create(settings.GetStorageSettings(user));
            var zippy = Path.Combine(Path.GetTempPath(), $"FS_{Guid.NewGuid()}.zip");
            await using var zipStream = File.OpenWrite(zippy);
            using (var zip = new ZipArchive(zipStream, ZipArchiveMode.Create, true)) {
                foreach (var url in request.Files) {
                    if (string.IsNullOrWhiteSpace(url)) 
                        continue;
                    
                    logger.LogInformation("[{Trace}] Downloading file {Url}", trace, url);
                    var fileName = url.Split('/').Last();
                    var stream = await storage.GetFileAsStream(url);
                    if (stream is not null) {
                        var entry = zip.CreateEntry(fileName);
                        logger.LogInformation("[{Trace}] Adding zip entry file {Name}", trace, fileName);
                        await using var entryStream = entry.Open();
                        await stream.CopyToAsync(entryStream, token);
                    }
                }
            }

            return new FileDownloadBulkResponse(zippy);
        }
        catch (RequiresException c) {
            logger.LogError(c, "[{Trace} invalid request", trace);
            return new FileDownloadBulkResponse(null, c) { IsServerError = false};
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} File bulk download failed", trace);
            return new FileDownloadBulkResponse(null, ex) {IsServerError = true};
        }
    }
}
 