﻿using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Features.FileDownloadBulk;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;

// ReSharper disable once CheckNamespace
namespace Cartrack.Core.Files.IO.Http;
public partial class FilesController : ControllerBase {
    [HttpPost]
    [Route("download-many")]
    public async Task<IActionResult> FileDownloadBulk([FromBody] FileDownloadBulkRequest request) {

        string tempZip = "";
        try {
            var response = await this._mediator.Send(request);
            if (!response.IsOk) {
                return this.NotFound("Files not downloaded. Please see the error log for more information.");
            }
            tempZip = response.Value!;
            var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
            var zipFileName = $"files_{timestamp}.zip";
            

            var zipBytes = await System.IO.File.ReadAllBytesAsync(response.Value!);
            return File(new MemoryStream(zipBytes), "application/zip", zipFileName);
        }
        catch (Exception ex) {
            return this.StatusCode(StatusCodes.Status500InternalServerError, ex.ToString());
        }
        finally {
            if (System.IO.File.Exists(tempZip))
                System.IO.File.Delete(tempZip);
        }
    }
}