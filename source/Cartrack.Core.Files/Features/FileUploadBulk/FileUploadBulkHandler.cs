﻿using Cartrack.AppHost;
using Cartrack.AppHost.ServiceContracts;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Features.FileUpload;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Http;
using Cartrack.Core.Files.IO.Http.DataContracts;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;
using System.Text.Json;

namespace Cartrack.Core.Files.Features.FileUploadBulk;


public class FileUploadBulkHandler(
    IStorageProviderFactory providerFactory,
    IServiceContractDiscovery serviceContract,
    IHttpContextAccessor context,
    AppSettings settings,
    IMediator mediator,
    ILogger<FileUploadBulkHandler> logger) : IRequestHandler<FileUploadBulkRequest, FileUploadBulkResponse> {
    public async Task<FileUploadBulkResponse> Handle(FileUploadBulkRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            Requires.NotNullOrEmpty(request.Metadata, nameof(request.Metadata));

            var metadata = JsonSerializer.Deserialize<UploadManyMetadata>(request.Metadata);
            Requires.IsTrue(() => metadata != null, () => "Invalid metadata");
            Requires.IsTrue(() => metadata!.UploadRequests.Count > 0 , () => "Invalid metadata. No upload requests");
            Requires.IsTrue(() => request.Files.Count > 0 , () => "No files to upload");
            Requires.IsTrue(() => metadata!.UploadRequests.Count == request.Files.Count , () => "Metadata requests count does not match files count");

            var uploadRequests = metadata!.UploadRequests.Select(x => {
                var formFile = request.Files.FirstOrDefault(t => t.FileName == x.FileName);
                Requires.IsTrue(() => formFile != null, () => $"File {x.FileName} not found in request");
                return new FileUploadRequest(formFile!, x.TargetPath, x.Overwrite);
            });
            
            List<FileUploadStatus> fileUploadStatus = new();
            foreach (var uploadRequest in uploadRequests) {
                var resp = await mediator.Send(uploadRequest, token);
                if (resp.IsOk) {
                    var job = resp.Value!.Job;
                    var uploadInfo = new FileUploadInfo(uploadRequest.Value.FileName, uploadRequest.Value.Length, uploadRequest.TargetFilePath, uploadRequest.Overwrite);
                    fileUploadStatus.Add(new FileUploadStatus(uploadInfo, job));
                    continue;
                }
                
                Requires.IsTrue(() => resp.IsOk, $"Failed to upload file {uploadRequest.Value.FileName}", _ => resp.Error! );
            }

            return new FileUploadBulkResponse(fileUploadStatus);
        }
        catch (RequiresException c) {
            logger.LogError(c, "[{Trace} invalid ", trace);
            return new FileUploadBulkResponse(null, c) { IsServerError = false };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} Bulk File upload failed", trace);
            return new FileUploadBulkResponse(null, ex) { IsServerError = true };
        }
    }
}