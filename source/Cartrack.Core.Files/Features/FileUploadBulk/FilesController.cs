﻿using Cartrack.Core.Files.Features.FileUpload;
using Cartrack.Core.Files.Features.FileUploadBulk;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;

// ReSharper disable once CheckNamespace
namespace Cartrack.Core.Files.IO.Http;

public record UploadRequest(string FileName, string TargetPath, bool Overwrite);

public record UploadManyMetadata(List<UploadRequest> UploadRequests);
public partial class FilesController : ControllerBase {
    [HttpPost]
    [Route("upload-many")]
    public async Task<ActionResult<FileUploadBulkResponse>> FileUploadBulk(
        [FromForm] string metadata,
        [FromForm] IFormFileCollection files) {
        var response = await this._mediator.Send(new FileUploadBulkRequest(metadata, files));
        return response.ToContentResult(this.HttpContext, response.Value);
    }
}