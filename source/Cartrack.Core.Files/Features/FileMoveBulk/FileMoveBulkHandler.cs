﻿using Cartrack.AppHost;
using Cartrack.AppHost.ServiceContracts;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Features.FileMove;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Http.DataContracts;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;

namespace Cartrack.Core.Files.Features.FileMoveBulk;

public class FileMoveBulkHandler(
    IStorageProviderFactory providerFactory,
    IServiceContractDiscovery serviceContract,
    IHttpContextAccessor context,
    AppSettings settings,
    ILogger<FileMoveBulkHandler> logger) : IRequestHandler<FileMoveBulkRequest, FileMoveBulkResponse> {
    public async Task<FileMoveBulkResponse> Handle(FileMoveBulkRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            List<FileMoveStatus> fileMoveStatus = new();

            foreach (var req in request.MoveBulkRequest) {
                Requires.IsTrue(() => !string.IsNullOrWhiteSpace(req.SourcePath), () => $"{nameof(req.SourcePath)} is required");
                Requires.IsTrue(() => !string.IsNullOrWhiteSpace(req.DestinationPath), () => $"{nameof(req.DestinationPath)} is required");

                var moveJob = new FileMoveJob(req, providerFactory, context, settings, logger, token);
                var worker = serviceContract.Discover<IJobWorker>()!;
                await worker.Submit(moveJob, context.HttpContext?.TraceIdentifier);

                var job = new JobInfo(moveJob.Id, moveJob.Status, moveJob.CompletionLevel, moveJob.Description, string.Empty, string.Empty);
                fileMoveStatus.Add(new FileMoveStatus(req, job));
            }

            return new FileMoveBulkResponse(fileMoveStatus);
        }
        catch (RequiresException c) {
            logger.LogError(c, "[{Trace} invalid ", trace);
            return new FileMoveBulkResponse(null, c) { IsServerError = false };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} Bulk File move failed", trace);
            return new FileMoveBulkResponse(null, ex){IsServerError = true};
        }
    }
}
