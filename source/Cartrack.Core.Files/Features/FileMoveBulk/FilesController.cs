﻿using Cartrack.Core.Files.Features.FileMoveBulk;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Core.Files.IO.Http;
public partial class FilesController : ControllerBase {
    [HttpPut]
    [Route("moveBulk")]
    public async Task<ActionResult<FileMoveBulkResponse>> FileMoveBulk([FromBody] FileMoveBulkRequest request) {
        var response = await this._mediator.Send(request);
        return response.ToContentResult(this.HttpContext, response.Value);
    }
}