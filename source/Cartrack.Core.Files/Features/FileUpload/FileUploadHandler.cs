﻿using Cartrack.AppHost;
using Cartrack.AppHost.Channels;
using Cartrack.AppHost.ServiceContracts;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Http.DataContracts;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;

namespace Cartrack.Core.Files.Features.FileUpload;

public class FileUploadHandler(
    IChannel channel,
    IStorageProviderFactory providerFactory,
    IServiceContractDiscovery serviceContract,
    IHttpContextAccessor context,
    AppSettings settings,
    ILogger<FileUploadHandler> logger) : IRequestHandler<FileUploadRequest, FileUploadResponse> {
    
    public async Task<FileUploadResponse> Handle(FileUploadRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            
            Requires.IsTrue(() => request.Value.Length > 0, () => "File cannot be empty");
            Requires.IsTrue(() => request.Value.Length < settings.FsMaxFileSizeBytes, () => $"File is too large. Max size is {settings.FsMaxFileSizeBytes / (1024 * 1024)} MB");
            Requires.IsTrue(
                () => Path.GetExtension(request.Value.FileName) == Path.GetExtension(request.TargetFilePath),
                () => "Source and target file extensions do not match");
            Requires.NotNullOrEmpty(request.TargetFilePath, nameof(request.TargetFilePath));


            var uploadRequest = request with { TargetFilePath = GetTargetPath(request.TargetFilePath) }; 
            var uploadJob = new FileUploadJob(uploadRequest, providerFactory, context, settings, logger, token);
            var worker = serviceContract.Discover<IJobWorker>()!;
            var jobId = await worker.Submit(uploadJob, context.HttpContext?.TraceIdentifier ?? "");
            var link = $"{settings.FsServerBaseUrl}/job/{uploadJob.Id}";
            var job = new JobInfo(jobId, uploadJob.Status, uploadJob.CompletionLevel, uploadJob.Description, "", link);
            var uploadInfo = new FileUploadInfo(uploadRequest.Value.FileName, uploadRequest.Value.Length, uploadRequest.TargetFilePath, uploadRequest.Overwrite);
            return new FileUploadResponse(new FileUploadStatus(uploadInfo, job));

            static string GetTargetPath(string reqPath) {
                if (reqPath.StartsWith("https://") || reqPath.StartsWith("http://")) {
                    var uri = new Uri(reqPath);
                    return string.Join("/", uri.Segments.Skip(2));
                }

                return reqPath.TrimStart('/');
            }
        }
        catch (RequiresException c) {
            logger.LogError(c, "[{Trace} invalid request", trace);
            return new FileUploadResponse(null, c) { IsServerError = false };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} File upload failed", trace);
            return new FileUploadResponse(null, ex) { IsServerError = true };
        }
    }
}