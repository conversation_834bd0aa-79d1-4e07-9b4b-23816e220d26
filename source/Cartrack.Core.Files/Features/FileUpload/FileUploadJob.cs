﻿using Cartrack.AppHost;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.Infrastructure.Providers;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Core.Files.IO.Sql.Models;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Common;
using Constants = Cartrack.Core.Files.Domain.Constants;

namespace Cartrack.Core.Files.Features.FileUpload;

public class FileUploadJob(
    FileUploadRequest request,
    IStorageProviderFactory factory,
    IHttpContextAccessor context,
    AppSettings settings,
    ILogger logger,
    CancellationToken token)
    : BaseJob(factory, context, settings, logger, token) {
    private readonly IStorageProviderFactory _factory = factory;
    private readonly AppSettings _settings = settings;
    private readonly CancellationToken _token = token;
    private InputStreamWrapper? _input;
    private readonly IFileRepository _fileRepo = new FileRepository(settings);
    public override string Description { get; } = $"Uploading {request.Value.FileName} to {request.TargetFilePath}";

    protected override async Task Initialize() {
        var formFile = request.Value;
        var totalBytes = formFile.Length;

        //if file is < 10MB buffer it to memory
        if (totalBytes < 10 * 1024 * 1024) {
            var ms = new MemoryStream();
            await formFile.CopyToAsync(ms, this._token);
            ms.Seek(0, SeekOrigin.Begin);
            this._input = new InputStreamWrapper(ms);
            return;
        }

        //If the file is big, write it to a temp file. Use the temp files as the input stream
        var ext = Path.GetExtension(formFile.FileName);
        var tempFile = Path.Combine(this.CurrentStorageSettings.GetTempPath(), Guid.NewGuid() + ext);
        tempFile = Path.GetFullPath(tempFile);

        var fs = new FileStream(tempFile, FileMode.Create, FileAccess.ReadWrite, FileShare.None);
        try {
            await formFile.CopyToAsync(fs, this._token);
            fs.Seek(0, SeekOrigin.Begin);
            this._input = new InputStreamWrapper(fs, tempFile);
        }
        catch {
            await fs.DisposeAsync();
            System.IO.File.Delete(tempFile);
            throw;
        }
    }

    protected override async Task Run(IStorageProvider storage) {
        Requires.NotNull(this._jobInfo, nameof(this._jobInfo));
        var bufferSize = this._settings.FsBufferSize;
        var status = await storage.Create(request.TargetFilePath, request.Overwrite, this.Write, bufferSize);
        Requires.IsTrue(() => status, () => $"{request.TargetFilePath}: File not uploaded.");

    }

    /// <summary>
    /// Stream processing with progress tracking
    /// </summary>
    /// <param name="targetStream"></param>
    private async Task Write(Stream targetStream) {
        Requires.NotNull(this._input, "Upload input stream");

        var formFile = request.Value;
        var totalBytes = formFile.Length;

        await using var input = this._input!;
        long totalBytesRead = 0;
        var bufferSize = this._settings.FsBufferSize;

        var buffer = new byte[bufferSize];
        int bytesRead;

        var previousProgress = 0;
        while ((bytesRead = await input.Stream.ReadAsync(buffer, this._token)) > 0) {
            await targetStream.WriteAsync(buffer.AsMemory(0, bytesRead), this._token);
            totalBytesRead += bytesRead;

            var progressPercentage = Convert.ToInt32(totalBytesRead * 1.0 / totalBytes * 100);
            this._jobInfo!.CompletionLevel = progressPercentage;
            var delta = progressPercentage - previousProgress;

            //Save progress updates to the DB only in > 10% increments to reduce DB usage
            if (delta >= 10) {
                logger.LogInformation("[{TraceIdentifier}] Job {JobId}: Uploaded {BytesRead} of {TotalBytes} bytes. {ProgressPercentage}% complete.",
                    this.TraceId, this._jobInfo.Id, totalBytesRead, totalBytes, progressPercentage);

                previousProgress = progressPercentage;
                _ = this.Update(Constants.InProgress, progressPercentage);
            }
        }
    }

    public override async Task Complete() {
        Requires.NotNull(this._jobInfo, nameof(FsJobInfo));

        using var provider = this._factory.Create(this.CurrentStorageSettings);
        var info = await provider.GetFileInfo(request.TargetFilePath);
        var fileId = await this._fileRepo.CreateOrUpdate(info);
        this._jobInfo!.StatusDetails = $"{this.CurrentStorageSettings.RootPath}/{info.FullPath}"; // info.FullPath;
        this._jobInfo.FileInfoId = fileId;
        await base.Complete();
        logger.LogInformation(
            "[{TraceId}] File uploaded successfully to {Destination}",
            this.TraceId,
            info.FullPath);//request.FilePath);
    }

    public override async Task Fail(Exception failureReason) {
        await base.Fail(failureReason);
        logger.LogError("[{TraceId}] File upload failed with {Exception}", this.TraceId, failureReason);
    }

    private class InputStreamWrapper(Stream source, string file = "") : IAsyncDisposable {
        public Stream Stream => source;

        public async ValueTask DisposeAsync() {
            await source.DisposeAsync();
            if (!string.IsNullOrWhiteSpace(file) && System.IO.File.Exists(file)) {
                System.IO.File.Delete(file);
            }
        }
    }
}