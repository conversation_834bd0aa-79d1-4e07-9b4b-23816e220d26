﻿using Cartrack.Core.Files.Features.FileUpload;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Core.Files.IO.Http;
public partial class FilesController {
    [HttpPost]
    [Route("upload")]
    public async Task<ActionResult<FileUploadResponse>> FileUpload([FromForm] UploadForm upload) {
        var response = await this._mediator.Send(new FileUploadRequest(upload.SourceFile, upload.TargetFile, upload.Overwrite));
        return response.ToContentResult(this.HttpContext, response.Value);
    }
}