﻿using Cartrack.AppHost;
using Cartrack.AppHost.ServiceContracts;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Features.FileDelete;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Http.DataContracts;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;

namespace Cartrack.Core.Files.Features.FileDeleteBulk;

public class FileDeleteBulkHandler(
    AppSettings settings,
    IStorageProviderFactory providerFactory,
    IServiceContractDiscovery serviceContract,
    IHttpContextAccessor context,
    ILogger<FileDeleteBulkHandler> logger) : IRequestHandler<FileDeleteBulkRequest, FileDeleteBulkResponse> {
    public async Task<FileDeleteBulkResponse> Handle(FileDeleteBulkRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            List<FileDeleteStatus> fileDeleteStatus = new();

            foreach (var req in request.fileDeleteBulkRequest) {
                Requires.IsTrue(() => !string.IsNullOrWhiteSpace(req.UriPath), () => $"{nameof(req.UriPath)} is required");

                var deleteJob = new FileDeleteJob(req, providerFactory, context, settings, logger, token);
                var worker = serviceContract.Discover<IJobWorker>()!;
                await worker.Submit(deleteJob, deleteJob.TraceId);

                var link = $"{settings.FsServerBaseUrl}/job/{deleteJob.Id}";
                var job = new JobInfo(deleteJob.Id, deleteJob.Status, deleteJob.CompletionLevel, deleteJob.Description, string.Empty, link);
                fileDeleteStatus.Add(new FileDeleteStatus(req, job));
            }

            return new FileDeleteBulkResponse(fileDeleteStatus);
        }
        catch (RequiresException c) {
            logger.LogError(c, "[{Trace} invalid request", trace);
            return new FileDeleteBulkResponse(null, c) { IsServerError = false};
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} Bulk File Delete failed", trace);
            return new FileDeleteBulkResponse(null, ex) {IsServerError = true};
        }
    }
}
