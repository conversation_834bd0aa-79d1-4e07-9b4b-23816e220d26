﻿ 
using Cartrack.Core.Files.Features.FileDeleteBulk;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Core.Files.IO.Http;
public partial class FilesController : ControllerBase {
    [HttpDelete]
    [Route("deleteBulk")]
    public async Task<ActionResult<FileDeleteBulkResponse>> FileDeleteBulk([FromBody] FileDeleteBulkRequest request) {
        var response = await this._mediator.Send(request);
        return response.ToContentResult(this.HttpContext, response.Value);
    }
}