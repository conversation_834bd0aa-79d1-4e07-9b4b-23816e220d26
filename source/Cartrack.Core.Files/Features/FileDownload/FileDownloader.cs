﻿using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Core.Files.Features.FileDownload;

public class FileDownloader(IServiceProvider service, AppSettings settings, HttpContext context) {
    public async Task<IActionResult> FileDownload(string uriPath) {
        var provider = new StorageProviderFactory(context);
        var logger = service.GetService<ILogger<FileDownloadHandler>>()!;
        var handler = new FileDownloadHandler(settings, provider, new LocalHttpContextAccessor(context), logger );
        var response = await handler.Handle(new FileDownloadRequest(uriPath, AuthClaims.From(context.User)), CancellationToken.None);

        if (!response.IsOk) {
            return new ObjectResult($"Failed to download file {uriPath}. {response.Error!}") { StatusCode = 500 };
        }

        if (response.Value is null) {
            return new NotFoundResult();
        }

        return new FileStreamResult(response.Value, response.ContentType);
    }
    
    private class LocalHttpContextAccessor(HttpContext? ctx) : IHttpContextAccessor {
        public HttpContext? HttpContext { get; set; } = ctx;
    }
}