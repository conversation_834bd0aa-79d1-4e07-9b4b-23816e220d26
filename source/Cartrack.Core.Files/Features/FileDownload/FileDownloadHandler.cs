﻿using Cartrack.AppHost;
using Cartrack.AppHost.ServiceContracts;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Features.FileDeleteBulk;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Vehicle.IO.Http.Filters;
using MediatR;

namespace Cartrack.Core.Files.Features.FileDownload;

public class FileDownloadHandler(
    AppSettings settings,
    IStorageProviderFactory providerFactory,
    IHttpContextAccessor context,
    ILogger<FileDownloadHandler> logger) : IRequestHandler<FileDownloadRequest, FileDownloadResponse> {
    public async Task<FileDownloadResponse> Handle(FileDownloadRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            Requires.IsTrue(() => !string.IsNullOrWhiteSpace(request.UriPath), () => $"{nameof(request.UriPath)} is required");
            logger.LogInformation("[{Trace} Downloading file {File}", trace, request.UriPath);
            
            var storageSettings = settings.GetStorageSettings(request.User);
            var provider = providerFactory.Create(storageSettings);
            var stream = await provider.GetFileAsStream(request.UriPath);

            return new FileDownloadResponse(stream, ContentTypeMapper.GetContentTypeFromPath(request.UriPath));
        }
        catch (RequiresException c) {
            logger.LogError(c, "[{Trace} invalid request", trace);
            return new FileDownloadResponse(null, "application/json", c) { IsServerError = false};
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} File download failed", trace);
            return new FileDownloadResponse(null, string.Empty, ex) {IsServerError = true};
        }
    }
}