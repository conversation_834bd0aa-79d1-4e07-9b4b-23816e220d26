﻿using Cartrack.Core.Files.Domain;
using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Constants = Cartrack.Fleet.Common.Constants;

namespace Cartrack.Core.Files.Features.FileDownload;

public class FileDownloadMiddleware {
    private readonly IMediator _mediator;
    private readonly AppSettings _settings;
    private readonly RequestDelegate _next;
    private readonly IServiceProvider _serviceProvider;

    public FileDownloadMiddleware(RequestDelegate next, IServiceProvider serviceProvider, IMediator mediator, AppSettings settings) {
        this._next = next;
        this._serviceProvider = serviceProvider;
        this._mediator = mediator;
        this._settings = settings;
    }

    public async Task InvokeAsync(HttpContext context) {
        if (context.Request.Method != "GET") {
            await this._next(context);
            return;
        }

        if (context.User.Identity is { IsAuthenticated: false }) {
            await this._next(context);
            return;
        }
        
        var path = context.Request.Path.Value?.TrimStart('/');
        if (string.IsNullOrEmpty(path)) {
            await this._next(context);
            return;
        }
  
        var segments = path.Split('/', StringSplitOptions.RemoveEmptyEntries);
        if (segments.Length < 2) {
            await this._next(context);
            return;
        }
        
        // setup user info in context   // data will be     // SomeUserID1|LOCAL|directoryPath
        string encryptedUrl;
        string requestedPath = context.Request.Path;

        var publicSegment = segments[0];
        if (publicSegment == _settings.FsPublicUrlSegment) {
            //This public link was generated to "share" a file 
            if (segments.Length >= 3) {
                encryptedUrl = segments[1];
                var decryptedSegmentData = PublicUrlEncryption.GetPublicUrlUserInfo(encryptedUrl, _settings);
                if (decryptedSegmentData == null) {
                    await this._next(context);
                    return;
                }

                var userInfo = decryptedSegmentData.Split('|');
                var directoryPath = userInfo[2];
                requestedPath = string.Concat(directoryPath, "/", segments[^1]);
                segments = directoryPath.Split('/', StringSplitOptions.RemoveEmptyEntries);
            }
        }
        
        AuthClaims currentUser = AuthClaims.From(context.User);
        var storageSettings = _settings.GetStorageSettings(currentUser!);
        if (segments[0] != storageSettings.BucketName) {
            await this._next(context);
            return;
        }

        try {
            var downloader = new FileDownloader(this._serviceProvider, this._settings, context);
            var uriBuilder = new UriBuilder(context.Request.Scheme, context.Request.Host.Host, context.Request.Host.Port ?? 80, requestedPath);
            var actionResult = await downloader.FileDownload(uriBuilder.ToString());
            if (actionResult is FileStreamResult fileStreamResult) {
                context.Response.ContentType = fileStreamResult.ContentType;
                if (!string.IsNullOrEmpty(fileStreamResult.FileDownloadName)) {
                    context.Response.Headers.Append(
                        "Content-Disposition",
                        $"attachment; filename=\"{fileStreamResult.FileDownloadName}\"");
                }

                await using var stream = fileStreamResult.FileStream;
                await stream.CopyToAsync(context.Response.Body);
            }

            else if (actionResult is NotFoundObjectResult) {
                context.Response.StatusCode = StatusCodes.Status404NotFound;
            }
            else if (actionResult is NoContentResult) {
                context.Response.StatusCode = StatusCodes.Status204NoContent;
            }
            else if (actionResult is ObjectResult o && o.StatusCode == 500) {
                context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                await context.Response.WriteAsJsonAsync(o);
            }
        }
        catch (Exception ex) {
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            await context.Response.WriteAsJsonAsync(new { error = ex.Message });
        }
    }
}

public static class FileDownloadMiddlewareExtensions {
    public static IApplicationBuilder UseCustomFileDownload(this IApplicationBuilder builder) {
        return builder.UseMiddleware<FileDownloadMiddleware>();
    }
}