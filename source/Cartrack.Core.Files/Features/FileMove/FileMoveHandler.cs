﻿using Cartrack.AppHost;
using Cartrack.AppHost.ServiceContracts;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Http.DataContracts;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;

namespace Cartrack.Core.Files.Features.FileMove;

public class FileMoveHandler(
    IStorageProviderFactory providerFactory,
    IServiceContractDiscovery serviceContract,
    IHttpContextAccessor context,
    AppSettings settings,
    ILogger<FileMoveHandler> logger) : IRequestHandler<FileMoveRequest, FileMoveResponse> {
    public async Task<FileMoveResponse> Handle(FileMoveRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            Requires.IsTrue(() => !string.IsNullOrWhiteSpace(request.SourcePath), () => $"{nameof(request.SourcePath)} is required");
            Requires.IsTrue(() => !string.IsNullOrWhiteSpace(request.DestinationPath), () => $"{nameof(request.DestinationPath)} is required");
            
            var moveJob = new FileMoveJob(request, providerFactory, context, settings, logger, token);
            var worker = serviceContract.Discover<IJobWorker>()!;
            await worker.Submit(moveJob, context.HttpContext?.TraceIdentifier);

            var job = new JobInfo(moveJob.Id, moveJob.Status, moveJob.CompletionLevel, moveJob.Description, string.Empty, string.Empty);
            return new FileMoveResponse(new FileMoveStatus(request, job));
        }
        catch (RequiresException c) {
            logger.LogError(c, "[{Trace} invalid request", trace);
            return new FileMoveResponse(null, c) { IsServerError = false };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} File move failed", trace);
            return new FileMoveResponse(null, ex){IsServerError = true};
        }
    }
}