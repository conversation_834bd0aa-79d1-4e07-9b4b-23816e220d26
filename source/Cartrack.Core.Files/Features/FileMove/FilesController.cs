﻿using Cartrack.Core.Files.Features.FileMove;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Core.Files.IO.Http;
public partial class FilesController {

    [HttpPut]
    [Route("move")]
    public async Task<ActionResult<FileMoveResponse>> FileMove([FromBody] FileMoveRequest request) {
        var response = await this._mediator.Send(request);
        return response.ToContentResult(this.HttpContext, response.Value);
    }
}