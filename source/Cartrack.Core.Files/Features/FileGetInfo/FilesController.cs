﻿using Cartrack.Core.Files.Features.FileGetInfo;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Core.Files.IO.Http;
public partial class FilesController {

    [HttpGet]
    [Route("info")]
    public async Task<ActionResult<FileGetInfoResponse>> FileGetInfo([FromQuery] string target) {
        var request = new FileGetInfoRequest(target);
        var response = await this._mediator.Send(request);
        return response.ToContentResult(this.HttpContext, response.Value);
    }
}