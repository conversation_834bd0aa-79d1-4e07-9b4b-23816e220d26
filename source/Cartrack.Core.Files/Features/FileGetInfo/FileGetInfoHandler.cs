﻿using Cartrack.AppHost;
using Cartrack.AppHost.ServiceContracts;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;
//using Cartrack.FileService.API.IO.Sql.Models;

namespace Cartrack.Core.Files.Features.FileGetInfo;

public class FileGetInfoHandler(
    IStorageProviderFactory providerFactory,
    IServiceContractDiscovery serviceContract,
    IHttpContextAccessor context,
    AppSettings settings,
    ILogger<FileGetInfoHandler> logger) : IRequestHandler<FileGetInfoRequest, FileGetInfoResponse> {

    public async Task<FileGetInfoResponse> Handle(FileGetInfoRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            Requires.IsTrue(() => !string.IsNullOrWhiteSpace(request.TargetUri), () => $"{nameof(request.TargetUri)} is required");
            
            var user = AuthClaims.From(context.HttpContext!.User);
            var storageSettings = settings.GetStorageSettings(user!);
            var storageProvider = providerFactory.Create(storageSettings);
            if (await storageProvider.Exists(request.TargetUri)) {
                var fileRecord = await storageProvider.GetFileInfo(request.TargetUri);

                // Generating public url value
                string publicUrl = PublicUrlEncryption.GetPublicUrl(request.TargetUri, settings, user!);
                return new FileGetInfoResponse(new FileGetInfoStatus(request.TargetUri, this.PrettyPrint(fileRecord.Size), fileRecord.Hash, fileRecord.CreatedUtc, fileRecord.ModifiedUtc, publicUrl));
            }

            return new FileGetInfoResponse(null);
        }
        catch (RequiresException c) {
            logger.LogError(c, "[{Trace} invalid request", trace);
            return new FileGetInfoResponse(null, c) { IsServerError = false };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} File GetInfo failed", trace);
            return new FileGetInfoResponse(null, ex) { IsServerError = true };
        }
    }

    private string PrettyPrint(long fileSizeInBytes) {
        const double kb = 1024;
        const double mb = kb * 1024;
        const double gb = mb * 1024;
        if (fileSizeInBytes >= gb) {
            return (fileSizeInBytes / gb).ToString("0.##") + "GB";
        }
        if (fileSizeInBytes >= mb) {
            return (fileSizeInBytes / mb).ToString("0.##") + "MB";
        }
        if (fileSizeInBytes >= kb) {
            return (fileSizeInBytes / kb).ToString("0.##") + "KB";
        }

        return $"{fileSizeInBytes} Bytes";
    }
}