﻿using Cartrack.AppHost;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Features.FileCopy;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;

//using Cartrack.FileService.API.IO.Sql.Models;

namespace Cartrack.Core.Files.Features.FolderGetFiles;

public class FolderGetFilesHandler(
    IStorageProviderFactory providerFactory,
    IHttpContextAccessor context,
    AppSettings settings,
    ILogger<FileCopyHandler> logger) : IRequestHandler<FolderGetFilesRequest, FolderGetFilesResponse> {

    public async Task<FolderGetFilesResponse> Handle(FolderGetFilesRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            var user = AuthClaims.From(context.HttpContext!.User);
            var storageSettings = settings.GetStorageSettings(user!);
            var storageProvider = providerFactory.Create(storageSettings);
            var files = await storageProvider.GetFiles(request.Path, request.SearchPattern, request.Option);
          
            files = files
                .Select(f => UrlPathHelper.ToFileServiceUrl(settings.FsServerBaseUrl, storageSettings, f))
                .ToArray();
            return new FolderGetFilesResponse(new FolderGetFilesStatus(files));
        }
        catch (RequiresException c) {
            logger.LogError(c, "[{Trace} invalid request", trace);
            return new FolderGetFilesResponse(null, c) { IsServerError = false };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} File copy failed", trace);
            return new FolderGetFilesResponse(null, ex) { IsServerError = true };
        }
    }
}