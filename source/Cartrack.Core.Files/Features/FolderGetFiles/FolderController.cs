﻿using Cartrack.Core.Files.Features.FolderGetFiles;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Core.Files.IO.Http;
public partial class FolderController {

    [HttpGet]
    [Route("getfiles")]
    public async Task<ActionResult<FolderGetFilesResponse>> GetFiles([FromQuery] string path = "", [FromQuery] string searchOption = "TopDirectoryOnly", [FromQuery] string searchPattern = "*") {
        if (!Enum.TryParse<SearchOption>(searchOption, out var option)) {
            option = SearchOption.TopDirectoryOnly;
        }
        
        var request = new FolderGetFilesRequest(path, searchPattern, option);
        var response = await this._mediator.Send(request);
        return response.ToContentResult(this.HttpContext, response.Value);
    }
}