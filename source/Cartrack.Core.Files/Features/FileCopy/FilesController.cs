﻿using Cartrack.Core.Files.Features.FileCopy;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace  Cartrack.Core.Files.IO.Http;

public partial class FilesController {

    [HttpPut]
    [Route("copy")]
    public async Task<ActionResult<FileCopyResponse>> FileCopy([FromBody] FileCopyRequest request) {
        var response = await this._mediator.Send(request);
        return response.ToContentResult(this.HttpContext, response.Value);
    }
}