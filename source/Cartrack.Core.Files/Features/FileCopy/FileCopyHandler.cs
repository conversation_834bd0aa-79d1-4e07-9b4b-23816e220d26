﻿using Cartrack.AppHost;
using Cartrack.AppHost.ServiceContracts;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Http.DataContracts;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;

//using Cartrack.FileService.API.IO.Sql.Models;

namespace Cartrack.Core.Files.Features.FileCopy;

public class FileCopyHandler(
    IStorageProviderFactory providerFactory,
    IServiceContractDiscovery serviceContract,
    IHttpContextAccessor context,
    AppSettings settings,
    ILogger<FileCopyHandler> logger) : IRequestHandler<FileCopyRequest, FileCopyResponse> {
    
    public async Task<FileCopyResponse> Handle(FileCopyRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            Requires.IsTrue(() => !string.IsNullOrWhiteSpace(request.SourcePath), () => $"{nameof(request.SourcePath)} is required");
            Requires.IsTrue(() => !string.IsNullOrWhiteSpace(request.DestinationPath), () => $"{nameof(request.DestinationPath)} is required");
            
            var copyJob = new FileCopyJob(request, providerFactory, context, settings, logger, token);
            var worker = serviceContract.Discover<IJobWorker>()!;
            await worker.Submit(copyJob, context.HttpContext?.TraceIdentifier);

            var link = $"{settings.FsServerBaseUrl}/job/{copyJob.Id}";
            var job = new JobInfo(copyJob.Id, copyJob.Status, copyJob.CompletionLevel, copyJob.Description, "", link);
            return new FileCopyResponse(new FileCopyStatus(request, job));
        }
        catch (RequiresException c) {
            logger.LogError(c, "[{Trace} invalid request", trace);
            return new FileCopyResponse(null, c) { IsServerError = false};
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} File copy failed", trace);
            return new FileCopyResponse(null, ex) { IsServerError = true};
        }
    }
}