﻿using Cartrack.AppHost;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.Infrastructure.Providers;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Core.Files.IO.Sql.Models;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Common;

namespace Cartrack.Core.Files.Features.FileCopy;

public class FileCopyJob(
    FileCopyRequest request,
    IStorageProviderFactory factory,
    IHttpContextAccessor context,
    AppSettings settings,
    ILogger logger,
    CancellationToken token) : BaseJob(factory, context, settings, logger, token) {

    private IFileRepository _fileRepo = new FileRepository(settings);
    public override string Description { get; } = $"Copying {request.SourcePath} to  {request.DestinationPath}";

    protected override Task Run(IStorageProvider storage) {
        return storage.Copy(request.SourcePath, request.DestinationPath, request.Overwrite);
    }

    public override async Task Complete() {
        Requires.NotNull(this._jobInfo, nameof(FsJobInfo));

        var provider = factory.Create(this.CurrentStorageSettings);
        var info = await provider.GetFileInfo(request.DestinationPath);
        var fileId = await this._fileRepo.CreateOrUpdate(info);

        this._jobInfo!.FileInfoId = fileId;
        await base.Complete();
        logger.LogInformation("[{TraceId}] File copied successfully from {Source} to {Destination}", this.TraceId, request.SourcePath, request.DestinationPath);
    }

    public override async Task Fail(Exception failureReason) {
        await base.Fail(failureReason);
        logger.LogError("[{TraceId}] File copy failed with {Exception}", this.TraceId, failureReason);
    }
}