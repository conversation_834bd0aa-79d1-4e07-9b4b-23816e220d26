﻿using Cartrack.AppHost;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.Infrastructure.Providers;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Core.Files.IO.Sql.Models;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Common;

namespace Cartrack.Core.Files.Features.FileDelete;

public class FileDeleteJob(
    FileDeleteRequest request,
    IStorageProviderFactory factory,
    IHttpContextAccessor context,
    AppSettings settings,
    ILogger logger,
    CancellationToken token) : BaseJob(factory, context, settings, logger, token) {
    public override string Description { get; } = $"Deleting {request.UriPath}";

    protected override Task Run(IStorageProvider storage) {
        return storage.Delete(request.UriPath);
    }

    public override async Task Complete() {
        Requires.NotNull(this._jobInfo, nameof(FsJobInfo));

        await base.Complete();
        logger.LogInformation("[{TraceId}] File {UriPath} deleted successfully", this.TraceId, request.UriPath);
    }

    public override async Task Fail(Exception failureReason) {
        await base.Fail(failureReason);
        logger.LogError("[{TraceId}] File delete failed with {Exception}", this.TraceId, failureReason);
    }
}