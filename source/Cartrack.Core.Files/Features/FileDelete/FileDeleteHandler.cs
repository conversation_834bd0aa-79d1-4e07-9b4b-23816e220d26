﻿using Cartrack.AppHost;
using Cartrack.AppHost.ServiceContracts;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Http.DataContracts;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;

namespace Cartrack.Core.Files.Features.FileDelete;

public class FileDeleteHandler(
    AppSettings settings,
    IStorageProviderFactory providerFactory,
    IServiceContractDiscovery serviceContract,
    IHttpContextAccessor context,
    ILogger<FileDeleteHandler> logger) : IRequestHandler<FileDeleteRequest, FileDeleteResponse> {
    public async Task<FileDeleteResponse> Handle(FileDeleteRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            Requires.IsTrue(() => !string.IsNullOrWhiteSpace(request.UriPath), () => $"{nameof(request.UriPath)} is required");
             
            var delJob = new FileDeleteJob(request, providerFactory, context, settings, logger, token);
            var worker = serviceContract.Discover<IJobWorker>()!;
            await worker.Submit(delJob, context.HttpContext?.TraceIdentifier);

            var link = $"{settings.FsServerBaseUrl}/job/{delJob.Id}";
            var job = new JobInfo(delJob.Id, delJob.Status, delJob.CompletionLevel, delJob.Description, "", link);
            return new FileDeleteResponse(new FileDeleteStatus(request, job));
        }
        catch (RequiresException c) {
            logger.LogError(c, "[{Trace} invalid request", trace);
            return new FileDeleteResponse(null, c) { IsServerError = false};
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} File delete failed", trace);
            return new FileDeleteResponse(null, ex){ IsServerError = true};
        }
    }
}