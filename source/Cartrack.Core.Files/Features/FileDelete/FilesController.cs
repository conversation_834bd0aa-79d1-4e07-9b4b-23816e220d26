﻿using Cartrack.Core.Files.Features.FileDelete;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Core.Files.IO.Http;
public partial class FilesController : ControllerBase {
    [HttpDelete]
    [Route("delete")]
    public async Task<ActionResult<FileDeleteResponse>> FileDelete([FromBody] FileDeleteRequest request) {
        var response = await this._mediator.Send(request);
        return response.ToContentResult(this.HttpContext, response.Value);
    }
}