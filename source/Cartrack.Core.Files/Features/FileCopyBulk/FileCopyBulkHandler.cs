﻿using Cartrack.AppHost;
using Cartrack.AppHost.ServiceContracts;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.Features.FileCopy;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Http.DataContracts;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;

namespace Cartrack.Core.Files.Features.FileCopyBulk;


public class FileCopyBulkHandler(
    IStorageProviderFactory providerFactory,
    IServiceContractDiscovery serviceContract,
    IHttpContextAccessor context,
    AppSettings settings,
    ILogger<FileCopyBulkHandler> logger) : IRequestHandler<FileCopyBulkRequest, FileCopyBulkResponse> {
    public async Task<FileCopyBulkResponse> Handle(FileCopyBulkRequest request, CancellationToken token) {
        var trace = context.HttpContext?.TraceIdentifier;
        try {
            List<FileCopyStatus> fileCopyStatuses = new();

            foreach (var req in request.FileCopyRequests) {
                Requires.IsTrue(() => !string.IsNullOrWhiteSpace(req.SourcePath), () => $"{nameof(req.SourcePath)} is required");
                Requires.IsTrue(() => !string.IsNullOrWhiteSpace(req.DestinationPath), () => $"{nameof(req.DestinationPath)} is required");

                var copyJob = new FileCopyJob(req, providerFactory, context, settings, logger, token);
                var worker = serviceContract.Discover<IJobWorker>()!;
                await worker.Submit(copyJob, context.HttpContext?.TraceIdentifier);

                var link = $"{settings.FsServerBaseUrl}/job/{copyJob.Id}";
                var job = new JobInfo(copyJob.Id, copyJob.Status, copyJob.CompletionLevel, copyJob.Description, string.Empty, link);
                fileCopyStatuses.Add(new FileCopyStatus(req, job));
            }

            return new FileCopyBulkResponse(fileCopyStatuses);
        }
        catch (RequiresException c) {
            logger.LogError(c, "[{Trace} invalid request", trace);
            return new FileCopyBulkResponse(null, c) { IsServerError = false};
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{Trace} File copy failed", trace);
            return new FileCopyBulkResponse(null, ex) {IsServerError = true};
        }
    }
}
//
// public class FileCopyBulkHandler(
//     IFileRepository fileRepository,
//     FileManagerService fileManagerService,
//     IHttpContextAccessor context,
//     ILogger<FileCopyBulkHandler> logger) : IRequestHandler<FileCopyBulkRequest, FileCopyBulkResponse> {
//     public async Task<FileCopyBulkResponse> Handle(FileCopyBulkRequest request, CancellationToken cancellationToken) {
//         var trace = context.HttpContext?.TraceIdentifier;
//         try {
//             logger.LogInformation("RequestId : {trace}. Bulk files copy process started {FilesInfo}.", trace, request.FilesInfo);
//
//             var newFile = await this.CopyFile(request);
//
//             await fileRepository.FileCopyBulk(newFile, request.FilesLocationInfo);
//
//             logger.LogInformation("RequestId : {trace}. {Count} Files copied successfully.", trace, newFile.Count());
//
//             return new FileCopyBulkResponse(newFile);
//         }
//         catch (Exception ex) {
//             logger.LogError(ex, "RequestId : {Trace}. Error in copying bulk file {FilesInfo}.", trace, request.FilesInfo);
//             return new FileCopyBulkResponse(default, ex);
//         }
//     }
//
//     internal async Task<List<FilesInfo>> CopyFile(FileCopyBulkRequest request) {
//         List<FilesInfo> filesInfos = new();
//
//         foreach (var fileInfo in request.FilesInfo) {
//             var newPath = request.FilesLocationInfo.First(f => f.SourceFileId == fileInfo.Id).ToDestination;
//             var overwrite = request.FilesLocationInfo.First(f => f.SourceFileId == fileInfo.Id).Overwrite;
//
//             var newName = Path.GetFileName(newPath);
//             if (!overwrite) {
//                 newName = UniqueNameGenerator.GenerateUniqueFileName(newPath);
//             }
//
//             var newFile = new FilesInfo {
//                 FileName = newName,
//                 FileType = fileInfo.FileType,
//                 Id = overwrite ? fileInfo.Id : 0,
//                 JobId = Guid.NewGuid().ToString(),
//                 Path = UniqueNameGenerator.ReplaceName(newPath, newName),
//                 Permissions = fileInfo.Permissions
//             };
//
//             filesInfos.Add(newFile);
//         }
//
//         await Task.Yield();
//         return filesInfos;
//     }
// }