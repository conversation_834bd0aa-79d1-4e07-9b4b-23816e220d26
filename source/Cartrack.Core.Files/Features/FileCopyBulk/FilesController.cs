﻿
using Cartrack.Core.Files.Features.FileCopyBulk;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Core.Files.IO.Http;

public partial class FilesController {
    [HttpPut]
    [Route("copyBulk")]
    public async Task<ActionResult<FileCopyBulkResponse>> FileCopyBulk([FromBody] FileCopyBulkRequest request) {
        var response = await this._mediator.Send(request);
        return response.ToContentResult(this.HttpContext, response.Value);
    }
}