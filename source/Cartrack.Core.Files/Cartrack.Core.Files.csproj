﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <OutputType>Library</OutputType>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Cartrack.AppHost" Version="2.5.7" />
        <PackageReference Include="Cartrack.EFCore.Models.TfmsCustom" Version="1.2.1" />
        <PackageReference Include="Minio" Version="6.0.3" />
        <PackageReference Include="Scalar.AspNetCore" Version="1.2.44" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Features\FolderDelete\" />
        <Folder Include="Features\FolderGetFolders\" />
        <Folder Include="Mock\abcd\" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="Mock\abcd\FIS - TFMS Interface Requirements Specification v1.0.pdf" />
      <None Remove="Mock\TFMS recordings 10122024.zip" />
      <None Remove="logs\**" />
      <None Remove="Properties\launchSettings.json" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Cartrack.Fleet.Common\Cartrack.Fleet.Common.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Domain\BaseResponse.cs" />
      <Compile Remove="IO\Http\DataContracts\BaseResponseExtensions.cs" />
      <Compile Remove="Domain\StorageSettings.cs" />
      <Compile Remove="IO\Http\DataContracts\BaseError.cs" />
      <Compile Remove="Domain\AppSettings.cs" />
      <Compile Remove="logs\**" />
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Remove="logs\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="logs\**" />
    </ItemGroup>

</Project>
