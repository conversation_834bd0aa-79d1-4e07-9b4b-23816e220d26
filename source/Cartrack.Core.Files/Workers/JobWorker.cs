﻿using Cartrack.AppHost;
using Cartrack.AppHost.Channels;
using Cartrack.AppHost.ServiceContracts;
using Cartrack.AppHost.WorkerHosting;
using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Core.Files.IO.Sql.Models;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;

namespace Cartrack.Core.Files.Workers;

public class JobWorker : Worker, IJobWorker {
    private IOneWayChannel<IJob>? _jobChannel;
    private readonly IServiceContractDiscovery _serviceDiscovery;
    private readonly AppSettings _settings;
    private readonly IChannel _channel;
    private readonly IJobRepository _jobRepo;
    private readonly ILogger<Worker> _logger;

    public JobWorker(IServiceContractDiscovery serviceDiscovery, AppSettings settings, IChannel channel, ILogger<Worker> logger) : base(logger, false) {
        this._serviceDiscovery = serviceDiscovery;
        this._settings = settings;
        this._channel = channel;
        this._jobRepo = new JobRepository(this._settings);
        this._logger = logger;
    }

    public async Task<FsJobInfo> GetJobInfo(long jobId) {
        var job = await this._jobRepo.Get(jobId);
        return job;
    }

    public void Dispose() {
    }

    public async Task<long> Submit(IJob job, string requestId = "<unknown>") {
        try {
            Requires.NotNull(this._jobChannel, nameof(this._jobChannel));

            await job.Create();
            await this._jobChannel!.Write(job);
            
            // If the actual job completes first, the status will be "Completed" otherwise,
            // status will be "In Progress" or "Not Started" in which case the client will have
            // to call the API again to check the job status using the JobId  
            var tcs = new TaskCompletionSource();
            job.OnStatusChanged += OnStatusChanged;
            await Task.WhenAny(Delay(), tcs.Task);
            job.OnStatusChanged -= OnStatusChanged;
           
            void OnStatusChanged(object? sender, string e) {
               tcs.TrySetResult();
            }
            
            async Task Delay() {
                await Task.Delay(this._settings.FsMaxCallDurationMsec);
                tcs.TrySetResult();
            }

        }
        catch (Exception exc) {
            this._logger.LogError(exc, "[{TraceIdentifier}] Unable to create job. {Job}", requestId, job);
            await job.Fail(exc);
        }

        return job.Id;
    }

    public override async Task DoWork(CancellationToken cancellationToken) {
        this._serviceDiscovery.Register<IJobWorker, JobWorker>(this);
        this._jobChannel = this._channel.GetChannel<IJob>(cancellationToken);

        //Create parallel listeners for processing the jobs
        var count = this._settings.FsWorkerCount;
        var listeners = Enumerable.Range(1, count)
            .Select(i => this._jobChannel.GetListener(job => job.Id % count == i, name:$"Worker{i}"))
            .ToArray();

        await Task.WhenAll(listeners.Select(StartListeningForJobs));
        return;

        async Task StartListeningForJobs(IChannelListener<IJob> listener) {
            while (!cancellationToken.IsCancellationRequested) {
                var job = await listener.Listen(cancellationToken);
                this._logger.LogInformation("Processing job {job} : {id}", job, listener.Name);
                try {
                    if (job != null) {
                        await job.Start();
                    }
                }
                catch (Exception exc) {
                    this._logger.LogError(exc, "Error processing job {job}", job);
                }
            }
        }
    }
}