﻿using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.IO.Sql.Models;
using Cartrack.EFCore.Models.TfmsCustom;

namespace Cartrack.Core.Files.IO.Sql;

public class MockFileRepository : IFileRepository {
    private static long FILEID = 0;
    private SortedList<long, FsFileInfo> _jobs = new();
    public Task<long> Create(FsFileInfo fileInfo) {
        fileInfo.Id = Interlocked.Increment(ref FILEID);
        this._jobs[fileInfo.Id] = fileInfo;
        return Task.FromResult(fileInfo.Id);
    }

    public Task<FsFileInfo?> GetById(long jobId) {
        return Task.FromResult(this._jobs[jobId]);
    }

    public Task Update(FsFileInfo fileInfo) {
        this._jobs[fileInfo.Id] = fileInfo;
        return Task.FromResult(fileInfo);
    }

    public Task<bool> Delete(long fileId) {
        this._jobs.Remove(fileId);
        return Task.FromResult(true);
    }

    public async Task<long> CreateOrUpdate(FileInfoRecord info) {
        var dbRecord = await this.GetByFilePath(info.FullPath);
        if (dbRecord == null) {
            dbRecord = new FsFileInfo() {
                Name = Path.GetFileName(info.FullPath),
                Path = info.FullPath,
                Size = info.Size,
                Provider = info.ProviderName,
                Hash = info.Hash,
                ContentType = ContentTypeMapper.GetContentTypeFromPath(info.FullPath)
            };
            await this.Create(dbRecord);
        }
        else {
            dbRecord.Size = info.Size;
            dbRecord.Hash = info.Hash;
            dbRecord.Provider = info.ProviderName;
            dbRecord.ContentType = ContentTypeMapper.GetContentTypeFromPath(info.FullPath);
            await this.Update(dbRecord);
        }

        return dbRecord.Id;
    }

    public async Task<FsFileInfo?> GetByFilePath(string filePath) {
        var pair = this._jobs.FirstOrDefault(k => k.Value.Path.StartsWith(filePath));
        return pair.Value;
    }

    public void Dispose() {
    }
}