﻿using Cartrack.Core.Files.IO.Sql.Models;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Core.Files.IO.Sql;

public class JobRepository(AppSettings settings) : IJobRepository {
    private readonly AppTfmsCustomDbContext _dbContext = new(settings.ConnectionString);

    public async Task Create(FsJobInfo jobInfo) {
        await this._dbContext.FsJobInfos.AddAsync(jobInfo);
        await this._dbContext.SaveChangesAsync();
    }

    public async Task<FsJobInfo?> Get(long jobId) {
        return (await this._dbContext.FsJobInfos.Where(x => x.Id == jobId).ToArrayAsync()).FirstOrDefault();
    }

    public async Task<FsJobInfo?> Update(long jobId, string status, int completionLevel) {
        var job = await this.Get(jobId);
        if (job is null)
            return null;
     
        job.UpdatedTs = DateTime.UtcNow;
        job.Status = status;
        job.CompletionLevel = completionLevel;
        await this._dbContext.SaveChangesAsync();
        return job;
    }

    public async Task<FsJobInfo?> Update(FsJobInfo jobInfo) {
        var job = await this.Get(jobInfo.Id);
        if (job is null)
            return null;

        job.FileInfoId = jobInfo.FileInfoId;
        job.JobDesc = jobInfo.JobDesc;
        job.StatusDetails = jobInfo.StatusDetails;
        job.UpdatedTs = DateTime.UtcNow;
        job.Status = jobInfo.Status;
        job.CompletionLevel = jobInfo.CompletionLevel;
        await this._dbContext.SaveChangesAsync();
        return job;
    }

    public async Task<bool> Delete(long jobId) {
        var job = await this.Get(jobId);
        if (job == null)
            return false;
        
        this._dbContext.FsJobInfos.Remove(job);
        await this._dbContext.SaveChangesAsync();
        return true;
    }

    public void Dispose() {
        this._dbContext.Dispose();
    }
}