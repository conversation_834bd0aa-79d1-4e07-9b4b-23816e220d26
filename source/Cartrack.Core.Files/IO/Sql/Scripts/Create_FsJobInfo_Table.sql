﻿-- tfms_custom.fs_job_info definition

-- Drop table

-- DROP TABLE tfms_custom.fs_job_info;

CREATE TABLE tfms_custom.fs_job_info (
                                         id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                                         status text DEFAULT 'Not Started'::text NOT NULL,
                                         status_details text DEFAULT ''::text NOT NULL,
                                         file_info_id int8 DEFAULT 0 NOT NULL,
                                         completion_level int4 DEFAULT 0 NOT NULL,
                                         job_desc text DEFAULT ''::text NOT NULL,
                                         created_ts timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                         updated_ts timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                         CONSTRAINT fs_job_info_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_fs_job_info_created_ts ON tfms_custom.fs_job_info USING btree (created_ts);
CREATE INDEX idx_fs_job_info_file_id ON tfms_custom.fs_job_info USING btree (file_info_id);
CREATE INDEX idx_fs_job_info_status ON tfms_custom.fs_job_info USING btree (status);
CREATE INDEX idx_fs_job_info_updated_ts ON tfms_custom.fs_job_info USING btree (updated_ts);


-- tfms_custom.fs_job_info foreign keys

ALTER TABLE tfms_custom.fs_job_info ADD CONSTRAINT fs_job_info_fs_file_info_fk FOREIGN KEY (file_info_id) REFERENCES tfms_custom.fs_file_info(id);