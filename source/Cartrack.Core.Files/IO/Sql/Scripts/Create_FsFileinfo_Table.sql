﻿
-- Create the fs_file_info table
CREATE TABLE IF NOT EXISTS  tfms_custom.fs_file_info (
                                          id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                                          name TEXT NOT NULL,
                                          content_type TEXT,
                                          hash TEXT,
                                          size BIGINT NOT NULL,
                                          path TEXT,
                                          provider TEXT
);

-- Add indexes for commonly queried fields
CREATE INDEX IF NOT EXISTS  idx_fs_file_info_name ON tfms_custom.fs_file_info (name);
CREATE INDEX IF NOT EXISTS  idx_fs_file_info_path ON tfms_custom.fs_file_info (path);