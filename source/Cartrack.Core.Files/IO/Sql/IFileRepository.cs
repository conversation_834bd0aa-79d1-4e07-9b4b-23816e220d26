﻿using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.IO.Sql.Models;
using Cartrack.EFCore.Models.TfmsCustom;

namespace Cartrack.Core.Files.IO.Sql;

public interface IFileRepository : IDisposable {
    Task<long> Create(FsFileInfo fileInfo);

    Task<FsFileInfo?> GetById(long jobId);

    Task Update(FsFileInfo fileInfo);

    Task<bool> Delete(long fileId);

    Task<long> CreateOrUpdate(FileInfoRecord info);
    
    Task<FsFileInfo?> GetByFilePath(string filePath);
}