﻿using Cartrack.Core.Files.Domain;
using Cartrack.Core.Files.IO.Sql.Models;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Core.Files.IO.Sql;

public class FileRepository(AppSettings settings) : IFileRepository {
    private readonly AppTfmsCustomDbContext _dbContext = new(settings.ConnectionString);
    public async Task<long> Create(FsFileInfo fileInfo) {
        await this._dbContext.FsFileInfos.AddAsync(fileInfo);
        await this._dbContext.SaveChangesAsync();
        return fileInfo.Id;
    }

    public async Task<FsFileInfo?> GetById(long id) {
        return (await this._dbContext.FsFileInfos.Where(t => t.Id == id).ToArrayAsync()).FirstOrDefault();
    }

    public async Task Update(FsFileInfo file) {
        var fileInfo = await this.GetById(file.Id);
        if (fileInfo is not null) {
            fileInfo.Path = file.Path;
            fileInfo.Name = file.Name;
            fileInfo.Size = file.Size;
            fileInfo.ContentType = file.ContentType;
            fileInfo.Hash = file.Hash;
            fileInfo.Provider = file.Provider;
            await this._dbContext.SaveChangesAsync();
        }

    }

    public async Task<bool> Delete(long fileId) {
        var fileInfo = await this.GetById(fileId);
        if (fileInfo is null)
            return false;
        
        this._dbContext.FsFileInfos.Remove(fileInfo);
        await this._dbContext.SaveChangesAsync();
        return true;
    }

    public async Task<long> CreateOrUpdate(FileInfoRecord info) {
        var fileInfo = this._dbContext.FsFileInfos.Where(f => f.Hash == info.Hash).FirstOrDefault();
        if (fileInfo is null) {
            fileInfo = new FsFileInfo();
        }

        fileInfo.Name = Path.GetFileName(info.FullPath);
        fileInfo.Path = info.FullPath;
        fileInfo.Size = info.Size;
        fileInfo.ContentType = ContentTypeMapper.GetContentTypeFromPath(info.FullPath);
        fileInfo.Hash = info.Hash;
        fileInfo.Provider = info.ProviderName;
        if (fileInfo.Id == 0) {
            return await this.Create(fileInfo);
        }

        await this._dbContext.SaveChangesAsync();
        return fileInfo.Id;
    }

    public async Task<FsFileInfo?> GetByFilePath(string filePath) {
        return await this._dbContext.FsFileInfos.FirstOrDefaultAsync(t => t.Path == filePath);
    }

    public void Dispose() {
        this._dbContext.Dispose();
    }
}