﻿using Cartrack.Core.Files.IO.Sql.Models;
using Cartrack.EFCore.Models.TfmsCustom;

namespace Cartrack.Core.Files.IO.Sql;

public class MockJobRepository: IJobRepository {
    
    private static long JOBID = 0;
    private SortedList<long, FsJobInfo> _jobs = new();
    public Task Create(FsJobInfo jobInfo) {
        jobInfo.Id = Interlocked.Increment(ref JOBID);
        this._jobs[jobInfo.Id] = jobInfo;
        return Task.Delay(Random.Shared.Next(50,150));
    }

    public Task<FsJobInfo> Get(long jobId) {
        return Task.FromResult(this._jobs[jobId]);
    }

    

    public Task<FsJobInfo> Update(FsJobInfo jobInfo) {
        this._jobs[jobInfo.Id] = jobInfo;
        return Task.FromResult(jobInfo);
    }

    public Task<bool> Delete(long jobId) {
        this._jobs.Remove(jobId);
        return Task.FromResult(true);
    }

    public void Dispose() {
    }
}