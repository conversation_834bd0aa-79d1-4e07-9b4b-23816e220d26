﻿using Cartrack.Fleet.Vehicle.IO.Http.Filters;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Core.Files.IO.Http;

[ApiController]
[Route("file")]
[PermissionsContext]
public partial class FilesController : ControllerBase {
    private readonly ILogger<FilesController> _logger;
    private readonly IMediator _mediator;

    public FilesController(ILogger<FilesController> logger, IMediator mediator) {
        this._logger = logger;
        this._mediator = mediator;
    }
}