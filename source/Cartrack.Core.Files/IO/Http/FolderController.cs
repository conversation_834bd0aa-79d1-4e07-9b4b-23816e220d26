﻿using Cartrack.Fleet.Vehicle.IO.Http.Filters;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Core.Files.IO.Http;

[ApiController]
[Route("folder")]
[PermissionsContext]
public partial class FolderController : ControllerBase {
    private readonly ILogger<FolderController> _logger;
    private readonly IMediator _mediator;

    public FolderController(ILogger<FolderController> logger, IMediator mediator) {
        this._logger = logger;
        this._mediator = mediator;
    }
}