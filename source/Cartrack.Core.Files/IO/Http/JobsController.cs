﻿using Cartrack.Fleet.Vehicle.IO.Http.Filters;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Core.Files.IO.Http;

[ApiController]
[Route("job")]
[PermissionsContext]
public partial class JobsController : ControllerBase {
    private readonly ILogger<JobsController> _logger;
    private readonly IMediator _mediator;

    public JobsController(ILogger<JobsController> logger, IMediator mediator) {
        this._logger = logger;
        this._mediator = mediator;
    }
}