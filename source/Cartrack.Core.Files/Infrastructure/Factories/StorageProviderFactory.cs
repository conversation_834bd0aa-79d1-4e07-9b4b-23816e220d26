﻿using Cartrack.Core.Files.Infrastructure.Providers;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Http;
using Minio;

namespace Cartrack.Core.Files.Infrastructure.Factories;

public class StorageProviderFactory : IStorageProviderFactory {
    private readonly HttpContext? _context;

    public StorageProviderFactory(IHttpContextAccessor context) {
        this._context = context.HttpContext;
    }
    
    public StorageProviderFactory(HttpContext? context) {
        this._context = context;
    }

    public IStorageProvider Create(StorageSettings storageSettings) {
        var type = storageSettings.ProviderType;
        switch (type) {
            case StorageProviderType.MinIo:
                if (this._context is { } ctx) {
                    var minioClient = this._context.RequestServices.GetRequiredService<IMinioClient>(); 
                    return new MinIoStorageProvider(storageSettings, minioClient);
                }

                var minio = MinioClientBuilder.Create(storageSettings.RootPath, storageSettings.MinIo);
                return new MinIoStorageProvider(storageSettings, minio);
            
            case StorageProviderType.FileSystem:
                return new FileSystemStorageProvider(storageSettings);
            case StorageProviderType.Mock:
                return new MockStorageProvider(storageSettings);
            case StorageProviderType.PostgresDb:
                //return _serviceProvider.GetService<DatabaseStorageProvider>();
                break;
            case StorageProviderType.AzureBlobStorage:
                //return _serviceProvider.GetService<CloudStorageProvider>();
                break;
            default:
                throw new ArgumentException("Invalid provider type", nameof(type));
        }

        throw new NotImplementedException();
    }
}