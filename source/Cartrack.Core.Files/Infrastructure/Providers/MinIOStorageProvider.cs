﻿using Cartrack.AppHost;
using Cartrack.Core.Files.Domain;
using Cartrack.Fleet.Common;
using Minio;
using Minio.DataModel.Args;
using Minio.Exceptions;
using System.Collections.Concurrent;
using System.IO.Pipelines;
using System.Text.RegularExpressions;
using System.Web;

namespace Cartrack.Core.Files.Infrastructure.Providers;

public class MinIoStorageProvider(StorageSettings storageSettings, IMinioClient client) : IStorageProvider {
   public IMinioClient Client { get; } = client; //GetOrAddClient(storageSettings);

    public StorageProviderType Type { get; } = StorageProviderType.MinIo;

    public async Task Copy(string sourceFileNameUri, string destFileNameUri, bool overwrite) {
        var uriSource = this.GetMinIoPathUri(sourceFileNameUri);
        var uriDestination = this.GetMinIoPathUri(destFileNameUri);

        var isFileExist = await this.Exists(uriSource);

        Requires.IsTrue(
                () => isFileExist,
                () => $"{sourceFileNameUri} does not exist in the bucket '{storageSettings.BucketName}'.");

        if (!overwrite) {
            var objectExists = await this.Exists(uriDestination);
            Requires.IsTrue(
                () => !objectExists,
                () => $"{sourceFileNameUri} already exists in the bucket/destinationPath '{storageSettings.BucketName}/{destFileNameUri}'. Specify overwrite = true");
        }

        sourceFileNameUri = GetObjectFromUri(uriSource);
        destFileNameUri = GetObjectFromUri(uriDestination);

        var copyArgs = new CopyObjectArgs()
        .WithBucket(storageSettings.BucketName)
        .WithObject(destFileNameUri)
        .WithCopyObjectSource(new CopySourceObjectArgs()
            .WithBucket(storageSettings.BucketName)
            .WithObject(sourceFileNameUri));

        await this.Client.CopyObjectAsync(copyArgs);
    }

    public async Task<bool> Create(string pathFragment, bool overwrite, Func<Stream, Task> write, int bufferSize = 4096) {
        var uri = this.GetPathUri(pathFragment);

        var bucketName = storageSettings.BucketName;

        var objName = GetObjectFromUri(uri);
        if (!overwrite) {
            var objectExists = await this.Exists(uri);
            Requires.IsTrue(
                () => !objectExists,
                () => $"{uri} already exists in the bucket '{bucketName}'. Specify overwrite = true");
        }

        await using var memoryStream = new MemoryStream();
        await write(memoryStream);
        memoryStream.Seek(0, SeekOrigin.Begin);
        var contentType = ContentTypeMapper.GetContentTypeFromPath(uri.ToString());

        var poa = new PutObjectArgs()
            .WithBucket(bucketName)
            .WithObject(objName)
            .WithStreamData(memoryStream)
            .WithObjectSize(memoryStream.Length)
            .WithContentType(contentType);

        var status = await this.Client.PutObjectAsync(poa);
        return status != null;
    }

    private string GetMinIoPathUri(string hostPathUri) {
        var pathFragment = GetObjectFromUri(hostPathUri);
        return this.GetPathUri(pathFragment);
    }

    private string GetPathUri(string pathFragment) {
        var uriBuilder = new UriBuilder(storageSettings.RootPath);
        if (!uriBuilder.Path.EndsWith("/"))
            uriBuilder.Path += "/";

        uriBuilder.Path += pathFragment;
        return uriBuilder.Uri.ToString();
    }

    public async Task Delete(string path) {
        var uriPath = this.GetMinIoPathUri(path);

        var isFileExist = await this.Exists(uriPath);
        Requires.IsTrue(
                () => isFileExist,
                () => $"{path} is not exist in the bucket '{storageSettings.BucketName}'.");

        var filePath = GetObjectFromUri(uriPath);

        var deleteObjectArgs = new RemoveObjectArgs()
            .WithBucket(storageSettings.BucketName)
            .WithObject(filePath);

        await this.Client.RemoveObjectAsync(deleteObjectArgs);
    }

    public async Task<Stream?> GetFileAsStream(string path) {
        var uriPath = this.GetMinIoPathUri(path);
        var isFileExist = await this.Exists(uriPath);
        Requires.IsTrue(() => isFileExist, () => $"Invalid request.");

        var filePath = GetObjectFromUri(uriPath);
        var streamPipe = new Pipe();
        var writer = streamPipe.Writer;
        var reader = streamPipe.Reader;
        
        var getObjectArgs = new GetObjectArgs()
            .WithBucket(storageSettings.BucketName)
            .WithObject(filePath)
            .WithCallbackStream(async (stream, _) =>
            {
                try {
                    await stream.CopyToAsync(writer.AsStream());
                }
                finally {
                    await writer.CompleteAsync();
                }
            });

        
        // Start the MinIO operation in the background
        _ = Task.Run(async () =>
        {
            try {
                await this.Client.GetObjectAsync(getObjectArgs);
            }
            catch (Exception ex) {
                await writer.CompleteAsync(ex);
            }
        });
      
        return reader.AsStream();
    }

    public async Task<bool> Exists(string uriPath) {
        try {
            var objName = GetObjectFromUri(uriPath);
            var statObjectArgs = new StatObjectArgs()
                .WithBucket(storageSettings.BucketName)
                .WithObject(objName);

            var status = await this.Client.StatObjectAsync(statObjectArgs);
            return true;
        }
        catch (ObjectNotFoundException) {
            return false;
        }
    }

    public async Task<FileInfoRecord> GetFileInfo(string uriPath) {
        Requires.NotNullOrEmpty(uriPath, nameof(uriPath));
        var bucketName = storageSettings.BucketName;

        var objName = uriPath;
        if (uriPath.StartsWith(storageSettings.ServerBaseUrl))
            objName = GetObjectFromUri(uriPath);

        var statObjectArgs = new StatObjectArgs()
            .WithBucket(bucketName)
            .WithObject(objName);

        var statObject = await this.Client.StatObjectAsync(statObjectArgs);
        var hash = statObject.ETag; // ETag serves as a unique hash for the file in MinIO

        var fileInfoRecord = new FileInfoRecord(
            uriPath,
            statObject.Size,
            hash,
            statObject.LastModified.ToUniversalTime(),
            statObject.LastModified.ToUniversalTime(),
            nameof(MinIoStorageProvider)
        );

        return fileInfoRecord;
    }



    private string GetRootPathUriWithBucketName() {
        string rootPath = storageSettings.RootPath;
        if (!rootPath.EndsWith("/"))
            rootPath += "/";
        return rootPath;
    }


    public async Task<string[]> GetFiles(string prefixPath, string searchPattern, SearchOption option) {

        var resultList = new List<string>();
        try {
            var listArgs = new ListObjectsArgs()
            .WithBucket(storageSettings.BucketName)
            .WithPrefix(prefixPath)
            .WithRecursive(option == SearchOption.AllDirectories);

            await foreach (var item in this.Client.ListObjectsEnumAsync(listArgs)) {
                var fileName = item.Key;

                if (this.MatchesSearchPattern(fileName, searchPattern)) {
                    var rootPath = this.GetRootPathUriWithBucketName();
                    var fullFileName = new Uri(new Uri(rootPath), fileName).ToString();
                    resultList.Add(fullFileName);
                }
            }
        }
        catch (Exception e) {
            throw new InvalidOperationException(e.ToString());
        }
        return resultList.ToArray();
    }


    private bool MatchesSearchPattern(string fileName, string searchPattern) {
        var regexPattern = "^" + Regex.Escape(searchPattern).Replace("\\*", ".*").Replace("\\?", ".") + "$";
        return Regex.IsMatch(fileName, regexPattern, RegexOptions.IgnoreCase);
    }


    public Task<string[]> GetFolders(string path, string searchPattern, SearchOption option) {
        throw new NotImplementedException();
    }

    public async Task Move(string sourceFileNameUri, string destFileNameUri, bool overwrite) {
        // Step 1: Copy the source file to destination
        await this.Copy(sourceFileNameUri, destFileNameUri, overwrite);

        // Step 2: Delete the source file
        await this.Delete(sourceFileNameUri);
    }

    private static async Task TryCreateBucket(IMinioClient client, string bucketName) {
        var beArgs = new BucketExistsArgs().WithBucket(bucketName);
        var found = await client.BucketExistsAsync(beArgs).ConfigureAwait(false);
        if (!found) {
            var mbArgs = new MakeBucketArgs().WithBucket(bucketName);
            await client.MakeBucketAsync(mbArgs).ConfigureAwait(false);
        }
    }

    private static string GetObjectFromUri(string uriPath) {
        var uri = new Uri(uriPath);

        //skip the starting '/' and the bucketName
        return HttpUtility.UrlDecode(string.Join(string.Empty, uri!.Segments.Skip(2)));
    }
    public void Dispose() {
       Client.Dispose();
    }
}