﻿using Cartrack.AppHost;
using Cartrack.Core.Files.Domain;
using Cartrack.Fleet.Common;

namespace Cartrack.Core.Files.Infrastructure.Providers;

public class MockStorageProvider(StorageSettings storageSettings) : IStorageProvider {
    public StorageProviderType Type => StorageProviderType.Mock;

    public Task Copy(string sourceFileName, string destFileName, bool overwrite) {
        return Task.CompletedTask;
    }

    public Task Move(string sourceFileName, string destFileName, bool overwrite) {
        return Task.CompletedTask;
    }

    public Task Delete(string path) {
        return Task.CompletedTask;
    }

    public Task<bool> Exists(string path) {
        return Task.FromResult(true);
    }

    public Task<bool> Create(string path, bool overwrite, Func<Stream, Task> write, int bufferSize = 4096) {
        return Task.FromResult(true);
    }

    public async Task<FileInfoRecord> GetFileInfo(string path) {
        Requires.NotNullOrEmpty(path, nameof(path));

        await Task.Yield();
        var info = new FileInfo(path);
        var hash = "SOMERANDOMHASH";
        return new FileInfoRecord(info.FullName, 42, hash, DateTime.UtcNow, DateTime.UtcNow, nameof(MockStorageProvider));
    }

    public Task<string[]> GetFolders(string path, string searchPattern, SearchOption option) {
        throw new NotImplementedException();
    }

    public async Task<Stream?> GetFileAsStream(string uriPath) {
        await Task.Yield();
        var ms = new MemoryStream("abcdef"u8.ToArray());
        ms.Seek(0, SeekOrigin.Begin);
        return ms;
    }


    public Task<string[]> GetFiles(string path, string searchPattern, SearchOption option) {
        throw new NotImplementedException();
    }

    public void Dispose() {
    }
}