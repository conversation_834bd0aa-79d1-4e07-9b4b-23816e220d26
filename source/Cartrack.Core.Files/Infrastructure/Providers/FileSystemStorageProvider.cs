﻿using Cartrack.AppHost;
using Cartrack.Core.Files.Domain;
using Cartrack.Fleet.Common;
using System.Web;

namespace Cartrack.Core.Files.Infrastructure.Providers;

public class FileSystemStorageProvider(StorageSettings storageSettings) : IStorageProvider {
    private readonly Uri _serverBaseUri = new(storageSettings.ServerBaseUrl);

    public StorageProviderType Type { get; } = StorageProviderType.FileSystem;

    public Task Copy(string sourceUri, string destUri, bool overwrite) {
        Requires.NotNullOrEmpty(sourceUri, nameof(sourceUri));
        Requires.NotNullOrEmpty(destUri, nameof(destUri));

        var sourceFile = this.ToLocalPath(sourceUri);
        var targetFile = this.ToLocalPath(destUri);

        Requires.IsTrue(() => System.IO.File.Exists(sourceFile), () => $"{sourceFile} does not exist!");

        var dir = Path.GetDirectoryName(targetFile) ?? string.Empty;
        Requires.NotNullOrEmpty(dir, "dir");
        Directory.CreateDirectory(dir);

        System.IO.File.Copy(sourceFile, targetFile, overwrite);
        return Task.CompletedTask;
    }

    public Task Move(string sourceUri, string destUri, bool overwrite) {
        Requires.NotNullOrEmpty(sourceUri, nameof(sourceUri));
        Requires.NotNullOrEmpty(destUri, nameof(destUri));

        var sourceFile = this.ToLocalPath(sourceUri);
        var targetFile = this.ToLocalPath(destUri);

        Requires.IsTrue(() => System.IO.File.Exists(sourceFile), () => $"{sourceFile} does not exist!");

        System.IO.File.Move(sourceFile, targetFile, overwrite);
        return Task.CompletedTask;
    }

    public async Task<Stream?> GetFileAsStream(string uriPath) {
        await Task.Yield();
        var localFile = this.ToLocalPath(uriPath);
        if (System.IO.File.Exists(localFile))
            return System.IO.File.OpenRead(localFile);
        
        return null;
    }
    public Task Delete(string sourceUri) {
        Requires.NotNullOrEmpty(sourceUri, nameof(sourceUri));
        var sourceFile = this.ToLocalPath(sourceUri);
        if (System.IO.File.Exists(sourceFile)) {
            System.IO.File.Delete(sourceFile);
        }

        return Task.CompletedTask;
    }

    public Task<bool> Exists(string path) {
        Requires.NotNullOrEmpty(path, nameof(path));
        return Task.FromResult(System.IO.File.Exists(this.ToLocalPath(path)));
    }

    public async Task<bool> Create(string pathFragment, bool overwrite, Func<Stream, Task> write, int bufferSize = 4096) {
        var targetFile = Path.Combine(storageSettings.RootPath, pathFragment);
        targetFile = Path.GetFullPath(targetFile);
        if (!overwrite) {
            Requires.IsTrue(() => !System.IO.File.Exists(targetFile), () => $"{pathFragment} already exists.  Specify overwrite = true");
        }

        var dir = Path.GetDirectoryName(targetFile);
        Directory.CreateDirectory(dir!);
        await using var fs = new FileStream(targetFile, FileMode.Create, FileAccess.Write, FileShare.None, bufferSize, true);
        await write(fs);

        return System.IO.File.Exists(targetFile);
    }

    public async Task<FileInfoRecord> GetFileInfo(string uriPath) {
        Requires.NotNullOrEmpty(uriPath, nameof(uriPath));

        var localFile = this.ToLocalPath(uriPath);
        Requires.IsTrue(() => System.IO.File.Exists(localFile), () => $"{uriPath} does not exist!");

        var info = new FileInfo(localFile);
        var hash = await FileHasher.ComputeMd5HashAsync(localFile);
        return new FileInfoRecord(info.FullName, info.Length, hash, info.CreationTimeUtc, info.LastWriteTimeUtc, nameof(FileSystemStorageProvider));
    }

    public Task<string[]> GetFolders(string path, string searchPattern, SearchOption option) {
        return Task.FromResult(Directory.GetDirectories(path, searchPattern, option));
    }

    public Task<string[]> GetFiles(string path, string searchPattern, SearchOption option) {
        return Task.FromResult(Directory.GetFiles(path, searchPattern, option));
    }

    private string ToLocalPath(string uriPath) {
        var isValidSource = Uri.TryCreate(uriPath, UriKind.Absolute, out var uri);

        //Validate Source and dest file are actual URIs
        Requires.IsTrue(() => isValidSource, () => $"{uriPath} is not a valid Uri");
        var relativePath = HttpUtility.UrlDecode(string.Join(string.Empty, uri!.Segments.Skip(1)));

        var sourceFile = Path.Combine(Path.GetFullPath(storageSettings.RootPath + "/../"), relativePath);

        //Convert it to a file system full path
        return Path.GetFullPath(sourceFile);
    }

    private string ToUriPath(string localPath) {
        var rootPath = Path.GetFullPath(storageSettings.RootPath);
        var uriFragment = localPath.Replace(rootPath, string.Empty);

        var builder = new UriBuilder { Path = uriFragment, Scheme = this._serverBaseUri.Scheme, Host = this._serverBaseUri.Host, Port = this._serverBaseUri.Port };
        return builder.Uri.ToString();
    }

    public void Dispose() {
    }
}