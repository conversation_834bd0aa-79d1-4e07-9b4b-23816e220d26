﻿using Cartrack.Core.Files.Domain;
using Cartrack.Fleet.Common;

namespace Cartrack.Core.Files.Infrastructure.Providers;

public interface IStorageProvider : IDisposable {
    StorageProviderType Type { get; }
    Task Copy(string sourceUri, string destFileUri, bool overwrite);
    Task Move(string sourceUri, string destUri, bool overwrite);
    Task Delete(string uri);
    Task<bool> Exists(string uri);
    Task<bool> Create(string pathFragment, bool overwrite, Func<Stream, Task> write, int bufferSize = 4096);
    Task<FileInfoRecord> GetFileInfo(string uri);
    Task<string[]> GetFolders(string uriPath, string searchPattern, SearchOption option);
    Task<string[]> GetFiles(string uriPath, string searchPattern, SearchOption option);
    
    Task<Stream?> GetFileAsStream(string uriPath);

    // Task<bool> Upload(FilesInfo file);
    // Task BulkUpload(IEnumerable<FilesInfo> files);
    // Task<FilesInfo> Download(string fileId);
    // Task<IEnumerable<FilesInfo>> BulkDownload(IEnumerable<string> fileIds);
    // Task Delete(string fileId);
    // Task BulkDelete(IEnumerable<string> fileIds);
    // Task Rename(string oldName, string newName);
    // Task Move(ObjectLocationInfo objectLocationInfo);
    // Task BulkMove(IEnumerable<ObjectLocationInfo> objectLocationInfos);
    // Task Copy(ObjectLocationInfo objectLocationInfo);
    // Task BulkCopy(IEnumerable<ObjectLocationInfo> objectLocationInfos);
    // Task ManageFilePermission(FilesInfo filesInfo);
    // Task ManageFolderPermission(FolderInfo folderInfo);
    // Task<IEnumerable<FilesInfo>> FileList(string location);
    // Task<IEnumerable<FolderInfo>> FolderList();
}