﻿namespace Cartrack.Core.Files.Domain;

public class UniqueNameGenerator {
    public static string GenerateUniqueFileName(string fileNameWithFullPath) {
        var baseFileName = Path.GetFileNameWithoutExtension(fileNameWithFullPath); // abc
        var fileExtension = Path.GetExtension(fileNameWithFullPath); // .txt
        var path = Path.GetDirectoryName(fileNameWithFullPath) ?? string.Empty; // D:\Folder

        var newFileName = baseFileName + fileExtension; // abc.txt
        var counter = 0;

        while (System.IO.File.Exists(Path.Combine(path, newFileName))) // D:\Folder\abc.txt
        {
            counter++;
            newFileName = $"{baseFileName}_copy({counter}){fileExtension}"; // abc_copy(1).txt, abc_copy(2).txt, etc
        }

        return newFileName;
    }

    public static string GenerateUniqueFolderName(string folderPathWithFullName) {
        //  D:\Folder\FolderA
        var baseFolderName = Path.GetFileName(folderPathWithFullName); // FolderA
        var directoryPath = Path.GetDirectoryName(folderPathWithFullName) ?? string.Empty; // D:\Folder

        var newFolderName = baseFolderName;
        var counter = 0;

        while (Directory.Exists(Path.Combine(directoryPath, newFolderName))) // Check if folder exists
        {
            counter++;
            newFolderName = $"{baseFolderName}_copy({counter})"; // FolderA_copy(1), FolderA_copy(2), etc.
        }

        return newFolderName;
    }

    public static string ReplaceName(string path, string newName) {
        var directoryPath = Path.GetDirectoryName(path) ?? string.Empty;

        var newPath = Path.Combine(directoryPath, newName);

        return newPath;
    }
}