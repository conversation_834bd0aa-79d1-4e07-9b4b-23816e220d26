﻿using System.Security.Cryptography;

namespace Cartrack.Core.Files.Domain;

public static class FileHasher
{
    /// <summary>
    /// Computes the MD5 hash of a file synchronously.
    /// </summary>
    /// <param name="filePath">The path to the file to hash</param>
    /// <returns>MD5 hash as a lowercase hexadecimal string</returns>
    /// <exception cref="ArgumentException">Thrown if file path is null or empty</exception>
    /// <exception cref="FileNotFoundException">Thrown if file does not exist</exception>
    public static string ComputeMd5Hash(string filePath)
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

        if (!System.IO.File.Exists(filePath))
            throw new FileNotFoundException("The specified file was not found", filePath);

        using var md5 = MD5.Create();
        using var stream = System.IO.File.OpenRead(filePath);
        byte[] hashBytes = md5.ComputeHash(stream);
        return ConvertHashToString(hashBytes);
    }

    /// <summary>
    /// Computes the MD5 hash of a file asynchronously.
    /// </summary>
    /// <param name="filePath">The path to the file to hash</param>
    /// <returns>MD5 hash as a lowercase hexadecimal string</returns>
    /// <exception cref="ArgumentException">Thrown if file path is null or empty</exception>
    /// <exception cref="FileNotFoundException">Thrown if file does not exist</exception>
    public static async Task<string> ComputeMd5HashAsync(string filePath)
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

        if (!System.IO.File.Exists(filePath))
            throw new FileNotFoundException("The specified file was not found", filePath);

        using var md5 = MD5.Create();
        await using var stream = System.IO.File.OpenRead(filePath);
        byte[] hashBytes = await md5.ComputeHashAsync(stream);
        return ConvertHashToString(hashBytes);
    }

    /// <summary>
    /// Converts a byte array to a lowercase hexadecimal string.
    /// </summary>
    /// <param name="hashBytes">Byte array of hash</param>
    /// <returns>Lowercase hexadecimal representation of the hash</returns>
    private static string ConvertHashToString(byte[] hashBytes)
    {
        return BitConverter.ToString(hashBytes)
            .Replace("-", string.Empty)
            .ToLowerInvariant();
    }

    /// <summary>
    /// Computes the MD5 hash of a byte array.
    /// </summary>
    /// <param name="data">Byte array to hash</param>
    /// <returns>MD5 hash as a lowercase hexadecimal string</returns>
    public static string ComputeMd5Hash(byte[] data)
    {
        if (data == null)
            throw new ArgumentNullException(nameof(data));

        using (var md5 = MD5.Create())
        {
            byte[] hashBytes = md5.ComputeHash(data);
            return ConvertHashToString(hashBytes);
        }
    }
}