﻿using Cartrack.AppHost;
using Cartrack.Fleet.Common;

namespace Cartrack.Core.Files.Domain;

public class UrlPathHelper {
    public static string GetBasePath(StorageSettings settings) {
        if (settings.ProviderType == StorageProviderType.FileSystem)
            return Path.GetDirectoryName(Path.GetFullPath(settings.RootPath))!;

        if (settings.ProviderType == StorageProviderType.MinIo)
            return settings.RootPath.Substring(0, settings.RootPath.LastIndexOf('/'));

        return settings.RootPath;
    }

    public static string GetRelativePath(StorageSettings settings, string fullBasePath, string fullPath) {
        Requires.IsTrue(() => fullPath.StartsWith(fullBasePath), $"Invalid path \"{fullPath}\" relative to base \"{fullBasePath}\"");

        var providerType = settings.ProviderType;
        if (providerType == StorageProviderType.FileSystem || providerType == StorageProviderType.MinIo) {
            return fullPath.Substring(fullBasePath.Length + 1);
        }

        throw new NotImplementedException();
    }

    public static string ToFileServiceUrl(string baseUrl, StorageSettings storage, string sourcePath) {
        var basePath = GetBasePath(storage);
        var relativePath = GetRelativePath(storage, basePath, sourcePath);
        return $"{baseUrl}/{relativePath}".Replace("\\", "/");
    }
}