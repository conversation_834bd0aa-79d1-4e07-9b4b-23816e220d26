﻿using Cartrack.AppHost;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.Infrastructure.Providers;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Core.Files.IO.Sql.Models;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Vehicle.IO.Http.Filters;
using System.Diagnostics;

namespace Cartrack.Core.Files.Domain;

public abstract class BaseJob(
    IStorageProviderFactory factory,
    IHttpContextAccessor context,
    AppSettings settings,
    ILogger logger,
    CancellationToken cancellationToken) : IJob {
    protected FsJobInfo? _jobInfo;
    private Stopwatch? _sw;

    protected IJobRepository JobRepo { get; } = new JobRepository(settings);
    public string StatusDetails {
        get => this._jobInfo?.StatusDetails ?? "unknown";
        set => this._jobInfo!.StatusDetails = value;
    }

    public AuthClaims CurrentUser { get; } = GetUserFromContext(context);
    public TimeSpan Elapsed => this._sw?.Elapsed ?? TimeSpan.Zero;

    public StorageSettings CurrentStorageSettings { get; } = settings.GetStorageSettings(GetUserFromContext(context));

    public string TraceId { get; } = GetTraceIdContext(context);
    public abstract string Description { get; }

    public string Status {
        get => this._jobInfo?.Status ?? "unknown";
        private set {
            var changed = this._jobInfo!.Status != value;
            if (changed) {
                this._jobInfo!.Status = value;
                this.OnStatusChanged?.Invoke(this, value);
            }
        }
    }

    public event EventHandler<string>? OnStatusChanged;

    public async Task Create() {
        this._sw = Stopwatch.StartNew();
        await this.Initialize();

        //Save the job into the DB
        this._jobInfo = new FsJobInfo { JobDesc = this.Description, Status = Constants.NotStarted };
        await JobRepo.Create(this._jobInfo);
        logger.LogInformation("[{TraceId}] Job {JobId} created. \"{Description}\" ", this.TraceId, this._jobInfo.Id, this.Description);
    }

    public async Task Start() {
        Requires.NotNull(this._jobInfo, nameof(this._jobInfo));
        logger.LogInformation("[{TraceId}] Job {JobId} started. \"{Description}\" ", this.TraceId, this._jobInfo!.Id, this.Description);

        //Call the Job implementation that will call the IStorageProvider
        this._jobInfo!.Status = Constants.InProgress;
        using var storageProvider = factory.Create(this.CurrentStorageSettings);
        try {
            await this.Run(storageProvider);
            await this.Complete();
            this._sw!.Stop();
            logger.LogInformation("[{TraceId}] Job Id {JobId} completed successfully. Elapsed {elapsed}", this.TraceId, this._jobInfo.Id, this.Elapsed);
        }
        catch (Exception exc) {
            this._sw!.Stop();
            logger.LogWarning("[{TraceId}] Job Id {JobId} failed! {Error}. Elapsed {Elapsed}", this.TraceId, this._jobInfo.Id, exc.ToString(), this.Elapsed);
            await this.Fail(exc);
        }
    }

    public virtual Task Complete() {
        Requires.NotNull(this._jobInfo, nameof(FsJobInfo));
        Requires.IsTrue(() => this._jobInfo!.Id > 0, () => "JobInfo.Id must be greater than 0");

        //Update the Job in the DB to Complete
        return this.Update(Constants.Completed, 100);
    }

    public virtual Task Fail(Exception failureReason) {
        Requires.NotNull(this._jobInfo, nameof(FsJobInfo));
        Requires.IsTrue(() => this._jobInfo!.Id > 0, () => "JobInfo.Id must be greater than 0");

        return this.Update(Constants.Failed, statusDetails: failureReason.ToString());
    }

    public int CompletionLevel {
        get => this._jobInfo?.CompletionLevel ?? 0;
        private set => this._jobInfo!.CompletionLevel = value;
    }

    public long Id => this._jobInfo?.Id ?? 0;

    private static AuthClaims GetUserFromContext(IHttpContextAccessor context) {
        var loggedInUser = context.GetPermissionsContext()!.Claims;
        Requires.NotNull(loggedInUser, nameof(loggedInUser));
        return loggedInUser!;
    }

    private static string GetTraceIdContext(IHttpContextAccessor context) {
        return context.HttpContext?.TraceIdentifier ?? "<unknown>";
    }

    protected abstract Task Run(IStorageProvider provider);

    protected virtual Task Initialize() { return Task.CompletedTask; }

    public Task Update(string status, int? completionLevel = null, string? statusDetails = null) {
        this.Status = status;

        if (status == Constants.Completed || status == Constants.Failed) {
            this.OnStatusChanged = null;
        }
        
        if (completionLevel != null) {
            this.CompletionLevel = completionLevel.Value;
        }

        if (!string.IsNullOrEmpty(statusDetails)) {
            this.StatusDetails = statusDetails;
        }

        return JobRepo.Update(this._jobInfo!);
    }

    public override string ToString() {
        return $"{this.GetType().Name}, Id: {this.Id}, Description: {this.Description}, Status: {this.Status}, CompletionLevel: {this.CompletionLevel}%";
    }
}