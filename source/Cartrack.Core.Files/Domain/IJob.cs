﻿using Cartrack.Fleet.Common;

namespace Cartrack.Core.Files.Domain;

public interface IJob {
    Task Create();
    Task Start();
    Task Complete();
    Task Fail(Exception failureReason);
    int CompletionLevel { get; }
    long Id { get; }
    AuthClaims CurrentUser { get; } 
    TimeSpan Elapsed { get; }

    StorageSettings CurrentStorageSettings { get; }

    string TraceId { get; }
    string Description { get; }

    string Status { get; }

    event EventHandler<string> OnStatusChanged;
}