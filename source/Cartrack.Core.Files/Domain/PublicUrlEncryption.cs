﻿using Cartrack.Fleet.Common;
using System.Security.Cryptography;

namespace Cartrack.Core.Files.Domain;

public class PublicUrlEncryption {
    static public string GetPublicUrl(string url, AppSettings appSettings, AuthClaims user) {
        var uri = new Uri(url);
        var fileName = Path.GetFileName(uri.LocalPath);
        var fullPath = uri.LocalPath;
        var directoryPath = fullPath.Substring(0, fullPath.LastIndexOf('/')).TrimStart('/');
        var encryptedValue = Encrypt(string.Concat(user.UserId, "|", user.Account + "|" + directoryPath), appSettings);
        string newUrl = $"{uri.Scheme}://{uri.Host}:{uri.Port}/{appSettings.FsPublicUrlSegment}/{encryptedValue}/{fileName}";
        return newUrl;
    }

    static public string? GetPublicUrlUserInfo(string encryptedData, AppSettings appSettings) {
        try {
            // Checking if the string is Base64 encoded
            var encryptedBytes = Convert.FromBase64String(encryptedData);
            return Decrypt(encryptedData, appSettings);
        }
        catch {
            return null;
        }
    }

    static string Encrypt(string plainText, AppSettings appSettings) {
        using Aes aes = Aes.Create();
        aes.Key = HexStringToByteArray(appSettings!.FsEncryptionKey);
        aes.IV = HexStringToByteArray(appSettings.FsEncryptionVector);

        ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);
        using MemoryStream ms = new MemoryStream();
        using CryptoStream cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write);
        using StreamWriter sw = new StreamWriter(cs);
        sw.Write(plainText);
        sw.Flush();
        return Convert.ToBase64String(ms.ToArray());
    }

    static string Decrypt(string cipherText, AppSettings appSettings) {
        using (Aes aes = Aes.Create()) {
            aes.Key = HexStringToByteArray(appSettings!.FsEncryptionKey);
            aes.IV = HexStringToByteArray(appSettings.FsEncryptionVector);

            ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);
            using MemoryStream ms = new MemoryStream(Convert.FromBase64String(cipherText));
            using CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read);
            using StreamReader sr = new StreamReader(cs);
            return sr.ReadToEnd();
        }
    }

    static byte[] HexStringToByteArray(string hex) {
        byte[] bytes = new byte[hex.Length / 2];
        for (int i = 0; i < hex.Length; i += 2) {
            bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
        }
        return bytes;
    }

}
