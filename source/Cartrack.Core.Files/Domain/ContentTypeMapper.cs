﻿namespace Cartrack.Core.Files.Domain;

public static class ContentTypeMapper
{
    private static readonly Dictionary<string, string> _contentTypes = new(StringComparer.OrdinalIgnoreCase)
    {
        // Document types
        {".pdf", "application/pdf"},
        {".doc", "application/msword"},
        {".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"},
        {".xls", "application/vnd.ms-excel"},
        {".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},
        {".ppt", "application/vnd.ms-powerpoint"},
        {".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation"},
        
        // Text types
        {".txt", "text/plain"},
        {".csv", "text/csv"},
        {".html", "text/html"},
        {".htm", "text/html"},
        {".xml", "application/xml"},
        {".json", "application/json"},
        
        // Image types
        {".jpg", "image/jpeg"},
        {".jpeg", "image/jpeg"},
        {".png", "image/png"},
        {".gif", "image/gif"},
        {".bmp", "image/bmp"},
        {".webp", "image/webp"},
        {".svg", "image/svg+xml"},
        
        // Audio types
        {".mp3", "audio/mpeg"},
        {".wav", "audio/wav"},
        {".ogg", "audio/ogg"},
        
        // Video types
        {".mp4", "video/mp4"},
        {".avi", "video/x-msvideo"},
        {".mov", "video/quicktime"},
        
        // Compressed types
        {".zip", "application/zip"},
        {".rar", "application/x-rar-compressed"},
        {".7z", "application/x-7z-compressed"},
        
        // Development types
        {".cs", "text/plain"},
        {".js", "application/javascript"},
        {".css", "text/css"}
    };

    /// <summary>
    /// Gets the HTTP Content-Type for a given file extension.
    /// </summary>
    /// <param name="fileExtension">The file extension including the dot (e.g., ".pdf")</param>
    /// <returns>The corresponding HTTP Content-Type, or "application/octet-stream" if unknown</returns>
    public static string GetContentType(string fileExtension)
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(fileExtension))
        {
            return "application/octet-stream";
        }

        // Normalize the extension (ensure it starts with a dot)
        fileExtension = fileExtension.StartsWith(".") 
            ? fileExtension 
            : "." + fileExtension;

        // Try to get the content type, default to generic binary if not found
        return _contentTypes.TryGetValue(fileExtension, out string contentType)
            ? contentType
            : "application/octet-stream";
    }

    /// <summary>
    /// Attempts to get the content type from a full file path.
    /// </summary>
    /// <param name="filePath">Full path or filename</param>
    /// <returns>The corresponding HTTP Content-Type</returns>
    public static string GetContentTypeFromPath(string filePath)
    {
        if (string.IsNullOrWhiteSpace(filePath))
        {
            return "application/octet-stream";
        }

        string extension = System.IO.Path.GetExtension(filePath);
        return GetContentType(extension);
    }
}