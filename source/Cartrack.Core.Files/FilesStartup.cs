﻿using Cartrack.Core.Files.IO.Http;
using Cartrack.Core.Files.Infrastructure.Factories;
using Cartrack.Core.Files.IO.Sql;
using Cartrack.Core.Files.Workers;
using Cartrack.Fleet.Common;

namespace Cartrack.Core.Files;

public static class FilesStartup {
    public static void Register(IServiceCollection builderServices, AppSettings appSetting) {
        builderServices.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<FilesController>());
        builderServices.AddControllers().AddApplicationPart(typeof(FilesController).Assembly);
        builderServices.AddScoped<IStorageProviderFactory, StorageProviderFactory>();
        builderServices.AddHostedService<JobWorker>();
        //builderServices.AddScoped<IFileRepository, MockFileRepository>();
        //builderServices.AddScoped<IJobRepository,MockJobRepository>();
    }
}