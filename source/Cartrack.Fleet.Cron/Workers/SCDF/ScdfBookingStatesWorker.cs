﻿using Cartrack.AppHost.Common.Cron;
using Cartrack.AppHost.WorkerHosting;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Cron.Workers.SCDF;

public class ScdfBookingStatesWorker : Worker {
    private readonly AppSettings _appSettings;
    private readonly ICronJobScheduler _cron;
    private readonly ILogger<Worker> _logger;

    public ScdfBookingStatesWorker(AppSettings appSettings,  ICronJobScheduler cron, ILogger<Worker> logger, bool autoRestartUnlessStopped = false) : base(logger, autoRestartUnlessStopped) {
        this._appSettings = appSettings;
        this._cron = cron;
        this._logger = logger;
         
       
    }

    public string Name = "SCDF cron job";
    
    public override async Task DoWork(CancellationToken token) {
        await Task.Delay(60*1000, token);
        this._logger.LogInformation("[{Name}] Starting SCDF cron job.", this.Name);
        var timer = new PeriodicTimer(this._appSettings.ScdfBookingsCronSchedule);
        while (await timer.WaitForNextTickAsync(token)) {
            try {
                this._logger.LogInformation("[{Name}] Running SCDF cron job.", this.Name);
                await this.Run();
            }
            catch (Exception exc) {
                this._logger.LogError(exc,"[{Name}] SCDF cron job failed.", this.Name);
            }
        }
    }

    private async Task Run() {
        //For SCDF we only need to update the Active Bookings to ActiveLate (if applicable)
        const int activeBookingStatusCode = 6;
        const int activeLateBookingStatusCode = 8;
        
        await using var dbContext = new AppPoolDbContext(_appSettings.ConnectionString);
        var activeBookings = await dbContext.Bookings.Where(b => b.BookingStatusId == activeBookingStatusCode && 
                                                                 b.UserId == this._appSettings.ScdfUserId).ToArrayAsync();

        var count = 0;
        foreach (var b in activeBookings) {
            if (b.EndTs.ToUniversalTime() < DateTime.UtcNow) {
                //This booking is now past the end time, so we need to update it to ActiveLate
                b.BookingStatusId = activeLateBookingStatusCode;
                b.UpdatedTs = DateTime.UtcNow;
                count++;
            }
        }
        
        this._logger.LogInformation("[{Name}] Updated {count} bookings to ActiveLate", this.Name, count);
        await dbContext.SaveChangesAsync();
    }
}