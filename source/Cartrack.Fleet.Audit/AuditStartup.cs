﻿using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Audit.IO.Http;
using Cartrack.Fleet.Audit.IO.Sql;
using Cartrack.Fleet.Common;
using Microsoft.Extensions.DependencyInjection;

namespace Cartrack.Fleet.Audit;

public static class AuditStartup {
    public static void Register(IServiceCollection builderServices, AppSettings appSetting) {
        //builderServices.AddScoped<IAuthRepository, AuthRepository>();
        builderServices.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<AuditController>());
        builderServices.AddControllers().AddApplicationPart(typeof(AuditController).Assembly);

        builderServices.AddScoped<IClientUserAuditRepository, ClientUserAuditRepository>();
        builderServices.AddScoped<IIssuanceSettingsRulesAuditRepository, IssuanceSettingsRulesAuditRepository>();
        builderServices
            .AddScoped<IIssuanceSettingsVehicleCategoriesAuditRepository,
                IssuanceSettingsVehicleCategoriesAuditRepository>();
        builderServices.AddScoped<IIssuanceSettingsPurposeAuditRepository, IssuanceSettingsPurposeAuditRepository>();
        builderServices.AddScoped<IVehicleAuditRepository, VehicleAuditRepository>();
        builderServices.AddScoped<ILoginHistoryRepository, LoginHistoryRepository>();
    }
}