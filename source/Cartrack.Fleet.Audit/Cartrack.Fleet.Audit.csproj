﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>12</LangVersion>
    </PropertyGroup>

    <ItemGroup>
      <Folder Include="Features\GetBookingSettingsHistory\" />
      <Folder Include="Features\GetClientUserHistory\" />
      <Folder Include="Features\GetClientUsersHistory\" />
      <Folder Include="Infrastructure\" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Cartrack.AppHost" Version="2.5.7" />
      <PackageReference Include="Cartrack.EFCore.Models.CT" Version="1.2.1" />
      <PackageReference Include="Cartrack.EFCore.Models.Fleet" Version="1.3.4" />
      <PackageReference Include="Cartrack.EFCore.Models.TfmsCustom" Version="1.2.1" />
      <PackageReference Include="Cartrack.EFCore.Models.Pool" Version="1.1.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Cartrack.Fleet.Common\Cartrack.Fleet.Common.csproj" />
    </ItemGroup>

</Project>
