﻿using Cartrack.Fleet.Common;

namespace Cartrack.Fleet.Audit.Domain;

public class ClientUserChangeHistory : ChangeHistory {
    public override EntityType Entity { get; } = EntityType.User;
    public required long UserId { get; set; }
    public required string ClientUserId { get; init; }
    public required string UserName { get; init; }
    public int TotalCount { get; set; }
    public List<ClientUserChange> Changes { get; init; } = [];
}