﻿using Cartrack.Fleet.Common;

namespace Cartrack.Fleet.Audit.Domain;

public class VehicleChangeHistory : ChangeHistory {
    public override EntityType Entity { get; } = EntityType.Vehicle;
    public required long VehicleId { get; init; }
    public required string VehicleRegistration { get; init; }
    public int TotalCount { get; set; }
    public List<VehicleChange> Changes { get; init; } = [];
}