﻿using Cartrack.Fleet.Common;

namespace Cartrack.Fleet.Audit.Domain;

public class LoginChangeHistory : ChangeHistory {
    public override EntityType Entity { get; } = EntityType.Logins;
    public required long UserId { get; init; }
    public required string ClientUserId { get; init; }
    public required string ClientUserName { get; init; }
    public int TotalCount { get; set; }
    public List<LoginChange> Changes { get; init; } = [];
}