﻿using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Features.GetVehicleHistory;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Audit.IO.Http;

public partial class AuditController {
    [HttpGet]
    [Route("vehicles/{id}")]
    public async Task<ActionResult<GetVehicleHistoryResponse>> GetVehicleHistory(int id, DateTime From, DateTime To,
        int Page, int PageSize) {
        var updatedRequest = new GetVehicleHistoryRequest(id, From, To, Page, PageSize);

        var response = await this._mediator.Send(updatedRequest);
        return response.ToContentResult<GetVehicleHistoryResponse, VehicleChangeHistory>(this.HttpContext);
    }
}