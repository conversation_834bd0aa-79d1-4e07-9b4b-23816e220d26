﻿using Cartrack.AppHost;
using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Common;
using MediatR;

namespace Cartrack.Fleet.Audit.Features.GetVehicleHistory;

public class GetVehicleHistoryHandler : IRequestHandler<GetVehicleHistoryRequest, GetVehicleHistoryResponse> {
    private readonly IVehicleAuditRepository _repo;

    public GetVehicleHistoryHandler(IVehicleAuditRepository repo) {
        this._repo = repo;
    }

    public async Task<GetVehicleHistoryResponse> Handle(GetVehicleHistoryRequest request,
        CancellationToken cancellationToken) {
        //Step1: Always validate the request contains correct data
        Requires.IsTrue(() => request.To >= request.From, "To date must be greater than From date");
        Requires.IsTrue(() => request.From <= DateTime.Now, "From date must be less than Today");
        Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");

        var vehicles = await this._repo.GetVehicles(request.VehicleId, cancellationToken);

        var veh = vehicles.FirstOrDefault();

        if (!vehicles.Any()) {
            return new GetVehicleHistoryResponse(new VehicleChangeHistory() {
                VehicleId = 0,
                VehicleRegistration = "",
                TotalCount = 0,
                From = request.From,
                To = request.To
            });
        }

        var auVehicleDriverLicenses =
            await this._repo.GetAuVehicleDriverLicenses(request.VehicleId, request.From, request.To, cancellationToken);

        var driverLicenses = await this._repo.GetDriverLicenseTypes(cancellationToken);

        var auVehicleSpecialLicenses =
            await this._repo.GetAuVehicleSpecialLicenses(request.VehicleId, request.From, request.To,
                cancellationToken);

        var specialLicenses = await this._repo.GetDriverSpecialLicenseTypes(cancellationToken);

        var auVehicleDepartments =
            await this._repo.GetAuVehicleDepartments(request.VehicleId, request.From, request.To, cancellationToken);

        var departments = await this._repo.GetDepartments(cancellationToken);

        var auVehicleAdditionalInfo = await this._repo.GetAuVehicleAdditionalInfos(request.VehicleId, request.From,
            request.To, request.Page, request.PageSize, cancellationToken);

        var totalAuVehicleAdditionalInfo =
            await this._repo.GetCountAuVehicleAdditionalInfos(request.VehicleId, request.From, request.To,
                cancellationToken);

        var query = from v in auVehicleAdditionalInfo
            join c in vehicles on v.VehicleId equals c.VehicleId
            where v.VehicleId == request.VehicleId
            orderby v.AuTs descending
            select new {
                Registration = c.Registration, Vehicle = v
            };

        var pagedResults = query
            .ToList(); // Execute the in-memory query

        var history = new VehicleChangeHistory() {
            VehicleId = request.VehicleId,
            VehicleRegistration = veh.Registration,
            TotalCount = totalAuVehicleAdditionalInfo,
            From = request.From,
            To = request.To
        };

        var keysToIgnore = new HashSet<string> {
            "AuAuditId",
            "Cts",
            "Uts",
            "AuTs",
            "VehicleId",
            "AuUsername",
            "AuApplication",
            "AuCounter"
        };

        for (int i = 0; i < request.PageSize; i++) {
            if (i >= pagedResults.Count) {
                // If we reach beyond the available list, break the loop
                break;
            }

            var b = pagedResults[i]; // Current Value Item
            var previousB = (i + 1 < pagedResults.Count) ? pagedResults[i + 1] : null; // Next item is PreviousValue

            ChangeType type = ChangeType.Unknown;

            switch (b.Vehicle.AuActionType) {
                case 'I': type = ChangeType.Created; break;
                case 'U': type = ChangeType.Updated; break;
                case 'D': type = ChangeType.Deleted; break;
            }

            var difference = new object[0][];
            if (previousB != null && b != null && previousB.Vehicle != null && b.Vehicle != null) {
                difference = Utilities.CompareObjectAndFindDifference(previousB.Vehicle, b.Vehicle, keysToIgnore);
            }

            Dictionary<string, string> previousValueList = new Dictionary<string, string>();
            Dictionary<string, string> currentValueList = new Dictionary<string, string>();

            if (difference is not null) {
                foreach (var x in difference) {
                    if (x != null) {
                        var field = x[0].ToString();
                        var lastValue = x[1].ToString();
                        var currValue = x[2].ToString();

                        if (field is null) continue;

                        switch (b.Vehicle?.AuActionType) {
                            case 'I': lastValue = Constants.Missing; break;
                            case 'D': currValue = Constants.Deleted; break;
                        }

                        previousValueList.Add(field, lastValue ?? "");
                        currentValueList.Add(field, currValue ?? "");
                    }
                }
            }

            ///////////////////////
            // Vehicle Departments
            ///////////////////////
            var newUpdateVehicleDepartments = from f in auVehicleDepartments
                join d in departments on f.DepartmentId equals d.DepartmentId
                where f.AuTs >= b.Vehicle.AuTs.Value.AddSeconds(0)
                      && f.AuTs <= b.Vehicle.AuTs.Value.AddSeconds(5)
                      && f.VehicleId == b.Vehicle.VehicleId
                      && (f.AuActionType == 'I')
                select new {
                    f.DepartmentId,
                    d.DepartmentName,
                    f.AuActionType
                };

            var oldVehicleDepartments = from f in auVehicleDepartments
                join d in departments on f.DepartmentId equals d.DepartmentId
                where f.AuTs >= b.Vehicle.AuTs.Value.AddSeconds(0)
                      && f.AuTs <= b.Vehicle.AuTs.Value.AddSeconds(5)
                      && f.VehicleId == b.Vehicle.VehicleId
                      && (f.AuActionType == 'D')
                select new {
                    f.DepartmentId,
                    d.DepartmentName,
                    f.AuActionType
                };

            var previousVD =
                string.Join("\n", oldVehicleDepartments.Select(v => $"{v.DepartmentId}-{v.DepartmentName}")) ??
                Constants.Missing;
            var currentVD =
                string.Join("\n", newUpdateVehicleDepartments.Select(v => $"{v.DepartmentId}-{v.DepartmentName}")) ??
                Constants.Deleted;

            if (currentVD != previousVD) {
                previousValueList.Add("VehicleDepartments", previousVD);
                currentValueList.Add("VehicleDepartments", currentVD);
            }

            ///////////////////////////
            // Vehicle Driver Licenses
            ///////////////////////////

            var newUpdateAuVehicleDriverLicenses = from f in auVehicleDriverLicenses
                join d in driverLicenses on f.DriverLicenseTypeId equals d.DriverLicenseTypeId
                where f.AuTs >= b.Vehicle.AuTs.Value.AddSeconds(0)
                      && f.AuTs <= b.Vehicle.AuTs.Value.AddSeconds(5)
                      && f.VehicleId == b.Vehicle.VehicleId
                      && f.AuActionType == 'I'
                select new {
                    f.DriverLicenseTypeId,
                    d.Name,
                    f.AuActionType
                };

            var oldAuVehicleDriverLicenses = from f in auVehicleDriverLicenses
                join d in driverLicenses on f.DriverLicenseTypeId equals d.DriverLicenseTypeId
                where f.AuTs >= b.Vehicle.AuTs.Value.AddSeconds(0)
                      && f.AuTs <= b.Vehicle.AuTs.Value.AddSeconds(5)
                      && f.VehicleId == b.Vehicle.VehicleId
                      && f.AuActionType == 'D'
                select new {
                    f.DriverLicenseTypeId,
                    d.Name,
                    f.AuActionType
                };

            var previousVDL =
                string.Join("\n", oldAuVehicleDriverLicenses.Select(v => $"{v.DriverLicenseTypeId}-{v.Name}")) ??
                Constants.Missing;
            var currentVDL =
                string.Join("\n", newUpdateAuVehicleDriverLicenses.Select(v => $"{v.DriverLicenseTypeId}-{v.Name}")) ??
                Constants.Deleted;

            if (currentVDL != previousVDL) {
                previousValueList.Add("VehicleDriverLicenses", previousVDL);
                currentValueList.Add("VehicleDriverLicenses", currentVDL);
            }

            ///////////////////////////
            // Vehicle Special Licenses
            ///////////////////////////

            var newUpdateAuVehicleSpecialLicenses = from f in auVehicleSpecialLicenses
                join d in specialLicenses on f.SpecialLicenseTypeId equals d.DriverSpecialLicenseTypeId
                where f.AuTs >= b.Vehicle.AuTs.Value.AddSeconds(0)
                      && f.AuTs <= b.Vehicle.AuTs.Value.AddSeconds(5)
                      && f.VehicleId == b.Vehicle.VehicleId
                      && f.AuActionType == 'I'
                select new {
                    f.SpecialLicenseTypeId,
                    d.LicenseName,
                    f.AuActionType
                };

            var oldAuVehicleSpecialLicenses = from f in auVehicleSpecialLicenses
                join d in specialLicenses on f.SpecialLicenseTypeId equals d.DriverSpecialLicenseTypeId
                where f.AuTs >= b.Vehicle.AuTs.Value.AddSeconds(0)
                      && f.AuTs <= b.Vehicle.AuTs.Value.AddSeconds(5)
                      && f.VehicleId == b.Vehicle.VehicleId
                      && f.AuActionType == 'D'
                select new {
                    f.SpecialLicenseTypeId,
                    d.LicenseName,
                    f.AuActionType
                };

            var previousVSL =
                string.Join("\n",
                    oldAuVehicleSpecialLicenses.Select(v => $"{v.SpecialLicenseTypeId}-{v.LicenseName}")) ??
                Constants.Missing;
            var currentVSL = string.Join("\n",
                                 newUpdateAuVehicleSpecialLicenses.Select(v =>
                                     $"{v.SpecialLicenseTypeId}-{v.LicenseName}")) ??
                             Constants.Deleted;

            if (currentVSL != previousVSL) {
                previousValueList.Add("VehicleSpecialLicenses", previousVSL);
                currentValueList.Add("VehicleSpecialLicenses", currentVSL);
            }

            history.Changes.Add(new VehicleChange() {
                VehicleId = (b != null && b.Vehicle != null) ? b.Vehicle.VehicleId : 0,
                VehicleRegistration = (b != null && b.Vehicle != null) ? b.Registration : "",
                Type = type,
                PreviousValue = previousValueList,
                CurrentValue = currentValueList,
                CreatedTime = (b != null && b.Vehicle != null) ? b.Vehicle.AuTs : null,
                ChangedBy = (b != null && b.Vehicle != null) ? b.Vehicle.AuUsername : ""
            });
        }

        return new GetVehicleHistoryResponse(history);
    }
}