﻿using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Features.GetIssuanceSettingsPurposeHistory;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Runtime.InteropServices.JavaScript;

namespace Cartrack.Fleet.Audit.IO.Http;

public partial class AuditController {
    [HttpPost]
    [Route("issuance/settings/purpose")]
    public async Task<ActionResult<GetIssuanceSettingsPurposeHistoryResponse>> GetIssuanceSettingsPurposeHistory(
        [FromBody] GetIssuanceSettingsPurposeHistoryRequest request) {
        var response = await this._mediator.Send(request);
        return response
            .ToContentResult<GetIssuanceSettingsPurposeHistoryResponse, IssuanceSettingsPurposeChangeHistory>(
                this.HttpContext);
    }
}