﻿using Cartrack.AppHost;
using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using MediatR;
using Cartrack.EFCore.Models.Fleet;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Index.Quadtree;
using Serilog.Sinks.Graylog.Core.Extensions;
using System.Linq;


namespace Cartrack.Fleet.Audit.Features.GetIssuanceSettingsPurposeHistory;

public class GetIssuanceSettingsPurposeHistoryHandler : IRequestHandler<GetIssuanceSettingsPurposeHistoryRequest,
    GetIssuanceSettingsPurposeHistoryResponse> {
    private readonly IIssuanceSettingsPurposeAuditRepository _repo;

    public GetIssuanceSettingsPurposeHistoryHandler(IIssuanceSettingsPurposeAuditRepository repo) {
        _repo = repo;
    }

    public async Task<GetIssuanceSettingsPurposeHistoryResponse> Handle(
        GetIssuanceSettingsPurposeHistoryRequest request, CancellationToken cancellationToken) {
        //Step1: Always validate the request contains correct data
        Requires.IsTrue(() => request.To >= request.From, "To date must be greater than From date");
        Requires.IsTrue(() => request.From <= DateTime.Now, "From date must be less than Today");
        Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");

        ///////////////////////////////////
        ///// User Booking Purpose
        ///////////////////////////////////
        var auUserBookingPurposes = await this._repo.GetAuditIssuanceSettingsPurpose(request.Search ?? "", request.From,
            request.To, request.Page, request.PageSize, cancellationToken);
        int totalAuBookingPurposes =
            await this._repo.GetTotal(request.Search ?? "", request.From, request.To, cancellationToken);

        // Extract the list of Booking Rule
        var bookingPurposes = await this._repo.GetBookingPurposes(cancellationToken);

        var history = new IssuanceSettingsPurposeChangeHistory() {
            From = request.From,
            To = request.To,
            TotalCount = totalAuBookingPurposes
        };

        int tmpCount = 0;

        foreach (var t in bookingPurposes) {
            var query = from v in auUserBookingPurposes
                where v.BookingPurposeId == t.BookingPurposeId
                orderby v.AuTs descending
                select new {
                    AuBookingPurpose = new AuditIssuanceSettingsPurposeDto(
                        v.UserId,
                        v.BookingPurposeId,
                        v.BookingPurposeName,
                        v.IsDeleted,
                        v.AuTs,
                        v.AuUsername,
                        v.AuActionType,
                        v.AuBookingPurposeVehicleTypes
                    )
                };

            var pagedResults = query.ToList(); // Execute the in-memory query

            for (int i = 0; i < pagedResults.Count; i++) {
                if (tmpCount == request.PageSize) {
                    break;
                }

                var b = pagedResults[i]; // Current Value Item
                var previousB = (i + 1 < pagedResults.Count) ? pagedResults[i + 1] : null;

                ChangeType type = ChangeType.Unknown;

                switch (b.AuBookingPurpose.AuActionType) {
                    case 'I': type = ChangeType.Created; break;
                    case 'U': type = ChangeType.Updated; break;
                    case 'D': type = ChangeType.Deleted; break;
                }

                Dictionary<string, string> currentValueList = new Dictionary<string, string>();
                Dictionary<string, string> previousValueList = new Dictionary<string, string>();

                var result = await this._repo.ValidateChange(b.AuBookingPurpose,
                    (previousB != null)
                        ? previousB.AuBookingPurpose
                        : new AuditIssuanceSettingsPurposeDto(0, 0, null, false, null, null, null, null));
                currentValueList = result.Item1;
                previousValueList = result.Item2;

                history.Changes.Add(new IssuanceSettingsPurposeChange() {
                    Type = type,
                    BookingPurposeId =
                        (b != null && b.AuBookingPurpose != null) ? b.AuBookingPurpose.BookingPurposeId : 0,
                    BookingPurposeName =
                        (b != null && b.AuBookingPurpose != null && b.AuBookingPurpose.BookingPurposeName != null)
                            ? b.AuBookingPurpose.BookingPurposeName
                            : "",
                    PreviousValue = previousValueList,
                    CurrentValue = currentValueList,
                    CreatedTime = (b != null && b.AuBookingPurpose != null) ? b.AuBookingPurpose.AuTs : null,
                    ChangedBy = (b != null && b.AuBookingPurpose != null && b.AuBookingPurpose.AuUsername != null)
                        ? b.AuBookingPurpose.AuUsername
                        : ""
                });

                tmpCount++;
            }
        }

        // Reorder based on Audit CreatedTime
        IssuanceSettingsPurposeChangeHistory changes1 = new IssuanceSettingsPurposeChangeHistory() {
            From = history.From,
            To = history.To,
            TotalCount = history.TotalCount,
            Changes = history.Changes
                .OrderByDescending(change => change.CreatedTime)
                .ToList()
        };

        history = changes1;

        return new GetIssuanceSettingsPurposeHistoryResponse(history);
    }
}