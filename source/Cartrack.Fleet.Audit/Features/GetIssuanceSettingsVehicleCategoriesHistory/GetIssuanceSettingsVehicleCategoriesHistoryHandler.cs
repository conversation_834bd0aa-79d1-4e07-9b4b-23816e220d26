﻿using Cartrack.AppHost;
using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using MediatR;
using Cartrack.EFCore.Models.Fleet;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Index.Quadtree;
using Serilog.Sinks.Graylog.Core.Extensions;


namespace Cartrack.Fleet.Audit.Features.GetIssuanceSettingsVehicleCategoriesHistory;

public class GetIssuanceSettingsVehicleCategoriesHistoryHandler : IRequestHandler<
    GetIssuanceSettingsVehicleCategoriesHistoryRequest, GetIssuanceSettingsVehicleCategoriesHistoryResponse> {
    private readonly IIssuanceSettingsVehicleCategoriesAuditRepository _repo;

    public GetIssuanceSettingsVehicleCategoriesHistoryHandler(IIssuanceSettingsVehicleCategoriesAuditRepository repo) {
        _repo = repo;
    }

    public async Task<GetIssuanceSettingsVehicleCategoriesHistoryResponse> Handle(
        GetIssuanceSettingsVehicleCategoriesHistoryRequest request, CancellationToken cancellationToken) {
        //Step1: Always validate the request contains correct data
        //Requires.NotNullOrEmpty(request.Search, nameof(request.Search));
        Requires.IsTrue(() => request.To >= request.From, "To date must be greater than From date");
        Requires.IsTrue(() => request.From <= DateTime.Now, "From date must be less than Today");
        Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");

        ///////////////////////////////////
        ///// Vehicle Categories
        ///////////////////////////////////
        var auUserBookingVehicleTypes = await this._repo.GetAuditIssuanceSettingsVehicleCategories(request.Search ?? "",
            request.From, request.To, request.Page, request.PageSize, cancellationToken);
        int totalauUserBookingVehicleTypes =
            await this._repo.GetTotal(request.Search ?? "", request.From, request.To, cancellationToken);

        // Extract the list of Booking Rule
        var bookingVehicleTypes = await this._repo.GetBookingVehicleTypes(cancellationToken);

        var history = new IssuanceSettingsVehicleCategoriesChangeHistory() {
            From = request.From,
            To = request.To,
            TotalCount = totalauUserBookingVehicleTypes
        };

        int tmpCount = 0;

        foreach (var t in bookingVehicleTypes) {
            var query = from v in auUserBookingVehicleTypes
                join c in bookingVehicleTypes on v.BookingVehicleTypeId equals c.BookingVehicleTypeId
                where v.BookingVehicleTypeId == t.BookingVehicleTypeId
                orderby v.AuTs descending
                select new {
                    AuBookingVehicleType = new AuditBookingVehicleTypeDto(
                        v.BookingVehicleTypeId,
                        v.UserId,
                        v.BookingVehicleType,
                        v.IsDeleted,
                        v.AuTs,
                        v.AuUsername,
                        v.AuActionType
                    ),
                    c.BookingVehicleTypeName
                };

            var pagedResults = query.ToList(); // Execute the in-memory query

            for (int i = 0; i < pagedResults.Count; i++) {
                if (tmpCount == request.PageSize) {
                    break;
                }

                var b = pagedResults[i]; // Current Value Item
                var previousB = (i + 1 < pagedResults.Count) ? pagedResults[i + 1] : null;

                ChangeType type = ChangeType.Unknown;

                switch (b.AuBookingVehicleType.AuActionType) {
                    case 'I': type = ChangeType.Created; break;
                    case 'U': type = ChangeType.Updated; break;
                    case 'D': type = ChangeType.Deleted; break;
                }

                var (currentValueList, previousValueList) =
                    (new Dictionary<string, string>(), new Dictionary<string, string>());

                if (previousB != null) {
                    var result =
                        await this._repo.ValidateChange(b.AuBookingVehicleType, previousB.AuBookingVehicleType);
                    (currentValueList, previousValueList) = (result.Item1, result.Item2);
                }
                else {
                    // if there is no previous value
                    // Check from '<= From Date' if they have one value. 
                    // Otherwise, set default
                    var previousOne = await this._repo.GetPreviousValueOne(request.Search ?? "", t.BookingVehicleTypeId,
                        request.From, cancellationToken);
                    AuditBookingVehicleTypeDto defaultValue = new AuditBookingVehicleTypeDto(t.BookingVehicleTypeId,
                        b.AuBookingVehicleType.UserId, Constants.Missing, null, null, null, null);

                    var result = await this._repo.ValidateChange(b.AuBookingVehicleType, previousOne ?? defaultValue);
                    (currentValueList, previousValueList) = (result.Item1, result.Item2);
                }

                history.Changes.Add(new IssuanceSettingsVehicleCategoriesChange() {
                    Type = type,
                    BookingVehicleTypeId =
                        (b != null && b.AuBookingVehicleType != null) ? b.AuBookingVehicleType.BookingVehicleTypeId : 0,
                    BookingVehicleType = (b != null) ? b.BookingVehicleTypeName : "",
                    PreviousValue = previousValueList,
                    CurrentValue = currentValueList,
                    CreatedTime = (b != null && b.AuBookingVehicleType != null) ? b.AuBookingVehicleType.AuTs : null,
                    ChangedBy = (b != null && b.AuBookingVehicleType != null &&
                                 b.AuBookingVehicleType.AuUsername != null)
                        ? b.AuBookingVehicleType.AuUsername
                        : ""
                });

                tmpCount++;
            }
        }

        return new GetIssuanceSettingsVehicleCategoriesHistoryResponse(history);
        //throw new NotImplementedException();
    }
}