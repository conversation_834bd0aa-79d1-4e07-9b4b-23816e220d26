﻿using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Features.GetIssuanceSettingsVehicleCategoriesHistory;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Runtime.InteropServices.JavaScript;

namespace Cartrack.Fleet.Audit.IO.Http;

public partial class AuditController {
    [HttpPost]
    [Route("issuance/settings/vehicle-categories")]
    public async Task<ActionResult<GetIssuanceSettingsVehicleCategoriesHistoryResponse>>
        GetIssuanceSettingsVehicleCategoriesHistory(
            [FromBody] GetIssuanceSettingsVehicleCategoriesHistoryRequest request) {
        var response = await this._mediator.Send(request);
        return response
            .ToContentResult<GetIssuanceSettingsVehicleCategoriesHistoryResponse,
                IssuanceSettingsVehicleCategoriesChangeHistory>(this.HttpContext);
    }
}