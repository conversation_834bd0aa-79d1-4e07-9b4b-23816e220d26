﻿using Cartrack.AppHost;
using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Common;
using MediatR;
using Cartrack.EFCore.Models.Fleet;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Index.Quadtree;
using Cartrack.Fleet.Audit.Infrastructure;


namespace Cartrack.Fleet.Audit.Features.GetClientUsersHistory;

public class GetClientUsersHistoryHandler(IClientUserAuditRepository repo)
    : IRequestHandler<GetClientUsersHistoryRequest, GetClientUsersHistoryResponse> {
    private readonly IClientUserAuditRepository _repo = repo;

    public async Task<GetClientUsersHistoryResponse> Handle(GetClientUsersHistoryRequest request,
        CancellationToken cancellationToken) {
        //Step1: Always validate the request contains correct data
        Requires.IsTrue(() => request.To >= request.From, "To date must be greater than From date");
        Requires.IsTrue(() => request.From <= DateTime.Now, "From date must be less than Today");
        Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");

        var clientUsers = await this._repo.GetClientUsers(null, cancellationToken);

        var auClientUsers = await this._repo.GetAuditClientUsers(null, request.Search ?? "", request.From, request.To,
            request.Page, request.PageSize, cancellationToken);
        int totalAuClientUsers =
            await this._repo.GetTotal(null, request.Search ?? "", request.From, request.To, cancellationToken);

        // Extract the list of Client User
        var clientUserIds = auClientUsers.Select(v => v.ClientUserId).Distinct().ToList();

        // This is for initialization
        var history =
            new ClientUsersChangeHistory() {
                From = request.From,
                To = request.To,
                TotalCount = totalAuClientUsers
            };

        int tmpCount = 0;

        foreach (var t in clientUserIds) {
            var query = from v in auClientUsers
                where v.ClientUserId == t
                orderby v.AuTs descending
                select new {
                    AuClientUser = new ClientUserLinkDepartmentAndSettingsDto(
                        v.UserId,
                        v.ClientUserId,
                        v.UserName,
                        v.CellNumber,
                        v.Email,
                        v.IsLocked,
                        v.IsRole,
                        v.ClientUserRoleName,
                        v.AuTs,
                        v.AuUsername,
                        v.AuActionType,
                        v.AuClientUserSetting,
                        v.AuClientUserDepartment
                    )
                };

            var pagedResults = query.ToList(); // Execute the in-memory query

            for (int i = 0; i < pagedResults.Count; i++) {
                if (tmpCount == request.PageSize) {
                    break;
                }

                var b = pagedResults[i]; // Current Value Item
                var previousB = (i + 1 < pagedResults.Count) ? pagedResults[i + 1] : null;

                ChangeType type = b.AuClientUser.AuActionType switch {
                    'I' => ChangeType.Created,
                    'U' => ChangeType.Updated,
                    'D' => ChangeType.Deleted,
                    _ => ChangeType.Unknown
                };

                Dictionary<string, string> currentValueList;
                Dictionary<string, string> previousValueList;

                if (previousB != null) {
                    var result = await this._repo.ValidateChange(b.AuClientUser, previousB.AuClientUser);
                    currentValueList = result.Item1;
                    previousValueList = result.Item2;
                }
                else {
                    // if there is no previous value
                    // Check from '<= From Date' if they have one value. 
                    // Otherwise, set default
                    var previousOne =
                        await this._repo.GetPreviousValueOne(request.Search ?? "", t, request.From, cancellationToken);
                    ClientUserLinkDepartmentAndSettingsDto defaultValue =
                        new ClientUserLinkDepartmentAndSettingsDto(b.AuClientUser.UserId, t, Constants.Missing,
                            Constants.Missing, Constants.Missing, Constants.Missing, Constants.Missing,
                            Constants.Missing, null, null, null, null, null);

                    var result = await this._repo.ValidateChange(b.AuClientUser, previousOne ?? defaultValue);
                    currentValueList = result.Item1;
                    previousValueList = result.Item2;
                }

                history.Changes.Add(new ClientUserChange() {
                    Type = type,
                    UserId = b.AuClientUser.UserId,
                    ClientUserId = (b.AuClientUser != null) ? b.AuClientUser.ClientUserId : String.Empty,
                    UserName = b.AuClientUser is { UserName: not null } ? b.AuClientUser.UserName : "",
                    PreviousValue = previousValueList,
                    CurrentValue = currentValueList,
                    CreatedTime = b.AuClientUser?.AuTs,
                    ChangedBy = b.AuClientUser is { AuUsername: not null } ? b.AuClientUser.AuUsername : ""
                });

                tmpCount++;
            }
        }

        return new GetClientUsersHistoryResponse(history);
        //throw new NotImplementedException();
    }
}