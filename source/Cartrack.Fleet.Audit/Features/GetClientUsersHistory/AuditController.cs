﻿using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Features.GetClientUsersHistory;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Audit.IO.Http;

public partial class AuditController {
    [HttpPost]
    [Route("users")]
    public async Task<ActionResult<GetClientUsersHistoryResponse>> GetClientUsersHistory(
        [FromBody] GetClientUsersHistoryRequest request) {
        var response = await this._mediator.Send(request);
        return response.ToContentResult<GetClientUsersHistoryResponse, ClientUsersChangeHistory>(this.HttpContext);
    }
}