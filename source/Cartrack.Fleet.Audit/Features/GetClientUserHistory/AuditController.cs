﻿using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Features.GetClientUserHistory;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices.JavaScript;

namespace Cartrack.Fleet.Audit.IO.Http;

public partial class AuditController {
    [HttpGet]
    [Route("users/{id}")]
    public async Task<ActionResult<GetClientUserHistoryResponse>> GetClientUserHistory(string id, DateTime fromDate,
        DateTime toDate, int page, int pageSize) {
        var updatedRequest = new GetClientUserHistoryRequest(id, fromDate, toDate, page, pageSize);

        var response = await this._mediator.Send(updatedRequest);
        return response.ToContentResult<GetClientUserHistoryResponse, ClientUserChangeHistory>(this.HttpContext);
    }
}