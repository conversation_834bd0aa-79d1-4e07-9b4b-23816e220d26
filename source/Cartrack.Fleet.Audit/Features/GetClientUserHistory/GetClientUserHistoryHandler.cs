﻿using Cartrack.AppHost;
using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Common;
using MediatR;

namespace Cartrack.Fleet.Audit.Features.GetClientUserHistory;

public class GetClientUserHistoryHandler(IClientUserAuditRepository repo)
    : IRequestHandler<GetClientUserHistoryRequest, GetClientUserHistoryResponse> {
    private readonly IClientUserAuditRepository _repo = repo;

    public async Task<GetClientUserHistoryResponse> Handle(GetClientUserHistoryRequest request,
        CancellationToken cancellationToken) {
        //Step1: Always validate the request contains correct data
        //Requires.NotNullOrEmpty(request.User, nameof(request.User));
        Requires.IsTrue(() => request.To >= request.From, "To date must be greater than From date");
        Requires.IsTrue(() => request.From <= DateTime.Now, "From date must be less than Today");
        Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");

        var clientUsers = await this._repo.GetClientUsers(request.ClientUserId, cancellationToken);

        var auClientUsers = await this._repo.GetAuditClientUsers(request.ClientUserId, "", request.From, request.To,
            request.Page, request.PageSize, cancellationToken);
        int totalAuClientUsers =
            await this._repo.GetTotal(request.ClientUserId, "", request.From, request.To, cancellationToken);

        // This is for initialization
        var history = new ClientUserChangeHistory() {
            From = request.From,
            To = request.To,
            UserId = 2,
            ClientUserId = request.ClientUserId,
            UserName = clientUsers.FirstOrDefault()?.UserName,
            TotalCount = totalAuClientUsers
        };

        int tmpCount = 0;

        var query = from v in auClientUsers
            where v.ClientUserId == request.ClientUserId
            orderby v.AuTs descending
            select new {
                AuClientUser = new ClientUserLinkDepartmentAndSettingsDto(
                    v.UserId,
                    v.ClientUserId,
                    v.UserName,
                    v.CellNumber,
                    v.Email,
                    v.IsLocked,
                    v.IsRole,
                    v.ClientUserRoleName,
                    v.AuTs,
                    v.AuUsername,
                    v.AuActionType,
                    v.AuClientUserSetting,
                    v.AuClientUserDepartment
                )
            };

        var pagedResults = query.ToList(); // Execute the in-memory query

        for (int i = 0; i < pagedResults.Count; i++) {
            if (tmpCount == request.PageSize) {
                break;
            }

            var b = pagedResults[i]; // Current Value Item
            var previousB = (i + 1 < pagedResults.Count) ? pagedResults[i + 1] : null;

            ChangeType type = ChangeType.Unknown;

            switch (b.AuClientUser.AuActionType) {
                case 'I': type = ChangeType.Created; break;
                case 'U': type = ChangeType.Updated; break;
                case 'D': type = ChangeType.Deleted; break;
            }

            Dictionary<string, string> currentValueList;
            Dictionary<string, string> previousValueList;

            if (previousB != null) {
                var result = await this._repo.ValidateChange(b.AuClientUser, previousB.AuClientUser);
                currentValueList = result.Item1;
                previousValueList = result.Item2;
            }
            else {
                // if there is no previous value
                // Check from '<= From Date' if they have one value. 
                // Otherwise, set default
                var previousOne =
                    await this._repo.GetPreviousValueOne(request.ClientUserId, "", request.From, cancellationToken);
                ClientUserLinkDepartmentAndSettingsDto defaultValue =
                    new ClientUserLinkDepartmentAndSettingsDto(b.AuClientUser.UserId, request.ClientUserId, null, null,
                        null, null, null, null, null, null, null, null, null);

                var result = await this._repo.ValidateChange(b.AuClientUser, previousOne ?? defaultValue);
                currentValueList = result.Item1;
                previousValueList = result.Item2;
            }

            history.Changes.Add(new ClientUserChange() {
                Type = type,
                UserId = b.AuClientUser.UserId,
                ClientUserId = (b.AuClientUser != null) ? b.AuClientUser.ClientUserId : String.Empty,
                UserName = (b.AuClientUser != null && b.AuClientUser.UserName != null) ? b.AuClientUser.UserName : "",
                PreviousValue = previousValueList,
                CurrentValue = currentValueList,
                CreatedTime = (b.AuClientUser != null) ? b.AuClientUser.AuTs : null,
                ChangedBy = (b.AuClientUser != null && b.AuClientUser.AuUsername != null)
                    ? b.AuClientUser.AuUsername
                    : ""
            });

            tmpCount++;
        }

        return new GetClientUserHistoryResponse(history);
    }
}