﻿using Cartrack.AppHost;
using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Audit.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;

namespace Cartrack.Fleet.Audit.Features.GetLoginHistory;

public class GetLoginHistoryHandler(IClientUserAuditRepository clientUserRepo, ILoginHistoryRepository loginHistoryRepo)
    : IRequestHandler<GetLoginHistoryRequest, GetLoginHistoryResponse> {
    private readonly IClientUserAuditRepository _clientUserRepo = clientUserRepo;
    private readonly ILoginHistoryRepository _loginHistoryRepo = loginHistoryRepo;

    public async Task<GetLoginHistoryResponse> Handle(GetLoginHistoryRequest request,
        CancellationToken cancellationToken) {
        Requires.IsTrue(() => request.To >= request.From, "To date must be greater than From date");
        Requires.IsTrue(() => request.From <= DateTime.Now, "From date must be less than Today");
        Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");

        var noHistory = new LoginChangeHistory() {
            UserId = 0,
            ClientUserId = "",
            ClientUserName = "",
            TotalCount = 0,
            From = request.From,
            To = request.To
        };

        var clientUsers = await this._clientUserRepo.GetClientUsers(request.ClientUserId, cancellationToken);
        var clientUser = clientUsers.FirstOrDefault();

        switch (request.LoginResult) {
            case LoginResult.Success:

                var clientLoginHistories = await this._loginHistoryRepo.GetClientLoginHistories(request.ClientUserId,
                    request.LoginMode, request.Search, request.From, request.To, request.Page, request.PageSize,
                    cancellationToken);

                if (!clientLoginHistories.Any()) {
                    return new GetLoginHistoryResponse(noHistory);
                }

                var totalClientLoginHistories = await this._loginHistoryRepo.GetClientLoginHistoriesTotal(
                    request.ClientUserId, request.LoginMode, request.Search, request.From, request.To,
                    cancellationToken);
                var query = from v in clientLoginHistories
                    join c in clientUsers on v.SubClientId equals c.ClientUserId
                    where v.SubClientId == request.ClientUserId
                    orderby v.EventTs descending
                    select new {
                        ClientUserName = c.UserName, LoginHistory = v
                    };

                var pagedResults = query.ToList();

                var history = new LoginChangeHistory() {
                    UserId = clientLoginHistories.FirstOrDefault()!.ClientId,
                    ClientUserId = request.ClientUserId,
                    ClientUserName = clientUser?.UserName ?? "",
                    TotalCount = totalClientLoginHistories,
                    From = request.From,
                    To = request.To
                };

                for (int i = 0; i < request.PageSize; i++) {
                    if (i >= pagedResults.Count) {
                        break;
                    }

                    var b = pagedResults[i];

                    history.Changes.Add(new LoginChange() {
                        ClientId = (b != null && b.LoginHistory != null) ? b.LoginHistory.ClientId : 0,
                        ClientUserId = (b != null && b.LoginHistory != null) ? b.LoginHistory.SubClientId : "",
                        ClientUserName = (b != null && b.ClientUserName != null) ? b.ClientUserName : "",
                        ClientLoginSourceIP = (b != null && b.LoginHistory != null) ? b.LoginHistory.LoginSourceIp : "",
                        ClientDetails = (b != null && b.LoginHistory != null) ? b.LoginHistory.ClientDetails : "",
                        Reason = (b != null && b.LoginHistory != null) ? b.LoginHistory.Reason : "",
                        EventTs = (b != null && b.LoginHistory != null) ? b.LoginHistory.EventTs : null,
                    });
                }

                return new GetLoginHistoryResponse(history);
                break;

            case LoginResult.Fail:
                var clientLoginHistoryFailures = await this._loginHistoryRepo.GetClientLoginHistoryFailures(
                    request.ClientUserId, request.LoginMode, request.Search, request.From, request.To, request.Page,
                    request.PageSize, cancellationToken);

                if (!clientLoginHistoryFailures.Any()) {
                    return new GetLoginHistoryResponse(noHistory);
                }

                var totalClientLoginHistoriesFailures =
                    await this._loginHistoryRepo.GetClientLoginHistoriesFailuresTotal(request.ClientUserId,
                        request.LoginMode, request.Search, request.From, request.To, cancellationToken);
                var query1 = from v in clientLoginHistoryFailures
                    where v.SubClientId == request.ClientUserId
                    orderby v.EventTs descending
                    select new {
                        LoginHistory = v
                    };

                var pagedResults1 = query1.ToList();

                var history1 = new LoginChangeHistory() {
                    UserId = clientLoginHistoryFailures.FirstOrDefault()!.ClientId,
                    ClientUserId = request.ClientUserId,
                    ClientUserName = clientUser?.UserName ?? "",
                    TotalCount = totalClientLoginHistoriesFailures,
                    From = request.From,
                    To = request.To
                };

                for (int i = 0; i < request.PageSize; i++) {
                    if (i >= pagedResults1.Count()) {
                        break;
                    }

                    var b = pagedResults1[i];

                    if (b != null && b.LoginHistory != null) {
                        history1.Changes.Add(new LoginChange() {
                            ClientId = b.LoginHistory?.ClientId ?? 0,
                            ClientUserId = b?.LoginHistory?.SubClientId ?? "",
                            ClientUserName =
                                (b.LoginHistory.SubUsername != "")
                                    ? b.LoginHistory.SubUsername
                                    : b.LoginHistory.ClientUserName,
                            ClientLoginSourceIP = b?.LoginHistory.LoginSourceIp ?? "",
                            ClientDetails = b?.LoginHistory.ClientDetails ?? "",
                            Reason = b?.LoginHistory.Reason ?? "Failed",
                            EventTs = b?.LoginHistory?.EventTs ?? null
                        });
                    }
                }

                return new GetLoginHistoryResponse(history1);
                break;
            default:
                break;
        }

        return new GetLoginHistoryResponse(noHistory);
    }
}