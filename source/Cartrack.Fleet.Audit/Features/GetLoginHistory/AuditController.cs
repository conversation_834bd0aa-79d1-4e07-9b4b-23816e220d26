﻿using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Features.GetLoginHistory;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Audit.IO.Http;

public partial class AuditController {
    [HttpGet]
    [Route("login/{clientUserId}")]
    public async Task<ActionResult<GetLoginHistoryResponse>> GetLoginHistory(string clientUserId, string? search,
        LoginMode loginMode, LoginResult loginResult, DateTime fromDate, DateTime toDate, int page, int pageSize) {
        var updatedRequest = new GetLoginHistoryRequest(clientUserId, search, loginMode, loginResult, fromDate, toDate,
            page, pageSize);

        var response = await this._mediator.Send(updatedRequest);
        return response.ToContentResult<GetLoginHistoryResponse, LoginChangeHistory>(this.HttpContext);
    }
}