﻿using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Features.GetVehiclesHistory;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Runtime.InteropServices.JavaScript;

namespace Cartrack.Fleet.Audit.IO.Http;

public partial class AuditController {
    [HttpPost]
    [Route("vehicles")]
    public async Task<ActionResult<GetVehiclesHistoryResponse>> GetVehiclesHistory(
        [FromBody] GetVehiclesHistoryRequest request) {
        var response = await this._mediator.Send(request);
        return response.ToContentResult<GetVehiclesHistoryResponse, VehiclesChangeHistory>(this.HttpContext);
    }
}