﻿using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Features.GetIssuanceSettingsRulesHistory;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Runtime.InteropServices.JavaScript;

namespace Cartrack.Fleet.Audit.IO.Http;

public partial class AuditController {
    [HttpPost]
    [Route("issuance/settings/rule")]
    public async Task<ActionResult<GetIssuanceSettingsRulesHistoryResponse>> GetIssuanceSettingsRulesHistory(
        [FromBody] GetIssuanceSettingsRulesHistoryRequest request) {
        var response = await this._mediator.Send(request);
        return response.ToContentResult<GetIssuanceSettingsRulesHistoryResponse, IssuanceSettingsRulesChangeHistory>(
            this.HttpContext);
    }
}