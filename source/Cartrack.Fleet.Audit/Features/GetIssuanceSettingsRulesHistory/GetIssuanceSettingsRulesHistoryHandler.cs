﻿using Cartrack.AppHost;
using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using MediatR;
using Cartrack.EFCore.Models.Fleet;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Index.Quadtree;
using Serilog.Sinks.Graylog.Core.Extensions;


namespace Cartrack.Fleet.Audit.Features.GetIssuanceSettingsRulesHistory;

public class GetIssuanceSettingsRulesHistoryHandler : IRequestHandler<GetIssuanceSettingsRulesHistoryRequest,
    GetIssuanceSettingsRulesHistoryResponse> {
    private readonly IIssuanceSettingsRulesAuditRepository _repo;

    public GetIssuanceSettingsRulesHistoryHandler(IIssuanceSettingsRulesAuditRepository repo) {
        _repo = repo;
    }

    public async Task<GetIssuanceSettingsRulesHistoryResponse> Handle(GetIssuanceSettingsRulesHistoryRequest request,
        CancellationToken cancellationToken) {
        //Step1: Always validate the request contains correct data
        //Requires.NotNullOrEmpty(request.Search, nameof(request.Search));
        Requires.IsTrue(() => request.To >= request.From, "To date must be greater than From date");
        Requires.IsTrue(() => request.From <= DateTime.Now, "From date must be less than Today");
        Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");

        ///////////////////////////////////
        ///// User Booking Rules
        ///////////////////////////////////
        var auUserBookingRules = await this._repo.GetAuditIssuanceSettingsRules(request.Search ?? "", request.From,
            request.To, request.Page, request.PageSize, cancellationToken);
        int totalauUserBookingRules =
            await this._repo.GetTotal(request.Search ?? "", request.From, request.To, cancellationToken);

        // Extract the list of Booking Rule
        var bookingRules = await this._repo.GetBookingRules(cancellationToken);

        var history = new IssuanceSettingsRulesChangeHistory() {
            From = request.From,
            To = request.To,
            TotalCount = totalauUserBookingRules
        };

        int tmpCount = 0;

        foreach (var t in bookingRules) {
            var query = from v in auUserBookingRules
                join c in bookingRules on v.BookingRuleId equals c.BookingRuleId
                where v.BookingRuleId == t.BookingRuleId
                orderby v.AuTs descending
                select new {
                    AuBookingRule = new AuditIssuanceSettingsRulesDto(
                        v.UserId,
                        v.BookingRuleId,
                        v.Value,
                        v.Unit,
                        v.Status,
                        v.AuTs,
                        v.AuUsername,
                        v.AuActionType
                    ),
                    c.BookingRuleName
                };

            var pagedResults = query.ToList(); // Execute the in-memory query

            for (int i = 0; i < pagedResults.Count; i++) {
                if (tmpCount == request.PageSize) {
                    break;
                }

                var b = pagedResults[i]; // Current Value Item
                var previousB = (i + 1 < pagedResults.Count) ? pagedResults[i + 1] : null;

                ChangeType type = ChangeType.Unknown;

                switch (b.AuBookingRule.AuActionType) {
                    case 'I': type = ChangeType.Created; break;
                    case 'U': type = ChangeType.Updated; break;
                    case 'D': type = ChangeType.Deleted; break;
                }

                Dictionary<string, string> currentValueList = new Dictionary<string, string>();
                Dictionary<string, string> previousValueList = new Dictionary<string, string>();

                if (previousB != null) {
                    var result = await this._repo.ValidateChange(b.AuBookingRule, previousB.AuBookingRule);
                    currentValueList = result.Item1;
                    previousValueList = result.Item2;
                }
                else {
                    // if there is no previous value
                    // Check from '<= From Date' if they have one value. 
                    // Otherwise, set default
                    var previousOne = await this._repo.GetPreviousValueOne(request.Search ?? "", t.BookingRuleId,
                        request.From, cancellationToken);
                    AuditIssuanceSettingsRulesDto defaultValue = new AuditIssuanceSettingsRulesDto(
                        b.AuBookingRule.UserId, t.BookingRuleId, Constants.Missing, Constants.Missing, null, null, null,
                        null);

                    var result = await this._repo.ValidateChange(b.AuBookingRule, previousOne ?? defaultValue);
                    currentValueList = result.Item1;
                    previousValueList = result.Item2;
                }

                history.Changes.Add(new IssuanceSettingsRulesChange() {
                    Type = type,
                    BookingRuleId = (b != null && b.AuBookingRule != null) ? b.AuBookingRule.BookingRuleId : 0,
                    BookingRuleName = (b != null) ? b.BookingRuleName : "",
                    PreviousValue = previousValueList,
                    CurrentValue = currentValueList,
                    CreatedTime = (b != null && b.AuBookingRule != null) ? b.AuBookingRule.AuTs : null,
                    ChangedBy = (b != null && b.AuBookingRule != null && b.AuBookingRule.AuUsername != null)
                        ? b.AuBookingRule.AuUsername
                        : ""
                });

                tmpCount++;
            }
        }

        return new GetIssuanceSettingsRulesHistoryResponse(history);
        //throw new NotImplementedException();
    }
}