﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.CT;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Features.GetLoginHistory;
using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Audit.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using System.Runtime.InteropServices;

namespace Cartrack.Fleet.Audit.Features.GetLoginsHistory;

public class GetLoginsHistoryHandler(
    IClientUserAuditRepository clientUserRepo,
    ILoginHistoryRepository loginHistoryRepo) : IRequestHandler<GetLoginsHistoryRequest, GetLoginsHistoryResponse> {
    private readonly IClientUserAuditRepository _clientUserRepo = clientUserRepo;
    private readonly ILoginHistoryRepository _loginHistoryRepo = loginHistoryRepo;

    public async Task<GetLoginsHistoryResponse> Handle(GetLoginsHistoryRequest request,
        CancellationToken cancellationToken) {
        Requires.IsTrue(() => request.To >= request.From, "To date must be greater than From date");
        Requires.IsTrue(() => request.From <= DateTime.Now, "From date must be less than Today");
        Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");

        var noHistory = new LoginsChangeHistory() {
            TotalCount = 0,
            From = request.From,
            To = request.To
        };

        var clientUsers = await this._clientUserRepo.GetClientUsers(null, cancellationToken);

        switch (request.loginResult) {
            case LoginResult.Success:
                var clientLoginHistories = await this._loginHistoryRepo.GetClientLoginHistories(null, request.loginMode,
                    request.Search, request.From, request.To, request.Page, request.PageSize, cancellationToken);

                var totalClientLoginHistories = await this._loginHistoryRepo.GetClientLoginHistoriesTotal(null,
                    request.loginMode, request.Search, request.From, request.To, cancellationToken);

                var query1 = from v in clientLoginHistories
                    join c in clientUsers on v.SubClientId equals c.ClientUserId
                    orderby v.EventTs descending
                    select new {
                        ClientId = v.ClientId,
                        SubClientId = c.ClientUserId ?? "",
                        ClientUserName = c.UserName ?? "",
                        LoginSourceIp = v.LoginSourceIp ?? "",
                        ClientDetails = v.ClientDetails ?? "",
                        Reason = v.Reason ?? "",
                        EventTs = v.EventTs
                    };

                var pagedResults1 = query1.ToList();

                var history1 = new LoginsChangeHistory() {
                    TotalCount = totalClientLoginHistories,
                    From = request.From,
                    To = request.To
                };

                for (int i = 0; i < request.PageSize; i++) {
                    if (i >= pagedResults1.Count) {
                        break;
                    }

                    var b = pagedResults1[i];

                    history1.Changes.Add(new LoginChange() {
                        ClientId = b?.ClientId ?? 0,
                        ClientUserId = (b != null) ? b.SubClientId : "",
                        ClientUserName = (b != null) ? b.ClientUserName : "",
                        ClientLoginSourceIP = (b != null) ? b.LoginSourceIp : "",
                        ClientDetails = (b != null) ? b.ClientDetails : "",
                        Reason = (b != null) ? b.Reason : "",
                        EventTs = (b != null) ? DateTime.SpecifyKind(b.EventTs, DateTimeKind.Unspecified) : null
                    });
                }

                return new GetLoginsHistoryResponse(history1);
                break;

            case LoginResult.Fail:
                var clientLoginHistoryFailures = await this._loginHistoryRepo.GetClientLoginHistoryFailures(null,
                    request.loginMode, request.Search, request.From, request.To, request.Page, request.PageSize,
                    cancellationToken);

                if (!clientLoginHistoryFailures.Any()) {
                    return new GetLoginsHistoryResponse(noHistory);
                }

                var totalClientLoginHistoryFailures =
                    await this._loginHistoryRepo.GetClientLoginHistoriesFailuresTotal(null, request.loginMode,
                        request.Search, request.From, request.To, cancellationToken);

                var query = from v in clientLoginHistoryFailures
                    orderby v.EventTs descending
                    select new {
                        ClientId = v.ClientId,
                        SubClientId = v.SubClientId ?? "",
                        ClientUserName = ((v.SubUsername != "") ? v.SubUsername : v.ClientUserName),
                        LoginSourceIp = v.LoginSourceIp ?? "",
                        ClientDetails = v.ClientDetails ?? "",
                        Reason = v.Reason ?? "",
                        EventTs = v.EventTs
                    };

                var pagedResults = query.ToList();

                var history = new LoginsChangeHistory() {
                    TotalCount = totalClientLoginHistoryFailures,
                    From = request.From,
                    To = request.To
                };

                for (int i = 0; i < request.PageSize; i++) {
                    if (i >= pagedResults.Count) {
                        break;
                    }

                    var b = pagedResults[i];

                    history.Changes.Add(new LoginChange() {
                        ClientId = b?.ClientId ?? 0,
                        ClientUserId = (b != null) ? b.SubClientId : "",
                        ClientUserName = (b != null) ? b.ClientUserName : "",
                        ClientLoginSourceIP = (b != null) ? b.LoginSourceIp : "",
                        ClientDetails = (b != null) ? b.ClientDetails : "",
                        Reason = (b != null) ? b.Reason : "",
                        EventTs = (b != null) ? DateTime.SpecifyKind(b.EventTs, DateTimeKind.Unspecified) : null
                    });
                }

                return new GetLoginsHistoryResponse(history);
                break;
            default:
                // nothing to do here
                break;
        }

        return new GetLoginsHistoryResponse(noHistory);
    }
    //public async Task<GetLoginsHistoryResponse> Handle(GetLoginsHistoryRequest request, CancellationToken cancellationToken) {

    //    //Step1: Always validate the request contains correct data
    //    Requires.IsTrue(() => request.To >= request.From, "To date must be greater than From date");
    //    Requires.IsTrue(() => request.From <= DateTime.Now, "From date must be less than Today");
    //    Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");

    //    // SET DEFAULT IF NO HISTORY
    //    var no_history = new LoginsChangeHistory() {
    //        TotalCount = 0,
    //        From = request.From,
    //        To = request.To
    //    };

    //    // RESULT : LOGIN SUCCESS
    //    if (request.loginResult == LoginResult.Success) {
    //        var queryable = _fleetDbContext.ClientLoginHistories
    //                                .GroupJoin(
    //                                    _fleetDbContext.ClientUsers,
    //                                    LH => LH.SubClientId, // LH = Login History table
    //                                    CU => CU.ClientUserId, // CU = ClientUser table
    //                                    (LH, CU) => new { LH, CU }
    //                                )
    //                                .SelectMany(
    //                                    x => x.CU.DefaultIfEmpty(),
    //                                    (x, CU) => new {
    //                                        x.LH.EventTs,
    //                                        x.LH.ClientId,
    //                                        x.LH.SubClientId,
    //                                        x.LH.LoginSourceIp,
    //                                        ClientDetails = x.LH.ClientDetails ?? "",
    //                                        ClientUserName = CU != null ? CU.UserName : ""
    //                                    }
    //                                );
    //        var qry = queryable;

    //        if (request.loginMode != LoginMode.All) {
    //            switch (request.loginMode) {
    //                case LoginMode.Web:
    //                    qry = qry.Where(p => !p.ClientDetails.Contains(DriverAppLoginText));
    //                    break;
    //                case LoginMode.DriverAppApi:
    //                    qry = qry.Where(p => p.ClientDetails.Contains(DriverAppLoginText));
    //                    break;
    //                default:
    //                    // nothing to do here...
    //                    break;
    //            }
    //        }

    //        if (!string.IsNullOrEmpty(request.Search)) {
    //            qry = qry.Where(p => p.ClientUserName.ToLower().Contains(request.Search.ToLower()));
    //        }

    //        qry = qry.Where(v => v.EventTs >= DateTime.SpecifyKind(request.From, DateTimeKind.Utc) 
    //                && v.EventTs <= DateTime.SpecifyKind(request.To, DateTimeKind.Utc)
    //                && (v.SubClientId != "" || v.SubClientId != null));

    //        var clientLoginHistory = await qry
    //                    .OrderByDescending(v => v.EventTs)
    //                    .Skip((request.Page - 1) * request.PageSize) // Skip records from previous pages
    //                    .Take(request.PageSize) // Take the records for the current page
    //                    .ToListAsync(); // Retrieve only matching vehicles

    //        var loginHistory = clientLoginHistory.FirstOrDefault();

    //        if (loginHistory is null) {
    //            return new GetLoginsHistoryResponse(no_history);
    //        }

    //        var totalClientLoginHistories = await qry.CountAsync();

    //        var query = from v in clientLoginHistory
    //                    join c in _ctDbContext.Users on v.ClientId equals c.UserId
    //                    orderby v.EventTs descending
    //                    select new {
    //                        ClientId = v.ClientId,
    //                        SubClientId = v.SubClientId ?? "",
    //                        ClientUserName = ((v.ClientUserName != "") ? v.ClientUserName : c.UserName),
    //                        LoginSourceIp = v.LoginSourceIp ?? "",
    //                        ClientDetails = v.ClientDetails ?? "",
    //                        EventTs = v.EventTs
    //                    };

    //        var pagedResults = query.ToList(); // Execute the in-memory query

    //        var history = new LoginsChangeHistory() {
    //            TotalCount = totalClientLoginHistories,
    //            From = request.From,
    //            To = request.To
    //        };

    //        for (int i = 0; i < request.PageSize; i++) {

    //            if (i >= pagedResults.Count) {
    //                // If we reach beyond the available list, break the loop
    //                break;
    //            }

    //            var b = pagedResults[i];

    //            history.Changes.Add(new LoginChange() {
    //                ClientId = (b != null) ? b.ClientId : 0,
    //                ClientUserId = (b != null) ? b.SubClientId : "",
    //                ClientUserName = (b != null) ? b.ClientUserName : "",
    //                ClientLoginSourceIP = (b != null) ? b.LoginSourceIp : "",
    //                ClientDetails = (b != null) ? b.ClientDetails : "",
    //                Reason = "Success",
    //                EventTs = (b != null) ? DateTime.SpecifyKind(b.EventTs, DateTimeKind.Unspecified) : null
    //            });
    //        }

    //        return new GetLoginsHistoryResponse(history);
    //    }

    //    ////////////////////////////
    //    /// LOGIN RESULT - FAIL
    //    ///////////////////////////
    //    if (request.loginResult == LoginResult.Fail) {
    //        var qry = _fleetDbContext.ClientLoginHistoryFailures.Where(p => true);

    //        if (request.loginMode != LoginMode.All) {
    //            switch (request.loginMode) {
    //                case LoginMode.Web:
    //                    qry = qry.Where(p => !(p.ClientDetails ?? "").Contains(DriverAppLoginText));
    //                    break;
    //                case LoginMode.DriverAppApi:
    //                    qry = qry.Where(p => (p.ClientDetails ?? "").Contains(DriverAppLoginText));
    //                    break;
    //                default:
    //                    // nothing to do here...
    //                    break;
    //            }
    //        }

    //        if (!string.IsNullOrEmpty(request.Search)) {
    //            qry = qry.Where(p => (p.Username ?? "").ToLower().Contains(request.Search.ToLower()));
    //        }

    //        qry = qry.Where(v => v.EventTs >= DateTime.SpecifyKind(request.From, DateTimeKind.Utc) 
    //                && v.EventTs <= DateTime.SpecifyKind(request.To, DateTimeKind.Utc)
    //                && (v.SubClientId != "" || v.SubClientId != null));

    //        var clientLoginHistory = await qry
    //                    .OrderByDescending(v => v.EventTs)
    //                    .Skip((request.Page - 1) * request.PageSize) // Skip records from previous pages
    //                    .Take(request.PageSize) // Take the records for the current page
    //                    .ToListAsync(); // Retrieve only matching vehicles

    //        var loginHistory = clientLoginHistory.FirstOrDefault();

    //        if (loginHistory is null) {
    //            return new GetLoginsHistoryResponse(no_history);
    //        }

    //        var totalClientLoginHistoriesFailures = await qry.CountAsync();

    //        var query = from v in clientLoginHistory
    //                    orderby v.EventTs descending
    //                    select new {
    //                        ClientId = v.ClientId,
    //                        SubClientId = v.SubClientId ?? "",
    //                        ClientUserName = ((v.SubUsername != "") ? v.SubUsername : v.Username),
    //                        LoginSourceIp = v.LoginSourceIp ?? "",
    //                        ClientDetails = v.ClientDetails ?? "",
    //                        Reason = v.Reason ?? "",
    //                        EventTs = v.EventTs
    //                    };

    //        var pagedResults = query.ToList(); // Execute the in-memory query

    //        var history = new LoginsChangeHistory() {
    //            TotalCount = totalClientLoginHistoriesFailures,
    //            From = request.From,
    //            To = request.To
    //        };

    //        for (int i = 0; i < request.PageSize; i++) {

    //            if (i >= pagedResults.Count) {
    //                // If we reach beyond the available list, break the loop
    //                break;
    //            }

    //            var b = pagedResults[i];

    //            history.Changes.Add(new LoginChange() {
    //                ClientId = (b != null) ? b.ClientId : 0,
    //                ClientUserId = (b != null) ? b.SubClientId : "",
    //                ClientUserName = (b != null) ? b.ClientUserName : "",
    //                ClientLoginSourceIP = (b != null) ? b.LoginSourceIp : "",
    //                ClientDetails = (b != null) ? b.ClientDetails : "",
    //                Reason = (b != null) ? b.Reason : "",
    //                EventTs = (b != null) ? DateTime.SpecifyKind(b.EventTs, DateTimeKind.Unspecified) : null
    //            });
    //        }

    //        return new GetLoginsHistoryResponse(history);
    //    }

    //    return new GetLoginsHistoryResponse(no_history);
    //}
}