﻿using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Features.GetLoginsHistory;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Audit.IO.Http;

public partial class AuditController {
    [HttpPost]
    [Route("login")]
    public async Task<ActionResult<GetLoginsHistoryResponse>> GetClientUsersHistory(
        [FromBody] GetLoginsHistoryRequest request) {
        var response = await this._mediator.Send(request);
        return response.ToContentResult<GetLoginsHistoryResponse, LoginsChangeHistory>(this.HttpContext);
    }
}