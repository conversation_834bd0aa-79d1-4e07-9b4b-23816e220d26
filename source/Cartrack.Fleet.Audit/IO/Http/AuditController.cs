﻿using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Audit.IO.Http;

[ApiController]
[Route("audit")]
public partial class AuditController : ControllerBase {
    private readonly ILogger<AuditController> _logger;
    private readonly IMediator _mediator;
    private readonly AppSettings _appSettings;

    public AuditController(ILogger<AuditController> logger, IMediator mediator, AppSettings appSettings) {
        this._logger = logger;
        this._mediator = mediator;
        this._appSettings = appSettings;
    }
}

public record GetVehicleHistoryHTTPRequest(string Search, DateTime From, DateTime To, int Page, int PageSize);

public record GetClientUserHistoryHTTPRequest(string Search, DateTime From, DateTime To, int Page, int PageSize);

public record GetLoginHistoryHTTPRequest(
    string Search,
    LoginMode LoginMode,
    LoginResult LoginResult,
    DateTime From,
    DateTime To,
    int Page,
    int PageSize);