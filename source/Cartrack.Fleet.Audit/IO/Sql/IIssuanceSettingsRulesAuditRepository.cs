﻿using Cartrack.Fleet.Audit.Domain;

namespace Cartrack.Fleet.Audit.Infrastructure;

public interface IIssuanceSettingsRulesAuditRepository {
    Task<List<AuditIssuanceSettingsRulesDto>> GetAuditIssuanceSettingsRules(string Search, DateTime From, DateTime To,
        int Page, int PageSize, CancellationToken token);

    Task<AuditIssuanceSettingsRulesDto> GetPreviousValueOne(string Search, long BookingRuleId, DateTime From,
        CancellationToken token);

    Task<int> GetTotal(string Search, DateTime From, DateTime To, CancellationToken token);
    Task<List<BookingRulesDto>> GetBookingRules(CancellationToken token);

    Task<(Dictionary<string, string>, Dictionary<string, string>)> ValidateChange(
        AuditIssuanceSettingsRulesDto AuBookingRuleCurrentValue,
        AuditIssuanceSettingsRulesDto AuBookingRulePreviousValue);
}

public record AuditIssuanceSettingsRulesDto(
    long? UserId,
    long? BookingRuleId,
    string? Value,
    string? Unit,
    bool? Status,
    DateTime? AuTs,
    string? AuUsername,
    char? AuActionType);

public record BookingRulesDto(long BookingRuleId, string? BookingRuleName);