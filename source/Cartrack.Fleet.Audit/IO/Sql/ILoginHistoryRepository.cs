﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using MediatR;

namespace Cartrack.Fleet.Audit.IO.Sql;

public interface ILoginHistoryRepository {
    public Task<List<ClientLoginHistoryDto>> GetClientLoginHistories(string? clientUserId, LoginMode loginMode,
        string? search, DateTime fromDate, DateTime toDate, int page, int pageSize, CancellationToken token);

    Task<int> GetClientLoginHistoriesTotal(string? clientUserId, LoginMode loginMode, string? search, DateTime fromDate,
        DateTime toDate, CancellationToken token);

    Task<List<ClientLoginHistoryFailuresDto>> GetClientLoginHistoryFailures(string? clientUserId, LoginMode loginMode,
        string? search, DateTime fromDate, DateTime toDate, int page, int pageSize, CancellationToken token);

    Task<int> GetClientLoginHistoriesFailuresTotal(string? clientUserId, LoginMode loginMode, string? search,
        DateTime fromDate, DateTime toDate, CancellationToken token);
}

public record ClientLoginHistoryDto(
    long ClientId,
    string? SubClientId,
    string ClientUserName,
    string LoginSourceIp,
    string ClientDetails,
    string Reason,
    DateTime EventTs);

public record ClientLoginHistoryFailuresDto(
    long ClientId,
    string? SubClientId,
    string ClientUserName,
    string LoginSourceIp,
    string ClientDetails,
    string Reason,
    DateTime EventTs,
    string SubUsername);