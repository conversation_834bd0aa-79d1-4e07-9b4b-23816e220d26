﻿using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query.SqlExpressions;
using System.Reflection.Metadata;

namespace Cartrack.Fleet.Audit.IO.Sql;

public class IssuanceSettingsPurposeAuditRepository(AppPoolDbContext poolDbContext)
    : IIssuanceSettingsPurposeAuditRepository {
    public async Task<List<AuditIssuanceSettingsPurposeDto>> GetAuditIssuanceSettingsPurpose(string Search,
        DateTime From, DateTime To, int Page, int PageSize, CancellationToken token) {
        var SearchLowerText = Search.ToLower();

        var query = from au in poolDbContext.AuBookingPurposes
            join br in poolDbContext.BookingPurposes on au.BookingPurposeId equals br.BookingPurposeId into
                bookingPurposeJoin
            from br in bookingPurposeJoin.DefaultIfEmpty()
            where au.AuTs >= DateTime.SpecifyKind(From, DateTimeKind.Utc) && au.AuTs <=
                                                                          DateTime.SpecifyKind(To, DateTimeKind.Utc)
                                                                          && (
                                                                              string.IsNullOrEmpty(SearchLowerText) ||
                                                                              (au.AuUsername != null &&
                                                                               au.AuUsername.ToLower()
                                                                                   .Contains(SearchLowerText)) ||
                                                                              (br.BookingPurpose1 != null &&
                                                                               br.BookingPurpose1.ToLower()
                                                                                   .Contains(SearchLowerText))
                                                                          )
            orderby au.AuTs descending
            select new AuditIssuanceSettingsPurposeDto(
                au.UserId,
                au.BookingPurposeId,
                au.BookingPurpose,
                au.IsDeleted,
                au.AuTs,
                au.AuUsername,
                au.AuActionType,
                (from x in poolDbContext.AuBookingPurposeVehicleTypes
                    join a in poolDbContext.BookingVehicleTypes on x.BookingVehicleTypeId equals a.BookingVehicleTypeId
                        into vehicleTypeJoin
                    from c in vehicleTypeJoin.DefaultIfEmpty()
                    where x.BookingPurposeId == au.BookingPurposeId
                          && x.AuTs >= au.AuTs.Value.AddSeconds(-2)
                          && x.AuTs <= au.AuTs.Value.AddSeconds(2)
                          && x.AuActionType == 'I'
                    orderby x.AuTs descending
                    select new AuditIssuanceSettingsPurposeVehicleTypesDto(
                        x.BookingPurposeId,
                        x.BookingVehicleTypeId,
                        c.BookingVehicleType1
                    )).ToList()
            );

        var auBookingPurpose = await query
            .Skip((Page - 1) * PageSize) // Skip records from previous pages
            .Take(PageSize + 1) // Take the records for the current page
            .ToListAsync(token);

        return auBookingPurpose;
    }

    public async Task<int> GetTotal(string Search, DateTime From, DateTime To, CancellationToken token) {
        var SearchLowerText = Search.ToLower();

        var query = from au in poolDbContext.AuBookingPurposes
            join br in poolDbContext.BookingPurposes on au.BookingPurposeId equals br.BookingPurposeId into
                bookingPurposeJoin
            from br in bookingPurposeJoin.DefaultIfEmpty()
            where au.AuTs >= DateTime.SpecifyKind(From, DateTimeKind.Utc) && au.AuTs <=
                                                                          DateTime.SpecifyKind(To, DateTimeKind.Utc)
                                                                          && (
                                                                              string.IsNullOrEmpty(SearchLowerText) ||
                                                                              (au.AuUsername != null &&
                                                                               au.AuUsername.ToLower()
                                                                                   .Contains(SearchLowerText)) ||
                                                                              (br.BookingPurpose1 != null &&
                                                                               br.BookingPurpose1.ToLower()
                                                                                   .Contains(SearchLowerText))
                                                                          )
            orderby au.AuTs descending
            select au;

        int totalAuBookingPurpose = await query.CountAsync(token);

        return totalAuBookingPurpose;
    }

    public async Task<List<BookingPurposeDto>> GetBookingPurposes(CancellationToken token) {
        var bookingPurposes = await poolDbContext.BookingPurposes
            .Select(v => new BookingPurposeDto(v.BookingPurposeId, v.BookingPurpose1))
            .ToListAsync(token);

        return bookingPurposes;
    }

    public async Task<(Dictionary<string, string>, Dictionary<string, string>)> ValidateChange(
        AuditIssuanceSettingsPurposeDto currValue, AuditIssuanceSettingsPurposeDto? prevValue) {
        Dictionary<string, string> previousValueList = new Dictionary<string, string>();
        Dictionary<string, string> currentValueList = new Dictionary<string, string>();

        var lastVal = Constants.Unknown;
        var currVal = Constants.Unknown;

        switch (currValue.AuActionType) {
            case 'I': lastVal = Constants.Missing; break;
            case 'U':
                lastVal = prevValue?.BookingPurposeName != null
                    ? prevValue.BookingPurposeName
                    : Constants.Missing; break;
            case 'D': currVal = Constants.Deleted; break;
        }

        var currListBookingVehicleType = (currValue.AuBookingPurposeVehicleTypes is not null)
            ? currValue.AuBookingPurposeVehicleTypes.Select(p => p.BookingVehicleTypeId).ToList()
            : new List<long?>();
        var currentVT = (currValue.AuBookingPurposeVehicleTypes != null)
            ? (string.Join(" \n ",
                currValue.AuBookingPurposeVehicleTypes.Select(v =>
                    $"{v.BookingVehicleTypeId}-{v.BookingVehicleTypeName}")) ?? Constants.Deleted)
            : null;

        if (prevValue == null) {
            currentValueList.Add("BookingPurposeName", currValue.BookingPurposeName?.ToString() ?? currVal);
            currentValueList.Add("IsDeleted", currValue.IsDeleted?.ToString() ?? currVal);
            currentValueList.Add("PurposeVehicleTypes", currentVT);
            return (currentValueList, previousValueList);
        }

        if (currValue.BookingPurposeName != prevValue.BookingPurposeName) {
            currentValueList.Add("BookingPurposeName", currValue.BookingPurposeName?.ToString() ?? currVal);
            previousValueList.Add("BookingPurposeName", prevValue.BookingPurposeName?.ToString() ?? lastVal);
        }

        if (currValue.IsDeleted != prevValue.IsDeleted) {
            currentValueList.Add("IsDeleted", currValue.IsDeleted?.ToString() ?? currVal);
            previousValueList.Add("IsDeleted", prevValue.IsDeleted?.ToString() ?? lastVal);
        }

        var prevListBookingVehicleType = (prevValue.AuBookingPurposeVehicleTypes is not null)
            ? prevValue.AuBookingPurposeVehicleTypes.Select(p => p.BookingVehicleTypeId).ToList()
            : new List<long?>();
        var areIdsDifferent = currListBookingVehicleType.Count != prevListBookingVehicleType.Count
                              || currListBookingVehicleType.Except(prevListBookingVehicleType).Any();

        currentValueList.Add("PurposeVehicleTypes", currentVT);
        if (areIdsDifferent && prevValue.AuBookingPurposeVehicleTypes != null) {
            var previousVT = string.Join(" \n ",
                prevValue.AuBookingPurposeVehicleTypes.Select(v =>
                    $"{v.BookingVehicleTypeId}-{v.BookingVehicleTypeName}")) ?? Constants.Missing;
            previousValueList.Add("PurposeVehicleTypes", previousVT);
        }
        else {
            previousValueList.Add("PurposeVehicleTypes", Constants.Missing);
        }

        await Task.Delay(1); // This is just a placeholder for async work

        return (currentValueList, previousValueList);
    }
}