﻿using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.EFCore.Models.Fleet;
using Microsoft.EntityFrameworkCore;
using MediatR;

namespace Cartrack.Fleet.Audit.IO.Sql;

public class LoginHistoryRepository : ILoginHistoryRepository {
    private const string DriverAppLoginText = "DriverApp-TFMS";
    private const string SuccessText = "Success";
    private readonly AppFleetDbContext _fleetDbContext;
    private readonly AppCtDbContext _ctDbContext;
    private readonly AppSettings _settings;

    public LoginHistoryRepository(AppSettings settings, AppFleetDbContext fleetDbContext, AppCtDbContext ctDbContext) {
        _settings = settings;
        _fleetDbContext = fleetDbContext;
        _ctDbContext = ctDbContext;
    }

    public async Task<List<ClientLoginHistoryDto>> GetClientLoginHistories(string? clientUserId, LoginMode loginMode,
        string? search, DateTime fromDate, DateTime toDate, int page, int pageSize, CancellationToken token) {
        var searchLowerText = search?.ToLower();

        var query = from clh in _fleetDbContext.ClientLoginHistories
            join cu in _fleetDbContext.ClientUsers on clh.SubClientId equals cu.ClientUserId into clientUsersTable
            from cu in clientUsersTable.DefaultIfEmpty()
            where clh.ClientId == _settings.UserId
                  && (clientUserId == null || clh.SubClientId == clientUserId)
                  && ((LoginMode.All == loginMode) ||
                      (LoginMode.Web == loginMode && clh.ClientDetails != DriverAppLoginText) ||
                      (LoginMode.DriverAppApi == loginMode && clh.ClientDetails == DriverAppLoginText))
                  && (search == null || clh.ClientDetails.Contains(search) ||
                      cu.UserName.ToLower().Contains(searchLowerText))
                  && clh.EventTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc)
                  && clh.EventTs <= DateTime.SpecifyKind(toDate, DateTimeKind.Utc)
            orderby clh.EventTs descending
            select new ClientLoginHistoryDto(clh.ClientId, clh.SubClientId, cu.UserName, clh.LoginSourceIp,
                clh.ClientDetails, SuccessText, clh.EventTs);

        var clientLoginHistories = await query
            .Skip((page - 1) * pageSize) // Skip records from previous pages
            .Take(pageSize + 1) // Take the records for the current page
            .ToListAsync(token);

        return clientLoginHistories;
    }

    public async Task<int> GetClientLoginHistoriesTotal(string? clientUserId, LoginMode loginMode, string? search,
        DateTime fromDate, DateTime toDate, CancellationToken token) {
        var searchLowerText = search?.ToLower();

        var query = from clh in _fleetDbContext.ClientLoginHistories
            join cu in _fleetDbContext.ClientUsers on clh.SubClientId equals cu.ClientUserId into clientUsersTable
            from cu in clientUsersTable.DefaultIfEmpty()
            where clh.ClientId == _settings.UserId
                  && (clientUserId == null || clh.SubClientId == clientUserId)
                  && ((LoginMode.All == loginMode) ||
                      (LoginMode.Web == loginMode && clh.ClientDetails != DriverAppLoginText) ||
                      (LoginMode.DriverAppApi == loginMode && clh.ClientDetails == DriverAppLoginText))
                  && (search == null || clh.ClientDetails.Contains(search) ||
                      cu.UserName.ToLower().Contains(searchLowerText))
                  && clh.EventTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc)
                  && clh.EventTs <= DateTime.SpecifyKind(toDate, DateTimeKind.Utc)
            select clh;

        int totalClientLoginHistoriesTotal = await query.CountAsync(token);

        return totalClientLoginHistoriesTotal;
    }

    public async Task<List<ClientLoginHistoryFailuresDto>> GetClientLoginHistoryFailures(string? clientUserId,
        LoginMode loginMode, string? search, DateTime fromDate, DateTime toDate, int page, int pageSize,
        CancellationToken token) {
        var searchLowerText = search?.ToLower();

        var query = from clh in _fleetDbContext.ClientLoginHistoryFailures
            join cu in _fleetDbContext.ClientUsers on clh.SubClientId equals cu.ClientUserId into clientUsersTable
            from cu in clientUsersTable.DefaultIfEmpty()
            where clh.ClientId == _settings.UserId
                  && (clientUserId == null || clh.SubClientId == clientUserId) && clh.SubClientId != ""
                  && ((LoginMode.All == loginMode) ||
                      (LoginMode.Web == loginMode && clh.ClientDetails != DriverAppLoginText) ||
                      (LoginMode.DriverAppApi == loginMode && clh.ClientDetails == DriverAppLoginText))
                  && (search == null || clh.ClientDetails.Contains(search) ||
                      cu.UserName.ToLower().Contains(searchLowerText))
                  && clh.EventTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc)
                  && clh.EventTs <= DateTime.SpecifyKind(toDate, DateTimeKind.Utc)
            orderby clh.EventTs descending
            select new ClientLoginHistoryFailuresDto(clh.ClientId, clh.SubClientId, cu.UserName, clh.LoginSourceIp,
                clh.ClientDetails, clh.Reason, clh.EventTs, clh.SubUsername);

        var clientLoginHistoriesFailures = await query
            .Skip((page - 1) * pageSize) // Skip records from previous pages
            .Take(pageSize + 1) // Take the records for the current page
            .ToListAsync(token);

        return clientLoginHistoriesFailures;
    }

    public async Task<int> GetClientLoginHistoriesFailuresTotal(string? clientUserId, LoginMode loginMode,
        string? search, DateTime fromDate, DateTime toDate, CancellationToken token) {
        var searchLowerText = search?.ToLower();

        var query = from clh in _fleetDbContext.ClientLoginHistoryFailures
            join cu in _fleetDbContext.ClientUsers on clh.SubClientId equals cu.ClientUserId into clientUsersTable
            from cu in clientUsersTable.DefaultIfEmpty()
            where clh.ClientId == _settings.UserId
                  && (clientUserId == null || clh.SubClientId == clientUserId) && clh.SubClientId != ""
                  && ((LoginMode.All == loginMode) ||
                      (LoginMode.Web == loginMode && clh.ClientDetails != DriverAppLoginText) ||
                      (LoginMode.DriverAppApi == loginMode && clh.ClientDetails == DriverAppLoginText))
                  && (search == null || clh.ClientDetails.Contains(search) ||
                      cu.UserName.ToLower().Contains(searchLowerText))
                  && clh.EventTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc)
                  && clh.EventTs <= DateTime.SpecifyKind(toDate, DateTimeKind.Utc)
            select clh;

        int totalClientLoginHistoriesFailuresTotal = await query.CountAsync(token);

        return totalClientLoginHistoriesFailuresTotal;
    }
}