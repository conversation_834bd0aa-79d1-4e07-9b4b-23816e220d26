﻿using Cartrack.Fleet.Audit.Domain;

namespace Cartrack.Fleet.Audit.Infrastructure;

public interface IIssuanceSettingsPurposeAuditRepository {
    Task<List<AuditIssuanceSettingsPurposeDto>> GetAuditIssuanceSettingsPurpose(string Search, DateTime From,
        DateTime To, int Page, int PageSize, CancellationToken token);

    Task<int> GetTotal(string Search, DateTime From, DateTime To, CancellationToken token);
    Task<List<BookingPurposeDto>> GetBookingPurposes(CancellationToken token);

    Task<(Dictionary<string, string>, Dictionary<string, string>)> ValidateChange(
        AuditIssuanceSettingsPurposeDto AuBookingPurposeCurrentValue,
        AuditIssuanceSettingsPurposeDto? AuBookingPurposePreviousValue);
}

public record AuditIssuanceSettingsPurposeDto(
    long? UserId,
    long? BookingPurposeId,
    string? BookingPurposeName,
    bool? IsDeleted,
    DateTime? AuTs,
    string? AuUsername,
    char? AuActionType,
    List<AuditIssuanceSettingsPurposeVehicleTypesDto>? AuBookingPurposeVehicleTypes);

public record AuditIssuanceSettingsPurposeVehicleTypesDto(
    long? BookingPurposeId,
    long? BookingVehicleTypeId,
    string? BookingVehicleTypeName);

public record BookingPurposeDto(long BookingPurposeId, string? BookingPurposeName);