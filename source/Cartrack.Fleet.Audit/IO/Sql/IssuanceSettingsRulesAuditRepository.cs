﻿using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using System.Reflection.Metadata;

namespace Cartrack.Fleet.Audit.IO.Sql;

public class IssuanceSettingsRulesAuditRepository(AppPoolDbContext poolDbContext)
    : IIssuanceSettingsRulesAuditRepository {
    public async Task<List<AuditIssuanceSettingsRulesDto>> GetAuditIssuanceSettingsRules(string Search, DateTime From,
        DateTime To, int Page, int PageSize, CancellationToken token) {
        var SearchLowerText = Search.ToLower();

        var query = from au in poolDbContext.AuUserBookingRules
            join br in poolDbContext.BookingRules on au.BookingRuleId equals br.BookingRuleId into bookingRuleJoin
            from br in bookingRuleJoin.DefaultIfEmpty()
            where au.AuTs >= DateTime.SpecifyKind(From, DateTimeKind.Utc) && au.AuTs <=
                                                                          DateTime.SpecifyKind(To, DateTimeKind.Utc)
                                                                          &&
                                                                          (string.IsNullOrEmpty(SearchLowerText) ||
                                                                           (au.AuUsername != null &&
                                                                            au.AuUsername.ToLower()
                                                                                .Contains(SearchLowerText)) ||
                                                                           (br.BookingRule1 != null &&
                                                                            br.BookingRule1.ToLower()
                                                                                .Contains(SearchLowerText)))
            orderby au.AuTs descending
            select new AuditIssuanceSettingsRulesDto(au.UserId, au.BookingRuleId, au.Value, au.Unit, au.Status, au.AuTs,
                au.AuUsername, au.AuActionType);

        var auUserBookingRules = await query
            .Skip((Page - 1) * PageSize) // Skip records from previous pages
            .Take(PageSize + 1) // Take the records for the current page
            .ToListAsync(token);

        return auUserBookingRules;
    }

    public async Task<AuditIssuanceSettingsRulesDto> GetPreviousValueOne(string Search, long BookingRuleId,
        DateTime From, CancellationToken token) {
        var SearchLowerText = Search.ToLower();

        var query = from au in poolDbContext.AuUserBookingRules
            join br in poolDbContext.BookingRules on au.BookingRuleId equals br.BookingRuleId into bookingRuleJoin
            from br in bookingRuleJoin.DefaultIfEmpty()
            where au.AuTs < DateTime.SpecifyKind(From, DateTimeKind.Utc) && au.BookingRuleId == BookingRuleId
                                                                         &&
                                                                         (string.IsNullOrEmpty(SearchLowerText) ||
                                                                          (au.AuUsername != null &&
                                                                           au.AuUsername.ToLower()
                                                                               .Contains(SearchLowerText)) ||
                                                                          (br.BookingRule1 != null &&
                                                                           br.BookingRule1.ToLower()
                                                                               .Contains(SearchLowerText)))
            orderby au.AuTs descending
            select new AuditIssuanceSettingsRulesDto(au.UserId, au.BookingRuleId, au.Value, au.Unit, au.Status, au.AuTs,
                au.AuUsername, au.AuActionType);

        var takeOneDataFromValue = await query
            .FirstOrDefaultAsync(token);

        return takeOneDataFromValue;
    }

    public async Task<int> GetTotal(string Search, DateTime From, DateTime To, CancellationToken token) {
        var SearchLowerText = Search.ToLower();

        var query = from au in poolDbContext.AuUserBookingRules
            join br in poolDbContext.BookingRules on au.BookingRuleId equals br.BookingRuleId into bookingRuleJoin
            from br in bookingRuleJoin.DefaultIfEmpty()
            where au.AuTs >= DateTime.SpecifyKind(From, DateTimeKind.Utc) && au.AuTs <=
                                                                          DateTime.SpecifyKind(To, DateTimeKind.Utc)
                                                                          &&
                                                                          (string.IsNullOrEmpty(SearchLowerText) ||
                                                                           (au.AuUsername != null &&
                                                                            au.AuUsername.ToLower()
                                                                                .Contains(SearchLowerText)) ||
                                                                           (br.BookingRule1 != null &&
                                                                            br.BookingRule1.ToLower()
                                                                                .Contains(SearchLowerText)))
            select au;

        int totalauUserBookingRules = await query.CountAsync(token);

        return totalauUserBookingRules;
    }

    public async Task<List<BookingRulesDto>> GetBookingRules(CancellationToken token) {
        var bookingRules = await poolDbContext.BookingRules
            .Select(v => new BookingRulesDto(v.BookingRuleId, v.BookingRule1))
            .ToListAsync(token);

        return bookingRules;
    }

    public async Task<(Dictionary<string, string>, Dictionary<string, string>)> ValidateChange(
        AuditIssuanceSettingsRulesDto currValue, AuditIssuanceSettingsRulesDto prevValue) {
        Dictionary<string, string> previousValueList = new Dictionary<string, string>();
        Dictionary<string, string> currentValueList = new Dictionary<string, string>();

        var lastVal = Constants.Unknown;
        var currVal = Constants.Unknown;

        switch (currValue.AuActionType) {
            case 'I': lastVal = Constants.Missing; break;
            case 'D': currVal = Constants.Deleted; break;
        }

        if (currValue.Value != prevValue.Value) {
            previousValueList.Add("Value", prevValue.Value?.ToString() ?? lastVal);
            currentValueList.Add("Value", currValue.Value?.ToString() ?? currVal);
        }

        if (currValue.Unit != prevValue.Unit) {
            previousValueList.Add("Unit", prevValue.Unit?.ToString() ?? lastVal);
            currentValueList.Add("Unit", currValue.Unit?.ToString() ?? currVal);
        }

        if (currValue.Status != prevValue.Status) {
            previousValueList.Add("Status", prevValue.Status?.ToString() ?? lastVal);
            currentValueList.Add("Status", currValue.Status?.ToString() ?? currVal);
        }

        await Task.Delay(1); // This is just a placeholder for async work

        return (currentValueList, previousValueList);
    }
}