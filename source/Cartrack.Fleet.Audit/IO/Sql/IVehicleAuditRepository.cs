﻿using Cartrack.EFCore.Models.CT;
using Cartrack.EFCore.Models.Fleet;

namespace Cartrack.Fleet.Audit.Infrastructure;

public interface IVehicleAuditRepository {
    Task<List<EFCore.Models.CT.Vehicle>> GetVehicles(long? vehicleId, CancellationToken token);

    Task<List<AuVehicleDriverLicense>> GetAuVehicleDriverLicenses(long? vehicleId, DateTime fromDate, DateTime toDate,
        CancellationToken token);

    Task<List<DriverLicenseType>> GetDriverLicenseTypes(CancellationToken token);

    Task<List<AuVehicleSpecialLicense>> GetAuVehicleSpecialLicenses(long? vehicleId, DateTime fromDate, DateTime toDate,
        CancellationToken token);

    Task<List<DriverSpecialLicenseType>> GetDriverSpecialLicenseTypes(CancellationToken token);

    Task<List<AuVehicleDepartment>> GetAuVehicleDepartments(long? vehicleId, DateTime fromDate, DateTime toDate,
        CancellationToken token);

    Task<List<Department>> GetDepartments(CancellationToken token);

    Task<List<AuVehicleAdditionalInfo>> GetAuVehicleAdditionalInfos(long? vehicleId, DateTime fromDate, DateTime toDate,
        int page, int pageSize, CancellationToken token);

    Task<int> GetCountAuVehicleAdditionalInfos(long? vehicleId, DateTime fromDate, DateTime toDate,
        CancellationToken token);
}