﻿using Cartrack.EFCore.Models.Fleet;

namespace Cartrack.Fleet.Audit.Infrastructure;

public interface IClientUserAuditRepository {
    Task<List<ClientUserDto>> GetClientUsers(string? clientUserId, CancellationToken token);

    Task<List<ClientUserLinkDepartmentAndSettingsDto>> GetAuditClientUsers(string? clientUserId, string? search,
        DateTime fromDate, DateTime toDate, int page, int pageSize, CancellationToken token);

    Task<ClientUserLinkDepartmentAndSettingsDto?> GetPreviousValueOne(string? clientUserId, string? search,
        DateTime fromDate, CancellationToken token);

    Task<int> GetTotal(string? clientUserId, string? search, DateTime fromDate, DateTime toDate,
        CancellationToken token);

    Task<(Dictionary<string, string>, Dictionary<string, string>)> ValidateChange(
        ClientUserLinkDepartmentAndSettingsDto AuClientUserDepartmentAndSettingsCurrentValue,
        ClientUserLinkDepartmentAndSettingsDto? AuClientUserDepartmentAndSettingsPreviousValue);
}

public record ClientUserDto(long UserId, string ClientUserId, string UserName);

public record ClientUserLinkDepartmentAndSettingsDto(
    long? UserId,
    string? ClientUserId,
    string? UserName,
    string? CellNumber,
    string? Email,
    string? IsLocked,
    string? IsRole,
    string? ClientUserRoleName,
    DateTime? AuTs,
    string? AuUsername,
    char? AuActionType,
    List<ClientUserSettingsDto>? AuClientUserSetting,
    List<ClientUserDepartmentsDto>? AuClientUserDepartment);

public record ClientUserDepartmentsDto(
    string? ClientUserId,
    long? DepartmentId,
    string? DepartmentName,
    string? ServiceType,
    bool? IsActive,
    DateTime? AuTs,
    string? AuUsername,
    char? AuActionType);

public record ClientUserSettingsDto(
    long? UserId,
    string? ClientUserId,
    string? CustomerName,
    string? GpsFormat,
    bool? AllowSubuserToSeeMainuserGeofences,
    bool? AllowSubuserToEditMainuserGeofences,
    bool? AllowMainuserToSeeSubuserGeofences,
    bool? AllowMainuserToEditSubuserGeofences,
    bool? AllowSubuserToSeeMainuserPoi,
    bool? AllowSubuserToEditMainuserPoi,
    bool? AllowMainuserToSeeSubuserPoi,
    bool? AllowMainuserToEditSubuserPoi
);