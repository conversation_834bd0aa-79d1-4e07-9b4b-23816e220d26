﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Audit.IO.Sql;

public class ClientUserAuditRepository(AppSettings settings, AppFleetDbContext fleetDbContext)
    : IClientUserAuditRepository {
    private readonly AppSettings _settings = settings;
    private readonly AppFleetDbContext _fleetDbContext = fleetDbContext;

    public async Task<List<ClientUserDto>> GetClientUsers(string? clientUserId, CancellationToken token) {
        var clientUsers = await this._fleetDbContext.ClientUsers
            .Where(v => v.ClientUserId.Equals(clientUserId))
            .Where(v => v.UserId == this._settings.UserId)
            .Select(p => new ClientUserDto(p.UserId, p.C<PERSON>UserId, p.UserName))
            .ToListAsync(token);
        return clientUsers;
    }

    public async Task<List<ClientUserLinkDepartmentAndSettingsDto>> GetAuditClientUsers(string? clientUserId,
        string? search, DateTime fromDate, DateTime toDate, int Page, int PageSize, CancellationToken token) {
        var searchLowerText = search?.ToLower();

        var query = from au in this._fleetDbContext.AuClientUsers
            join role in this._fleetDbContext.ClientUsers on au.ClientUserRoleId equals role.ClientUserId
            where au.UserId == this._settings.UserId
                  && au.AuTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc) &&
                  au.AuTs <= DateTime.SpecifyKind(toDate, DateTimeKind.Utc)
                  && (clientUserId == null || au.ClientUserId.Contains(clientUserId))
                  && (
                      string.IsNullOrEmpty(searchLowerText) ||
                      (au.AuUsername != null && au.AuUsername.ToLower().Contains(searchLowerText)) ||
                      (au.UserName != null && au.UserName.ToLower().Contains(searchLowerText))
                  )
            orderby au.AuTs descending
            select new ClientUserLinkDepartmentAndSettingsDto(
                au.UserId,
                au.ClientUserId,
                au.UserName,
                au.CellNumber,
                au.EMail,
                au.IsLocked.ToString(),
                au.IsRole.ToString(),
                role.UserName,
                au.AuTs,
                au.AuUsername,
                au.AuActionType,
                (from x in this._fleetDbContext.AuClientUserSettings
                    where x.ClientUserId == au.ClientUserId
                          && x.AuTs >= au.AuTs.Value.AddSeconds(0)
                          && x.AuTs <= au.AuTs.Value.AddSeconds(2)
                    orderby x.AuTs descending
                    select new ClientUserSettingsDto(
                        x.UserId,
                        x.ClientUserId,
                        x.CustomerName,
                        x.GpsFormat,
                        x.AllowSubuserToseeMainuserGeofences,
                        x.AllowSubuserToeditMainuserGeofences,
                        x.AllowMainuserToseeSubuserGeofences,
                        x.AllowMainuserToeditSubuserGeofences,
                        x.AllowSubuserToseeMainuserPoi,
                        x.AllowSubuserToeditMainuserPoi,
                        x.AllowMainuserToseeSubuserPoi,
                        x.AllowMainuserToeditSubuserPoi
                    )).ToList(),
                (from x in this._fleetDbContext.AuClientUserDepartments
                    join a in this._fleetDbContext.Departments on x.DepartmentId equals a.DepartmentId into
                        departmentJoin
                    from c in departmentJoin.DefaultIfEmpty()
                    where x.ClientUserId == au.ClientUserId
                          && x.AuTs >= au.AuTs.Value.AddSeconds(0)
                          && x.AuTs <= au.AuTs.Value.AddSeconds(2)
                          && x.AuActionType == 'I'
                    orderby x.AuTs descending
                    select new ClientUserDepartmentsDto(
                        x.ClientUserId,
                        x.DepartmentId,
                        c.DepartmentName,
                        x.ServiceType,
                        x.IsActive,
                        x.AuTs,
                        x.AuUsername,
                        x.AuActionType
                    )).ToList()
            );

        var auClientUsers = await query
            .Skip((Page - 1) * PageSize) // Skip records from previous pages
            .Take(PageSize + 1) // Take the records for the current page
            .ToListAsync(token);

        return auClientUsers;
    }

    public async Task<ClientUserLinkDepartmentAndSettingsDto?> GetPreviousValueOne(string? clientUserId, string? search,
        DateTime fromDate, CancellationToken token) {
        var searchLowerText = search?.ToLower();

        var query = from au in this._fleetDbContext.AuClientUsers
            join role in this._fleetDbContext.ClientUsers on au.ClientUserRoleId equals role.ClientUserId
            where au.UserId == this._settings.UserId
                  && au.AuTs < DateTime.SpecifyKind(fromDate, DateTimeKind.Utc)
                  && (clientUserId == null || au.ClientUserId.Contains(clientUserId))
                  && (string.IsNullOrEmpty(searchLowerText) ||
                      (au.AuUsername != null && au.AuUsername.ToLower().Contains(searchLowerText)) ||
                      (au.UserName != null && au.UserName.ToLower().Contains(searchLowerText))
                  )
            orderby au.AuTs descending
            select new ClientUserLinkDepartmentAndSettingsDto(
                au.UserId,
                au.ClientUserId,
                au.UserName,
                au.CellNumber,
                au.EMail,
                au.IsLocked.ToString(),
                au.IsRole.ToString(),
                role.UserName,
                au.AuTs,
                au.AuUsername,
                au.AuActionType,
                (from x in this._fleetDbContext.AuClientUserSettings
                    where x.ClientUserId == au.ClientUserId
                          && x.AuTs >= au.AuTs.Value.AddSeconds(0)
                          && x.AuTs <= au.AuTs.Value.AddSeconds(2)
                    orderby x.AuTs descending
                    select new ClientUserSettingsDto(
                        x.UserId,
                        x.ClientUserId,
                        x.CustomerName,
                        x.GpsFormat,
                        x.AllowSubuserToseeMainuserGeofences,
                        x.AllowSubuserToeditMainuserGeofences,
                        x.AllowMainuserToseeSubuserGeofences,
                        x.AllowMainuserToeditSubuserGeofences,
                        x.AllowSubuserToseeMainuserPoi,
                        x.AllowSubuserToeditMainuserPoi,
                        x.AllowMainuserToseeSubuserPoi,
                        x.AllowMainuserToeditSubuserPoi
                    )).ToList(),
                (from x in this._fleetDbContext.AuClientUserDepartments
                    join a in this._fleetDbContext.Departments on x.DepartmentId equals a.DepartmentId into
                        departmentJoin
                    from c in departmentJoin.DefaultIfEmpty()
                    where x.ClientUserId == au.ClientUserId
                          && x.AuTs >= au.AuTs.Value.AddSeconds(0)
                          && x.AuTs <= au.AuTs.Value.AddSeconds(2)
                          && x.AuActionType == 'I'
                    orderby x.AuTs descending
                    select new ClientUserDepartmentsDto(
                        x.ClientUserId,
                        x.DepartmentId,
                        c.DepartmentName,
                        x.ServiceType,
                        x.IsActive,
                        x.AuTs,
                        x.AuUsername,
                        x.AuActionType
                    )).ToList()
            );
        var takeOneDataFromValue = await query
            .FirstOrDefaultAsync(token);

        return takeOneDataFromValue;
    }

    public async Task<int> GetTotal(string? clientUserId, string? search, DateTime fromDate, DateTime toDate,
        CancellationToken token) {
        var searchLowerText = search?.ToLower();

        var query = from au in this._fleetDbContext.AuClientUsers
            where au.UserId == this._settings.UserId
                  && au.AuTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc) &&
                  au.AuTs <= DateTime.SpecifyKind(toDate, DateTimeKind.Utc)
                  && (clientUserId == null || au.ClientUserId.Contains(clientUserId))
                  && (
                      string.IsNullOrEmpty(searchLowerText) ||
                      (au.AuUsername != null && au.AuUsername.ToLower().Contains(searchLowerText)) ||
                      (au.UserName != null && au.UserName.ToLower().Contains(searchLowerText))
                  )
            orderby au.AuTs descending
            select au;

        int totalAuClientUsers = await query.CountAsync(token);

        return totalAuClientUsers;
    }

    public async Task<(Dictionary<string, string>, Dictionary<string, string>)> ValidateChange(
        ClientUserLinkDepartmentAndSettingsDto currValue, ClientUserLinkDepartmentAndSettingsDto? prevValue) {
        Dictionary<string, string> previousValueList = new Dictionary<string, string>();
        Dictionary<string, string> currentValueList = new Dictionary<string, string>();

        var lastVal = Constants.Unknown;
        var currVal = Constants.Unknown;

        switch (currValue.AuActionType) {
            case 'I': lastVal = Constants.Missing; break;
            //case 'U': lastVal = prevValue?.BookingPurposeName != null ? prevValue.BookingPurposeName : Constants.Missing; break;
            case 'D': currVal = Constants.Deleted; break;
        }

        var currListAuClientUserDepartment = (currValue.AuClientUserDepartment is not null)
            ? currValue.AuClientUserDepartment.Select(p => p.DepartmentId).ToList()
            : new List<long?>();
        var currentUserDepartment = (currValue.AuClientUserDepartment != null)
            ? (string.Join(" \n ",
                   currValue.AuClientUserDepartment.Select(v => $"{v.DepartmentId}-{v.DepartmentName}")) ??
               Constants.Deleted)
            : null;

        if (prevValue == null) {
            currentValueList.Add("UserName", currValue.UserName?.ToString() ?? currVal);
            currentValueList.Add("PhoneNumber", currValue.CellNumber?.ToString() ?? currVal);
            currentValueList.Add("Email", currValue.Email?.ToString() ?? currVal);
            currentValueList.Add("IsLocked", currValue.IsLocked?.ToString() ?? currVal);
            currentValueList.Add("IsRole", currValue.IsRole?.ToString() ?? currVal);
            currentValueList.Add("ClientUserRoleName", currValue.ClientUserRoleName?.ToString() ?? currVal);

            currentValueList.Add("ClientUserDepartments", currentUserDepartment ?? String.Empty);
            return (currentValueList, previousValueList);
        }

        if (currValue.UserName != prevValue.UserName) {
            currentValueList.Add("UserName", currValue.UserName?.ToString() ?? currVal);
            previousValueList.Add("UserName", prevValue.UserName?.ToString() ?? lastVal);
        }

        if (currValue.CellNumber != prevValue.CellNumber) {
            currentValueList.Add("PhoneNumber", currValue.CellNumber?.ToString() ?? currVal);
            previousValueList.Add("PhoneNumber", prevValue.CellNumber?.ToString() ?? lastVal);
        }

        if (currValue.Email != prevValue.Email) {
            currentValueList.Add("Email", currValue.Email?.ToString() ?? currVal);
            previousValueList.Add("Email", prevValue.Email?.ToString() ?? lastVal);
        }

        if (currValue.IsLocked != prevValue.IsLocked) {
            currentValueList.Add("IsLocked", currValue.IsLocked?.ToString() ?? currVal);
            previousValueList.Add("IsLocked", prevValue.IsLocked?.ToString() ?? lastVal);
        }

        if (currValue.IsRole != prevValue.IsRole) {
            currentValueList.Add("IsRole", currValue.IsRole?.ToString() ?? currVal);
            previousValueList.Add("IsRole", prevValue.IsRole?.ToString() ?? lastVal);
        }

        if (currValue.ClientUserRoleName != prevValue.ClientUserRoleName) {
            currentValueList.Add("ClientUserRoleName", currValue.ClientUserRoleName?.ToString() ?? currVal);
            previousValueList.Add("ClientUserRoleName", prevValue.ClientUserRoleName?.ToString() ?? lastVal);
        }

        // User Departments
        var prevListAuClientUserDepartment = (prevValue.AuClientUserDepartment is not null)
            ? prevValue.AuClientUserDepartment.Select(p => p.DepartmentId).ToList()
            : new List<long?>();
        var areIdsDifferent = currListAuClientUserDepartment.Count != prevListAuClientUserDepartment.Count
                              || currListAuClientUserDepartment.Except(prevListAuClientUserDepartment).Any();

        currentValueList.Add("ClientUserDepartments", currentUserDepartment ?? String.Empty);
        if (areIdsDifferent && prevValue.AuClientUserDepartment != null) {
            var previousUserDepartment = string.Join(" \n ",
                                             prevValue.AuClientUserDepartment.Select(v =>
                                                 $"{v.DepartmentId}-{v.DepartmentName}")) ??
                                         Constants.Missing;
            previousValueList.Add("ClientUserDepartments", previousUserDepartment);
        }
        else {
            previousValueList.Add("ClientUserDepartments", Constants.Missing);
        }

        await Task.Delay(1); // This is just a placeholder for async work

        return (currentValueList, previousValueList);
    }
}