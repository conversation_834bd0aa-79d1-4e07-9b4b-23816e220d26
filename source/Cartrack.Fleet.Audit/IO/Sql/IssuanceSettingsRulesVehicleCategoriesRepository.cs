﻿using Cartrack.Fleet.Audit.Domain;
using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using System.Reflection.Metadata;

namespace Cartrack.Fleet.Audit.IO.Sql;

public class IssuanceSettingsVehicleCategoriesAuditRepository(AppPoolDbContext poolDbContext)
    : IIssuanceSettingsVehicleCategoriesAuditRepository {
    public async Task<List<AuditBookingVehicleTypeDto>> GetAuditIssuanceSettingsVehicleCategories(string search,
        DateTime fromDate, DateTime to, int page, int pageSize, CancellationToken token) {
        var searchLowerText = search.ToLower();

        var query = from au in poolDbContext.AuBookingVehicleTypes
            where au.AuTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc) && au.AuTs <=
                                                                              DateTime.SpecifyKind(to, DateTimeKind.Utc)
                                                                              &&
                                                                              (string.IsNullOrEmpty(searchLowerText) ||
                                                                               (au.BookingVehicleType != null &&
                                                                                au.BookingVehicleType.ToLower()
                                                                                    .Contains(searchLowerText)))
            orderby au.AuTs descending
            select new AuditBookingVehicleTypeDto(au.BookingVehicleTypeId, au.UserId, au.BookingVehicleType,
                au.IsDeleted, au.AuTs, au.AuUsername, au.AuActionType);

        var auUserBookingVehicleTypes = await query
            .Skip((page - 1) * pageSize) // Skip records from previous pages
            .Take(pageSize + 1) // Take the records for the current page
            .ToListAsync(token);

        return auUserBookingVehicleTypes;
    }

    public async Task<AuditBookingVehicleTypeDto> GetPreviousValueOne(string search, long bookingVehicleTypeId,
        DateTime fromDate, CancellationToken token) {
        var searchLowerText = search.ToLower();

        var query = from au in poolDbContext.AuBookingVehicleTypes
            where au.AuTs < DateTime.SpecifyKind(fromDate, DateTimeKind.Utc) && au.BookingVehicleTypeId ==
                                                                             bookingVehicleTypeId
                                                                             &&
                                                                             (string.IsNullOrEmpty(searchLowerText) ||
                                                                              (au.BookingVehicleType != null &&
                                                                               au.BookingVehicleType.ToLower()
                                                                                   .Contains(searchLowerText)))
            orderby au.AuTs descending
            select new AuditBookingVehicleTypeDto(au.BookingVehicleTypeId, au.UserId, au.BookingVehicleType,
                au.IsDeleted, au.AuTs, au.AuUsername, au.AuActionType);

        var takeOneDataFromValue = await query
            .FirstOrDefaultAsync(token);

        return takeOneDataFromValue;
    }

    public async Task<int> GetTotal(string search, DateTime fromDate, DateTime toDate, CancellationToken token) {
        var searchLowerText = search.ToLower();

        var query = from au in poolDbContext.AuBookingVehicleTypes
            where au.AuTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc) && au.AuTs <=
                                                                              DateTime.SpecifyKind(toDate,
                                                                                  DateTimeKind.Utc)
                                                                              &&
                                                                              (string.IsNullOrEmpty(searchLowerText) ||
                                                                               (au.BookingVehicleType != null &&
                                                                                au.BookingVehicleType.ToLower()
                                                                                    .Contains(searchLowerText)))
            select au;

        int totalAuUserBookingVehicleType = await query.CountAsync(token);

        return totalAuUserBookingVehicleType;
    }

    public async Task<List<BookingVehicleTypesDto>> GetBookingVehicleTypes(CancellationToken token) {
        var bookingVehicleTypes = await poolDbContext.BookingVehicleTypes
            .Select(v => new BookingVehicleTypesDto(v.BookingVehicleTypeId, v.BookingVehicleType1))
            .ToListAsync(token);

        return bookingVehicleTypes;
    }

    public async Task<(Dictionary<string, string>, Dictionary<string, string>)> ValidateChange(
        AuditBookingVehicleTypeDto currValue, AuditBookingVehicleTypeDto prevValue) {
        Dictionary<string, string> previousValueList = new Dictionary<string, string>();
        Dictionary<string, string> currentValueList = new Dictionary<string, string>();

        var lastVal = Constants.Unknown;
        var currVal = Constants.Unknown;

        switch (currValue.AuActionType) {
            case 'I': lastVal = Constants.Missing; break;
            case 'D': currVal = Constants.Deleted; break;
        }

        if (currValue.BookingVehicleType != prevValue.BookingVehicleType) {
            previousValueList.Add("BookingVehicleTypeName", prevValue.BookingVehicleType?.ToString() ?? lastVal);
            currentValueList.Add("BookingVehicleTypeName", currValue.BookingVehicleType?.ToString() ?? currVal);
        }

        if (currValue.IsDeleted != prevValue.IsDeleted) {
            previousValueList.Add("IsDeleted", prevValue.IsDeleted?.ToString() ?? lastVal);
            currentValueList.Add("IsDeleted", currValue.IsDeleted?.ToString() ?? currVal);
        }

        await Task.Delay(1); // This is just a placeholder for async work

        return (currentValueList, previousValueList);
    }
}