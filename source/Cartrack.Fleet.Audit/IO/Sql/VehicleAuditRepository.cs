﻿using Cartrack.EFCore.Models.CT;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Audit.Infrastructure;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Audit.IO.Sql;

public class VehicleAuditRepository : IVehicleAuditRepository {
    private readonly AppFleetDbContext _fleetDbContext;
    private readonly AppCtDbContext _ctDbContext;
    private readonly AppSettings _settings;

    public VehicleAuditRepository(AppSettings settings, AppFleetDbContext fleetDbContext, AppCtDbContext ctDbContext) {
        _settings = settings;
        _fleetDbContext = fleetDbContext;
        _ctDbContext = ctDbContext;
    }

    public async Task<List<EFCore.Models.CT.Vehicle>> GetVehicles(long? vehicleId, CancellationToken token) {
        var vehicles = await _ctDbContext.Vehicles
            .Where(v => v.UserId == _settings.UserId
                        && (vehicleId == null || v.VehicleId == vehicleId))
            .ToListAsync(token);
        return vehicles;
    }

    public async Task<List<AuVehicleDriverLicense>> GetAuVehicleDriverLicenses(long? vehicleId, DateTime fromDate,
        DateTime toDate, CancellationToken token) {
        var auVehicleDriverLicenses = await _fleetDbContext.AuVehicleDriverLicenses
            .Where(v => (vehicleId == null || v.VehicleId == vehicleId)
                        && v.AuTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc)
                        && v.AuTs <= DateTime.SpecifyKind(toDate, DateTimeKind.Utc))
            .OrderByDescending(v => v.AuTs)
            .ToListAsync();
        return auVehicleDriverLicenses;
    }

    public async Task<List<DriverLicenseType>> GetDriverLicenseTypes(CancellationToken token) {
        var driverLicenses = await _fleetDbContext.DriverLicenseTypes
            .ToListAsync(token);
        return driverLicenses;
    }

    public async Task<List<AuVehicleSpecialLicense>> GetAuVehicleSpecialLicenses(long? vehicleId, DateTime fromDate,
        DateTime toDate, CancellationToken token) {
        var auVehicleSpecialLicenses = await _fleetDbContext.AuVehicleSpecialLicenses
            .Where(v => (vehicleId == null || v.VehicleId == vehicleId)
                        && v.AuTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc)
                        && v.AuTs <= DateTime.SpecifyKind(toDate, DateTimeKind.Utc))
            .OrderByDescending(v => v.AuTs)
            .ToListAsync(token);
        return auVehicleSpecialLicenses;
    }

    public async Task<List<DriverSpecialLicenseType>> GetDriverSpecialLicenseTypes(CancellationToken token) {
        var specialLicenses = await _fleetDbContext.DriverSpecialLicenseTypes
            .ToListAsync(token);
        return specialLicenses;
    }

    public async Task<List<AuVehicleDepartment>> GetAuVehicleDepartments(long? vehicleId, DateTime fromDate,
        DateTime toDate, CancellationToken token) {
        var auVehicleDepartments = await _fleetDbContext.AuVehicleDepartments
            .Where(v => (vehicleId == null || v.VehicleId == vehicleId)
                        && v.AuTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc)
                        && v.AuTs <= DateTime.SpecifyKind(toDate, DateTimeKind.Utc))
            .OrderByDescending(v => v.AuTs)
            .ToListAsync();
        return auVehicleDepartments;
    }

    public async Task<List<Department>> GetDepartments(CancellationToken token) {
        var departments = await _fleetDbContext.Departments
            .ToListAsync(token);
        return departments;
    }

    public async Task<List<AuVehicleAdditionalInfo>> GetAuVehicleAdditionalInfos(long? vehicleId, DateTime fromDate,
        DateTime toDate, int page, int pageSize, CancellationToken token) {
        var auVehicleDepartments = await _fleetDbContext.AuVehicleAdditionalInfos
            .Where(v => (vehicleId == null || v.VehicleId == vehicleId)
                        && v.AuTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc)
                        && v.AuTs <= DateTime.SpecifyKind(toDate, DateTimeKind.Utc))
            .Skip((page - 1) * pageSize) // Skip records from previous pages
            .Take(pageSize + 1) // Take the records for the current page
            .OrderByDescending(v => v.AuTs)
            .ToListAsync(token);
        return auVehicleDepartments;
    }

    public async Task<int> GetCountAuVehicleAdditionalInfos(long? vehicleId, DateTime fromDate, DateTime toDate,
        CancellationToken token) {
        var countAuVehicleAdditionalInfos = await _fleetDbContext.AuVehicleAdditionalInfos
            .Where(v => (vehicleId == null || v.VehicleId == vehicleId)
                        && v.AuTs >= DateTime.SpecifyKind(fromDate, DateTimeKind.Utc)
                        && v.AuTs <= DateTime.SpecifyKind(toDate, DateTimeKind.Utc))
            .CountAsync(token);
        return countAuVehicleAdditionalInfos;
    }
}