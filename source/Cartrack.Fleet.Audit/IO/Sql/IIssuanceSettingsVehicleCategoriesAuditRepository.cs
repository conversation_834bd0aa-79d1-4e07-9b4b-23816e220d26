﻿using Cartrack.Fleet.Audit.Domain;

namespace Cartrack.Fleet.Audit.Infrastructure;

public interface IIssuanceSettingsVehicleCategoriesAuditRepository {
    Task<List<AuditBookingVehicleTypeDto>> GetAuditIssuanceSettingsVehicleCategories(string Search, DateTime From,
        DateTime To, int Page, int PageSize, CancellationToken token);

    Task<AuditBookingVehicleTypeDto> GetPreviousValueOne(string Search, long BookingVehicleTypeId, DateTime From,
        CancellationToken token);

    Task<int> GetTotal(string Search, DateTime From, DateTime To, CancellationToken token);
    Task<List<BookingVehicleTypesDto>> GetBookingVehicleTypes(CancellationToken token);

    Task<(Dictionary<string, string>, Dictionary<string, string>)> ValidateChange(
        AuditBookingVehicleTypeDto AuBookingVehicleTypeCurrentValue,
        AuditBookingVehicleTypeDto AuBookingVehicleTypePreviousValue);
}

public record AuditBookingVehicleTypeDto(
    long? BookingVehicleTypeId,
    long? UserId,
    string? BookingVehicleType,
    bool? IsDeleted,
    DateTime? AuTs,
    string? AuUsername,
    char? AuActionType);

public record BookingVehicleTypesDto(long BookingVehicleTypeId, string? BookingVehicleTypeName);