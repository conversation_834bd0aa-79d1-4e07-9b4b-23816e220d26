﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Core.Auth.Domain;
using Cartrack.Core.Auth.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Core.Auth.IO.Sql;

public partial class AuthRepository {
    public async Task<ClientUserToken?> GetRefreshToken(string refreshToken) {
        var token = await context.ClientUserTokens
            .Include(c => c.ClientUser)
            .FirstOrDefaultAsync(r => r.Token == refreshToken);
        return token;
    }

    public async Task UpdateRefreshToken(ClientUserToken refreshToken, string newRefreshToken) {
        // Revoke the old refresh token
        refreshToken.RevokedTs = DateTime.UtcNow;
        refreshToken.ReplacedByToken = newRefreshToken;

        // Save new refresh token
        refreshToken.ClientUser.ClientUserTokens.Add(new ClientUserToken {
            Token = newRefreshToken,
            ExpiresTs = DateTime.UtcNow.AddDays(settings.RefreshTokenValidityDays),
            CreatedTs = DateTime.UtcNow,
            UserId = refreshToken.ClientUser.UserId,
            ClientUserId = refreshToken.ClientUser.ClientUserId,
        });

        await context.SaveChangesAsync();
    }
}