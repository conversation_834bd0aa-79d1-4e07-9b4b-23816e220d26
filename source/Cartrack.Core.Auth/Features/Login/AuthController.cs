﻿using Cartrack.Core.Auth.Features.Login;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Core.Auth.IO.Http;

public partial class AuthController {
    [HttpPost("login")]
    public async Task<ActionResult<LoginResponse>> Login(LoginRequest request) {
        var resp = await mediator.Send(request);

        if (resp.Value == null) {
            return this.Unauthorized(new {
                Message = "Username or password is incorrect"
            });
        }

        if (resp.PasswordVerificationResult == PasswordVerificationResult.Failed) {
            return this.Unauthorized(new {
                Message = "Username or password is incorrect"
            });
        }


        return this.Ok(resp);
    }
}