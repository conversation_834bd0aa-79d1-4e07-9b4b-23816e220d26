﻿using Cartrack.AppHost;
using Cartrack.Core.Auth.Domain;
using Cartrack.Core.Auth.IO.Sql;
using Cartrack.EFCore.Models.CT;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Security.Cryptography;
using System.Security.Principal;
using System.Text;

namespace Cartrack.Core.Auth.Features.Login;

public class LoginHandler(AppSettings settings, IAuthRepository repository, IJwtService jwtService, IHttpContextAccessor context, ILogger<LoginHandler> logger)
    : IRequestHandler<LoginRequest, LoginResponse> {
    public async Task<LoginResponse> Handle(LoginRequest request, CancellationToken token) {
        Requires.NotNullOrEmpty(request.Username, nameof(request.Username));
        Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
        
        bool isMainUser = false;
        ClientUser? clientUser = null;
        var mainUsers = settings.MainUser.Split(',').Select(u => u.Trim());
        if (mainUsers.Any(u => u.Equals(request.Username, StringComparison.OrdinalIgnoreCase))) {
            var user = await repository.GetUserByUserName(request.Username);
            if (user is null) {
                logger.LogWarning("[{TraceId}] Main user not found: {Username}", context.HttpContext?.TraceIdentifier ?? "", request.Username);
                return new LoginResponse(null);
            }

            isMainUser = true;
            clientUser = new ClientUser() { 
                UserName = user.UserName, 
                UserId = user.UserId, 
                ClientUserId = user.UserId.ToString(), 
                PasswordHash = user.PasswordHash 
            };
        }
        else {
            clientUser = await repository.GetClientUser(request.Username, token);
            if (clientUser is null) {
                logger.LogWarning("[{TraceId}] Sub user not found: {Username}", context.HttpContext?.TraceIdentifier ?? "", request.Username);
                return new LoginResponse(null);
            }
        }

        PasswordVerificationResult result;
        if (isMainUser || settings.IsValidatingPassword) {
            result = IsPasswordValid(request.Password, clientUser.PasswordHash);
            if (result == PasswordVerificationResult.Failed) {
                return new LoginResponse(Domain.Login.Empty()) {
                    PasswordVerificationResult = result,
                    IsServerError = false
                };
            }
        }
        else {
            result = PasswordVerificationResult.Success;
        }

        var accessToken = jwtService.GenerateAccessToken(clientUser, request.Account);
        var refreshToken = jwtService.GenerateRefreshToken(clientUser, request.Account);
        if (!isMainUser) {
            await repository.AddRefreshToken(clientUser, refreshToken);
        }

        logger.LogInformation("[{TraceId}] User logged in: {Username}, access token: {AccessToken}", 
            context.HttpContext?.TraceIdentifier ?? "", request.Username, accessToken);
        
        return new LoginResponse(new Domain.Login {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            ExpiresAtUtc = DateTime.UtcNow.AddMinutes(settings.TokenValidityMinutes)
        }) {
            PasswordVerificationResult = result
        };
    }

    private PasswordVerificationResult IsPasswordValid(string password, string expectedPasswordHash) {
        string GeneratePasswordHash(string pw) {
            using (SHA1 sha1 = SHA1.Create()) {
                byte[] inputBytes = Encoding.UTF8.GetBytes(password);
                byte[] hashBytes = sha1.ComputeHash(inputBytes);

                StringBuilder sb = new StringBuilder();
                foreach (var b in hashBytes) {
                    sb.Append(b.ToString("x2"));
                }

                return sb.ToString().ToUpper();
            }
            //throw new NotImplementedException("Andreas please check how fleet API creates the password hash and implement this method");
        }

        var passwordHash = GeneratePasswordHash(password);
        return passwordHash == expectedPasswordHash
            ? PasswordVerificationResult.Success
            : PasswordVerificationResult.Failed;
    }
}