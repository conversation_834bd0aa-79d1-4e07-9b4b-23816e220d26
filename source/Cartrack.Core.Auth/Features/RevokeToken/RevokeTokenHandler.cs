﻿using Cartrack.Core.Auth.Domain;
using Cartrack.Core.Auth.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;

namespace Cartrack.Core.Auth.IO.Http;

public class RevokeTokenHandler(
    AppSettings settings,
    IAuthRepository repository,
    IJwtService jwtService,
    IHttpContextAccessor httpContext) : IRequestHandler<RevokeTokenRequest, RevokeTokenResponse> {
    public async Task<RevokeTokenResponse> Handle(RevokeTokenRequest request, CancellationToken cancellationToken) {
        var refreshToken = await repository.GetRefreshToken(request.RefreshToken);

        if (refreshToken == null) {
            return new RevokeTokenResponse(null);
        }

        if (DateTime.UtcNow >= refreshToken.ExpiresTs.ToUniversalTime()) {
            return new RevokeTokenResponse("") {
                IsExpired = true
            };
        }

        // Get current user id from the JWT token
        var clientUserId = httpContext.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        // Only allow users to revoke their own tokens
        if (refreshToken.ClientUserId != clientUserId) {
            return new RevokeTokenResponse("") {
                IsUnauthorized = true
            };
        }

        await repository.RevokeToken(refreshToken);
        return new RevokeTokenResponse($"Token revoked : {request.RefreshToken}");
    }
}