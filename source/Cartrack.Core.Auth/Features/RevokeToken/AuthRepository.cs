﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Core.Auth.Domain;
using Cartrack.Core.Auth.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Core.Auth.IO.Sql;

public partial class AuthRepository {
    public async Task RevokeToken(ClientUserToken refreshToken) {
        // Revoke token
        refreshToken.RevokedTs = DateTime.UtcNow;
        refreshToken.ExpiresTs = DateTime.UtcNow.AddMinutes(-1);
        await context.SaveChangesAsync();
    }
}