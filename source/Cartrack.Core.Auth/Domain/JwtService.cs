﻿using Cartrack.EFCore.Models.CT;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Core.Auth.IO.Http;
using Cartrack.Fleet.Common;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Security.Principal;

namespace Cartrack.Core.Auth.Domain;

public class JwtService(IRsaKeyService rsaKeyService, AppSettings settings) : IJwtService {
    public string GenerateAccessToken(ClientUser user, string account) {
        var tokenHandler = new JwtSecurityTokenHandler();
        var privateKey = rsaKeyService.GetPrivateKey();

        var tokenDescriptor = new SecurityTokenDescriptor {
            Subject = new ClaimsIdentity(
            [
                new Claim(ClaimTypes.Name, user.UserName),
                new Claim(ClaimTypes.NameIdentifier, user.ClientUserId),
                //new Claim("RoleId", user.ClientUserRoleId),
                new Claim("Account", account),
                new Claim("AccountId", user.UserId.ToString())
            ]),
            Expires = DateTime.UtcNow.AddMinutes(settings.TokenValidityMinutes),
            SigningCredentials = new SigningCredentials(
                new RsaSecurityKey(privateKey),
                SecurityAlgorithms.RsaSha256
            ),
            Issuer = settings.Issuer,
            Audience = settings.Audience
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    public string GenerateRefreshToken(ClientUser user, string account) {
        var tokenHandler = new JwtSecurityTokenHandler();
        var privateKey = rsaKeyService.GetPrivateKey();

        var tokenDescriptor = new SecurityTokenDescriptor {
            Subject = new ClaimsIdentity(
            [
                new Claim(ClaimTypes.Name, user.UserName),
                new Claim(ClaimTypes.NameIdentifier, user.ClientUserId),
                //new Claim("RoleId", user.ClientUserRoleId),
                new Claim("Account", account),
            ]),
            Expires = DateTime.UtcNow.AddDays(settings.RefreshTokenValidityDays),
            SigningCredentials = new SigningCredentials(
                new RsaSecurityKey(privateKey),
                SecurityAlgorithms.RsaSha256
            ),
            Issuer = settings.Issuer,
            Audience = settings.Audience
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    //public string GenerateRefreshToken() {
    //    var randomBytes = new byte[32];
    //    using var rng = RandomNumberGenerator.Create();
    //    rng.GetBytes(randomBytes);
    //    return Convert.ToBase64String(randomBytes);
    //}

    public ClaimsPrincipal? GetPrincipalFromToken(string token) {
        try {
            var tokenHandler = new JwtSecurityTokenHandler();
            var publicKey = rsaKeyService.GetPublicKey();

            var tokenValidationParameters = new TokenValidationParameters {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new RsaSecurityKey(publicKey),
                ValidateIssuer = true,
                ValidIssuer = settings.Issuer,
                ValidateAudience = true,
                ValidAudience = settings.Audience,
                ValidateLifetime = false // Don't validate lifetime here
            };

            var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out var validatedToken);
            return this.IsJwtWithValidSecurityAlgorithm(validatedToken) ? principal : null;
        }
        catch {
            return null;
        }
    }

    public bool IsTokenExpired(string token) {
        var jwtToken = new JwtSecurityToken(token);
        var isTokenValid = (jwtToken.ValidFrom <= DateTime.UtcNow) && (jwtToken.ValidTo >= DateTime.UtcNow);
        return !isTokenValid;
    }

    private bool IsJwtWithValidSecurityAlgorithm(SecurityToken validatedToken) {
        return validatedToken is JwtSecurityToken jwtSecurityToken &&
               jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.RsaSha256,
                   StringComparison.InvariantCultureIgnoreCase);
    }
}