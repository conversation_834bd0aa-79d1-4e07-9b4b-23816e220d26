﻿namespace Cartrack.Core.Auth.Domain;

public class Login {
    public required string AccessToken { get; init; }
    public required string RefreshToken { get; init; }
    public required DateTime ExpiresAtUtc { get; init; }

    public static Login? Empty() {
        return new Login {
            AccessToken = "",
            RefreshToken = "",
            ExpiresAtUtc = DateTime.UtcNow
        };
    }
}