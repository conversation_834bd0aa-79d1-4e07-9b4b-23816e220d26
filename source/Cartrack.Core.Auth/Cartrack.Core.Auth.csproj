﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Cartrack.AppHost" Version="2.5.7" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication" Version="2.3.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.12"/>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.1"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Infrastructure\"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Cartrack.Fleet.Common\Cartrack.Fleet.Common.csproj"/>
    </ItemGroup>

</Project>
