﻿using Cartrack.Core.Auth.Domain;
using Cartrack.Core.Auth.IO.FileSystem;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Core.Auth.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Core.Auth.IO.Http;

[ApiController]
[Route("auth")]
public partial class AuthController(
    AppSettings settings,
    IJwtService jwtService,
    AppFleetDbContext fleetDbContext,
    AppCtDbContext ctDbContext,
    IMediator mediator,
    IFileIo fileIo,
    IHttpContextAccessor httpContext)
    : ControllerBase {
}