﻿using Cartrack.EFCore.Models.CT;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Core.Auth.Domain;

namespace Cartrack.Core.Auth.IO.Sql;

public interface IAuthRepository {
    Task<ClientUser?> GetClientUser(string username, CancellationToken token);
    Task AddRefreshToken(ClientUser user, string refreshToken);

    Task<ClientUserToken?> GetRefreshToken(string refreshToken);

    Task UpdateRefreshToken(ClientUserToken refreshToken, string newRefreshToken);
    Task RevokeToken(ClientUserToken refreshToken);

    Task<EFCore.Models.CT.User?> GetUser(long userId);

    Task<EFCore.Models.CT.User?> GetUserByUserName(string username);
}