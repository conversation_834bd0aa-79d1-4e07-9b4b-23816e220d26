﻿using Cartrack.EFCore.Models.CT;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Core.Auth.IO.Sql;

public partial class AuthRepository(AppSettings settings, AppFleetDbContext context, AppCtDbContext ctContext)
    : IAuthRepository {
    public async Task<EFCore.Models.CT.User?> GetUser(long userId) {
        return await ctContext.Users.FirstOrDefaultAsync(u => u.UserId == userId);
    }

    public async Task<EFCore.Models.CT.User?> GetUserByUserName(string username) {
        return await ctContext.Users.FirstOrDefaultAsync(u => u.UserName.ToLower() == username.ToLower());
    }
}