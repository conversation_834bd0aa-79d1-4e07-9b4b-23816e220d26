﻿namespace Cartrack.Fleet.User.Domain.Common;

public class UserAppSettings {
    public long UserId { get; set; }
    public string? ClientUserId { get; set; }
    public long PricingPlanId { get; set; }
    public List<AppSettings> GlobalAppSettings { get; set; }
    public List<UserAppSettingData> PricingPlanAppSettings { get; set; }
    public List<UserAppSettingData>? ParentAppSettingsValue { get; set; }
    public List<UserAppSettingData> ClientAppSettings { get; set; }
    public List<UserAppSettingData> SubUserAppSettings { get; set; }
    public List<UserAppSettingData>? AppSettings { get; set; }
}