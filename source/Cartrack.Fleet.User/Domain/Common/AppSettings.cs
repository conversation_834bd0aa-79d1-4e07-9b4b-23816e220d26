﻿namespace Cartrack.Fleet.User.Domain.Common;

public class AppSettings {
    public string? Name{ get; set; }
    public string? Value{ get; set; }
    public bool? IsMainUserSettingEnabled{ get; set; } // only from AppSetting Table
    public bool? IsSubUserSettingEnabled{ get; set; } // only from AppSetting Table
    public string? UserNameLimit{get;set;}
    public string? IsSubUserDefaultValueIfAllowed{get;set;}
    public string? IsMainUserRequiredValueToAllowSubUserOverride { get; set; }
}