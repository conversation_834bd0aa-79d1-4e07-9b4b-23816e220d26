﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.IO.Http;
using Cartrack.Fleet.User.IO.Sql;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;

namespace Cartrack.Fleet.User;

public static class UserStartup {
    public static void Register(IServiceCollection builderServices, AppSettings appSetting) {
        //builderServices.AddScoped<IAuthRepository, AuthRepository>();
        builderServices.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<UserController>());
        builderServices.AddControllers().AddApplicationPart(typeof(UserController).Assembly);
        builderServices.AddScoped<IUserRepository, UserRepository>();
        builderServices.AddScoped<IUserAppSettingsRepository, UserAppSettingsRepository>();
    }
}