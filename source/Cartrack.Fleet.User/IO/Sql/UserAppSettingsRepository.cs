﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Common.Domain;
using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.User.Domain.Common;
using Cartrack.Fleet.User.Domain.Permissions;
using Cartrack.Fleet.User.Infratructure;
using Microsoft.EntityFrameworkCore;
using System.Security.Principal;
using System;

namespace Cartrack.Fleet.User.IO.Sql;

public partial class UserAppSettingsRepository(AppFleetDbContext fleetDbContext, Fleet.Common.AppSettings appSettings) : IUserAppSettingsRepository {
    
    public async Task<UserAppSettings?> GetUserAppSettingsById(ClientUser user, string?[] settingsName) {

        var pricingPlanId = -1; // not used currently in SCDF / SPF
        
        if (settingsName.Length > 0) {
            await this.GetGlobalAppSettings(settingsName);
        }
        
        // Ensure the global app settings enabled to use it.
        var appSettings = await this.GetGlobalAppSettings(settingsName);
        settingsName = appSettings.Select(x => x.Name).ToArray();
        
        var clientAppSettings = await this.GetUserAppSettingsInMainClientById(user.UserId, appSettings);
        var subUserAppSettings = await this.GetUserAppSettingsInSubUserById(user.UserId, user.ClientUserId, appSettings);
        var pricingPlanAppSettings = await this.GetUserAppSettingsPricingPlanById(pricingPlanId, appSettings);

        var userAppSettings = new UserAppSettings {
            UserId = user.UserId,
            ClientUserId = user.ClientUserId,
            PricingPlanId = pricingPlanId,
            GlobalAppSettings = appSettings,
            /*ParentSettingsValue = fleetDbContext.ClientAppSettings
                    .Where(p =>
                        p.ClientId == user.UserId &&
                        p.SettingsName == aps.SettingsName &&
                        settingsName.Contains(p.SettingsName))
                    .Select(p => p.SettingsValue)
                    .FirstOrDefault(),
            */
            ClientAppSettings = clientAppSettings,
            SubUserAppSettings=  subUserAppSettings,
            PricingPlanAppSettings = pricingPlanAppSettings,
            AppSettings = null
        };
        
        var appSettingsData = this.GetProcessedAppSettings(userAppSettings, settingsName);
        userAppSettings.AppSettings = appSettingsData;
        return userAppSettings;
    }

    private List<UserAppSettingData>? GetProcessedAppSettings(UserAppSettings? userAppSettings, string[]? settingsName) {
        var dictionary = new Dictionary<string, string>();
        if (settingsName == null) return null;
        
        foreach (var sName in settingsName) {
            var globalAppSettingValue = userAppSettings.GlobalAppSettings?.FirstOrDefault(s => s.Name == sName)?.Value;
            var pricingPlanAppSettingValue = userAppSettings.PricingPlanAppSettings?.FirstOrDefault(s => s.Name == sName)?.Value;
            var clientAppSettingValue = userAppSettings.ClientAppSettings?.FirstOrDefault(s => s.Name == sName)?.Value;
            var subUserAppSettingValue = userAppSettings.SubUserAppSettings?.FirstOrDefault(s => s.Name == sName)?.Value;
            
            dictionary[sName] = globalAppSettingValue;
            if(pricingPlanAppSettingValue != null) { dictionary[sName] = pricingPlanAppSettingValue;}
            if(clientAppSettingValue != null) { dictionary[sName] = clientAppSettingValue;}
            if(subUserAppSettingValue != null) { dictionary[sName] = subUserAppSettingValue;}
        }
        
        var result = dictionary.Select(k => new UserAppSettingData {
            Name = k.Key,
            Value = k.Value
        }).ToList();
        
        return result;
    }
    
    private async Task<List<AppSettings>> GetGlobalAppSettings(string?[] settingsName) {
        var query = fleetDbContext.AppSettings.Where(aps => !aps.GroupName!.Equals("TO DELETE") && (bool)aps.Enabled!);
        if (settingsName.Length > 0) {
            query = query.Where(aps => settingsName.Contains(aps.SettingsName));
        }
        var result = await query
            .Select(a => new AppSettings {
                Name = a.SettingsName,
                Value = a.SettingsValue,
                IsMainUserSettingEnabled = a.MainUserSetting,
                IsSubUserSettingEnabled = a.SubUserSetting,
                UserNameLimit = a.UserNameLimit,
                IsSubUserDefaultValueIfAllowed = a.SubUserDefaultValueIfAllowed,
                IsMainUserRequiredValueToAllowSubUserOverride = a.MainUserRequiredValueToAllowSubUserOverride,
            })
            .ToListAsync();
        
        return result;
    }
    private async Task<List<UserAppSettingData>> GetUserAppSettingsPricingPlanById(long pricingPlanId, List<AppSettings> appSettings) {
        
        if(pricingPlanId == 0) return new List<UserAppSettingData>();
        
        var query = fleetDbContext.PricingPlanAppSettings.Where(ppaps => ppaps.PricingPlanId == pricingPlanId );
        if (appSettings.Count > 0) {
            var settingsName = appSettings.Select(a => a.Name).ToList();
            query = query.Where(ppaps => settingsName.Contains(ppaps.SettingsName));
        }
        
        var result = await query.Select(ppaps => new UserAppSettingData {
                Name = ppaps.SettingsName, 
                Value = ppaps.SettingsValue
            })
            .ToListAsync();
        
        return result;
    }

    private async Task<List<UserAppSettingData>> GetUserAppSettingsInMainClientById(long userId, List<AppSettings> appSettings) {
        
        var query = fleetDbContext.ClientAppSettings.Where(cas => cas.ClientId == userId );
        if (appSettings.Count > 0) {
            var settingsName = appSettings.Where(a => a.IsMainUserSettingEnabled == true).Select(a => a.Name).ToList();
            query = query.Where(cas => settingsName.Contains(cas.SettingsName));
        }
        
        var result = await query.Select(cas => new UserAppSettingData {
                Name = cas.SettingsName,  
                Value = cas.SettingsValue
            }).ToListAsync();
        
        return result;
    }

    private async Task<List<UserAppSettingData>> GetUserAppSettingsInSubUserById(long userId, string clientUserId, List<AppSettings> appSettings) {
        
        var query = fleetDbContext.ClientSubuserAppSettings
            .Where(csas => csas.UserId == userId && csas.SubClientId.Equals(clientUserId) );
        if (appSettings.Count > 0) {
            var settingsName = appSettings.Where(a => a.IsSubUserSettingEnabled == true).Select(a => a.Name).ToList();
            query = query.Where(csas => settingsName.Contains(csas.SettingsName));
        }
        
        var result = await query.Select(csas => new UserAppSettingData {
                    Name = csas.SettingsName,  
                    Value = csas.SettingsValue
            }).ToListAsync();

        return result;
    }

}