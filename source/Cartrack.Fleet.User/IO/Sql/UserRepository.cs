﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.User.Infratructure;
using Microsoft.EntityFrameworkCore;
using System.Security.Principal;
using System;

namespace Cartrack.Fleet.User.IO.Sql;

public partial class UserRepository(AppFleetDbContext fleetDbContext, AppTfmsCustomDbContext appTfmsCustomDbContext, AppCtDbContext ctDbContext)
    : IUserRepository {
    public async Task<ClientUser?> GetUserByUserName(long agency, string userName) {
        var user = await fleetDbContext.ClientUsers.FirstOrDefaultAsync(b =>
            b.UserId == agency && b.UserName == userName);
        return user;
    }

    //public async Task<List<ClientUser>> GetUserByRole(long agency, string roleCode) {
    //    var roleMaps = await appTfmsCustomDbContext.ClientUserRoleIdMaps
    //        .Where(r => r.RoleId.ToLower() == roleCode.ToLower())
    //        .Select(r => r.RoleUuid)
    //        .ToListAsync();

    //    var users = await fleetDbContext.ClientUsers
    //        .Where(cu => cu.UserId == agency && roleMaps.Contains(cu.ClientUserRoleId))
    //        .ToListAsync();

    //    return users;
    //}


    public async Task<ClientUser?> GetUserById(string clientUserId) {
        var user = await fleetDbContext.ClientUsers.FirstOrDefaultAsync(b => b.ClientUserId == clientUserId);
        return user;
    }

    //public async Task<List<ClientUser>> GetUsers(long agency) {
    //    var user = await fleetDbContext.ClientUsers.Where(b => b.UserId == agency).ToListAsync();
    //    return user;
    //}

    //public async Task<List<ClientUserDepartment>> GetUserDepartmentById(string clientUserId) {
    //    var userDepartments = await fleetDbContext.ClientUserDepartments.Where(b => b.ClientUserId == clientUserId)
    //        .ToListAsync();
    //    return userDepartments;
    //}

    public async Task<ClientUserDepartment?> GetUserDepartmentByIdAndServiceType(string clientUserId, string serviceType) {
        var userDepartment = await fleetDbContext.ClientUserDepartments
            .Where(b => b.ClientUserId == clientUserId && b.ServiceType == serviceType).FirstOrDefaultAsync();
        return userDepartment;
    }

    //public async Task<ClientUserDepartment> UpdateUserDepartment(ClientUserDepartment clientUserDepartment,
    //    long departmentIdToBeUpdated) {
    //    if (clientUserDepartment != null) {
    //        clientUserDepartment.DepartmentId = departmentIdToBeUpdated;
    //        fleetDbContext.Update(clientUserDepartment);
    //        await fleetDbContext.SaveChangesAsync();
    //    }

    //    return clientUserDepartment;
    //}

    public async Task<ClientUserDepartment> AddUserDepartment(ClientUserDepartment clientUserDepartment) {
        if (clientUserDepartment != null) {
            fleetDbContext.Update(clientUserDepartment);
            await fleetDbContext.SaveChangesAsync();
        }

        return clientUserDepartment;
    }

    public async Task<Department?> GetDepartmentInfo(long departmentId, long userId) {
        var department = await fleetDbContext.Departments
            .Where(b => b.DepartmentId == departmentId && b.UserId == userId).FirstOrDefaultAsync();
        return department;
    }

    //public async void DeleteUserById(string clientUserId) {
    //    var clientUser = await GetUserById(null, clientUserId);
    //    if (clientUser != null) {
    //        clientUser.IsDeleted = true;
    //        fleetDbContext.Update(clientUser);
    //        fleetDbContext.SaveChanges();
    //    }
    //}    

    //public async Task<ClientUser> UpdateUser(ClientUser clientUser, string email, string cellNumber,
    //    string? clientUserRoleId) {
    //    if (clientUser != null) {
    //        clientUser.EMail = email;
    //        clientUser.CellNumber = cellNumber;
    //        clientUser.ClientUserRoleId = clientUserRoleId;
    //        fleetDbContext.Update(clientUser);
    //        await fleetDbContext.SaveChangesAsync();
    //    }

    //    return clientUser;
    //}

    public async Task<ClientUser> CreateUser(ClientUser clientUser) {
        if (clientUser != null) {
            fleetDbContext.Add(clientUser);
            await fleetDbContext.SaveChangesAsync();
        }

        return clientUser;
    }

    public async Task<ClientUserState> AddClientUserState(ClientUserState clientUserState) {
        if (clientUserState != null) {
            fleetDbContext.Add(clientUserState);
            await fleetDbContext.SaveChangesAsync();
        }

        return clientUserState;
    }

    public async Task<ClientUserRoleIdMap> GetUserRoleByCode(string RoleCode) {
        var user = await appTfmsCustomDbContext.ClientUserRoleIdMaps.FirstOrDefaultAsync(b => b.RoleId == RoleCode);
        return user;
    }

    public async Task<ClientLoginHistory> AddUserLastLogin(ClientLoginHistory clientLoginHistory) {
        if (clientLoginHistory != null) {
            fleetDbContext.Add(clientLoginHistory);
            await fleetDbContext.SaveChangesAsync();
        }

        return clientLoginHistory;
    }

    //public async Task<ClientUserState?> GetUserStatusById(string clientUserId) {
    //    var statusIds = new[] {
    //        UserDriverStatus.Enabled, UserDriverStatus.Disabled
    //    };
    //    var clientUserStates = await fleetDbContext.ClientUserStates
    //        .Where(b => b.ClientUserId == clientUserId && statusIds.Contains(b.ClientUserStatusId))
    //        .FirstOrDefaultAsync();

    //    return clientUserStates;
    //}

    //public async Task<ClientUserState> UpdateUserStatus(ClientUserState clientUserState) {
    //    if (clientUserState != null) {
    //        fleetDbContext.Update(clientUserState);
    //        await fleetDbContext.SaveChangesAsync();
    //    }
    //    return clientUserState;
    //}
    //public async Task<ClientUserState> UpdateUserStatus(ClientUserState clientUserState, string clientActionId,
    //    int desiredStatusId) {
    //    if (clientUserState != null) {
    //        fleetDbContext.ClientUserStates.Remove(clientUserState);

    //        var clientUserStateObj = new ClientUserState() {
    //            ClientUserId = clientUserState.ClientUserId,
    //            ClientUserStatusId = desiredStatusId,
    //            ClientActionId = clientActionId,
    //            Cts = DateTime.UtcNow,
    //            Uts = DateTime.UtcNow,
    //        };

    //        fleetDbContext.ClientUserStates.Add(clientUserStateObj);
    //        await fleetDbContext.SaveChangesAsync();
    //    }

    //    return clientUserState;
    //}

    //public async Task<ClientAction> AddClientAction(string clientUserId, string note, long userId) {
    //    var ClientActioneObj = new ClientAction() {
    //        ClientUserId = clientUserId,
    //        UserId = userId,
    //        Note = note,
    //        ClientActionId = Guid.NewGuid().ToString(),
    //        NoteTs = DateTime.UtcNow,
    //        Cts = DateTime.UtcNow,
    //        Uts = DateTime.UtcNow,
    //    };

    //    fleetDbContext.ClientActions.Add(ClientActioneObj);
    //    await fleetDbContext.SaveChangesAsync();
    //    return ClientActioneObj;
    //}


    //public async Task<ClientDriver> GetDriverByClientUserId(string clientUserId) {
    //    var clientUserStates = await fleetDbContext.ClientDrivers
    //        .Where(b => b.ClientUserId == clientUserId)
    //        .FirstOrDefaultAsync();

    //    return clientUserStates;
    //}

    public async Task<ClientDriverState> GetDriverStatusById(string clientDriverId) {
        var statusIds = new[] {
            UserDriverStatus.Enabled, UserDriverStatus.Disabled
        };
        var clientDriverStates = await fleetDbContext.ClientDriverStates
            .Where(b => b.ClientDriverId == clientDriverId && statusIds.Contains(b.ClientDriverStatusId))
            .FirstOrDefaultAsync();

        return clientDriverStates;
    }
}