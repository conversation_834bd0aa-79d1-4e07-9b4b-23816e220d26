﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.User.Domain.Common;
using Cartrack.Fleet.User.Domain.Permissions;

namespace Cartrack.Fleet.User.IO.Sql;

public interface IUserAppSettingsRepository {
    Task<UserAppSettings?> GetUserAppSettingsById(ClientUser user, string?[] settingsName);

    Task<SetOfPermissions?> GetPermissions(string clientUserId);
}