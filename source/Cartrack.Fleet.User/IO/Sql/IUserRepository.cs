﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.User.Domain.UserDataAccess;

namespace Cartrack.Fleet.User.IO.Sql;

public interface IUserRepository {
    Task<ClientUser?> GetUserByUserName(long agency, string userName);

    Task<List<ClientUser>> GetUserByRole(long agency, string userName);

    Task<ClientUser?> GetUserById(string clientUserId);

    Task<List<ClientUser>> GetUsers(long agency);

    Task<List<ClientUserDepartment>> GetUserDepartmentById(string clientUserId);

    Task<ClientUserDepartment?> GetUserDepartmentByIdAndServiceType(string clientUserId, string serviceType);

    Task<ClientUserDepartment> UpdateUserDepartment(ClientUserDepartment clientUserDepartment,
        long departmentIdToBeUpdated);

    Task<ClientUserDepartment> AddUserDepartment(ClientUserDepartment clientUserDepartment);

    Task<Department?> GetDepartmentInfo(long departmentId, long userId);

    Task<ClientUser> DeleteUserById(ClientUser clientUser);

    Task<ClientUser> UpdateUser(ClientUser clientUser, string email, string cellNumber, string? clientUserRoleId);

    Task<ClientUser> CreateUser(ClientUser clientUser);

    Task<ClientUserState> AddClientUserState(ClientUserState clientUserState);

    Task<ClientUserRoleIdMap> GetUserRoleByCode(string RoleCode);

    Task<ClientLoginHistory> AddUserLastLogin(ClientLoginHistory clientLoginHistory);

    Task<ClientUserState?> GetUserStatusById(string clientUserId);

    Task<ClientUserState> UpdateUserStatus(ClientUserState clientUserState, string clientActionId, int desiredStatusI);

    Task<ClientAction> AddClientAction(string clientUserId, string note, long userId);

    Task<ClientDriver?> GetDriverByClientUserId(string clientUserId);

    Task<ClientDriverState> GetDriverStatusById(string clientDriverId);
    
    Task<DriversAndGroupsAccess?> GetDriverAndGroupsDataAccessById(string clientUserId);
    Task<VehiclesAndGroupsAccess?> GetVehicleAndGroupsDataAccessById(string clientUserId);
}