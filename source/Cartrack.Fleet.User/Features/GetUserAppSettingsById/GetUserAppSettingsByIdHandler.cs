﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetUserAppSettingsById;

public class GetUserAppSettingsByIdHandler(IUserRepository repo, IUserAppSettingsRepository appSettingsRepo, 
    IHttpContextAccessor context,
    ILogger<GetUserAppSettingsByIdHandler> logger)
    : IRequestHandler<GetUserAppSettingsByIdRequest, GetUserAppSettingsByIdResponse> {
    public async Task<GetUserAppSettingsByIdResponse> Handle(GetUserAppSettingsByIdRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.ClientUserId, nameof(request.ClientUserId));
            logger.LogInformation("[{TraceId}] Retrieving the user permissions with clientUserId={clientUserId}", context.HttpContext?.TraceIdentifier ?? "", request.ClientUserId);

            var user = await repo.GetUserById(request.ClientUserId);
            Requires.IsTrue(() => user != null, "Client User does not exist.");
            
            var settingsName = Array.Empty<string>();
            
            var userAppSettings = await appSettingsRepo.GetUserAppSettingsById(user!, settingsName);
            if (userAppSettings != null) {
                return new GetUserAppSettingsByIdResponse(userAppSettings);
            }

            return new GetUserAppSettingsByIdResponse(null, new Exception("The client user app settings doesn't exist")) {
                IsServerError = false
            };
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving client user app settings", context.HttpContext?.TraceIdentifier ?? "");
            return new GetUserAppSettingsByIdResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving client user permissions", context.HttpContext?.TraceIdentifier ?? "");
            return new GetUserAppSettingsByIdResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}