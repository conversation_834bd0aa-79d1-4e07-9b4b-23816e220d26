﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Domain.Common;
using Cartrack.Fleet.User.Features.GetUserAppSettingsById;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpGet]
    [Route("getappsettingsbyid/{clientUserId}")]
    public async Task<ActionResult<GetUserAppSettingsByIdResponse>> GetUserAppSettingsById(string clientUserId) {
        var resp = await this._mediator.Send(new GetUserAppSettingsByIdRequest(clientUserId));
        return resp.ToContentResult<GetUserAppSettingsByIdResponse, UserAppSettings>(this.HttpContext);
    }
}