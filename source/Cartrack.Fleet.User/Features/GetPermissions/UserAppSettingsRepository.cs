﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common.Domain;
using Cartrack.Fleet.User.Domain.Permissions;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.User.IO.Sql;

public partial class UserAppSettingsRepository {
    private static readonly ConcurrentCache<string, SetOfPermissions?> CachedPermissions = new();

    public async Task<SetOfPermissions?> GetPermissions(string clientUserId) {
        var clientUser2 = await fleetDbContext.ClientUsers.FirstOrDefaultAsync(t => t.ClientUserId == clientUserId || t.ClientUserRoleId == clientUserId);
        if (clientUser2 == null)
            return null;

        var permissions = await CachedPermissions.GetOrAddAsync(
            clientUserId, 
            async _ => await this.TryGetPermissions(clientUser2),
            expiry: appSettings.CacheExpiry,
            keepAlive: true);
        return permissions;
    }

    private async Task<SetOfPermissions?> TryGetPermissions(ClientUser clientUser) {
        var allAppSettings = await fleetDbContext.AppSettings.Where(t => (bool)t.Enabled!).ToArrayAsync();
        var subUserSettings = await GetSubUserSettings();
        var mainUserSettings = await GetMainUserSettings();

        var setOfPermissions = new SetOfPermissions();
        Read(setOfPermissions);
        Read(setOfPermissions.IssuancePermissions);
        Read(setOfPermissions.MapPermissions);
        Read(setOfPermissions.ReportPermissions);
        Read(setOfPermissions.UserPermissions);
        Read(setOfPermissions.IssuancePermissions);
        Read(setOfPermissions.ListPermissions);
        Read(setOfPermissions.ListPermissions.DriverPermissions);
        Read(setOfPermissions.ListPermissions.VehiclePermissions);
        return setOfPermissions;

        void Read<T>(T permissionObject) {
            foreach (var p in typeof(T).GetProperties()) {
                var settingsNameAttribute = Attribute.GetCustomAttribute(p, typeof(SettingsNameAttribute)) as SettingsNameAttribute;
                var settingsName = settingsNameAttribute?.Name ?? "";
                if (string.IsNullOrWhiteSpace(settingsName)) {
                    continue;
                }

                var item = allAppSettings.FirstOrDefault(t => t.SettingsName.Trim().ToUpperInvariant() == settingsName.Trim().ToUpperInvariant());
                switch (item) {
                    case null:
                        continue;
                    case { SubUserSetting: true }: {
                        if (subUserSettings.TryGetValue(settingsName, out var value)) {
                            p.SetValue(permissionObject, bool.Parse(value));
                        }

                        break;
                    }
                    case { MainUserSetting: true }: {
                        if (mainUserSettings.TryGetValue(settingsName, out var value)) {
                            p.SetValue(permissionObject, bool.Parse(value));
                        }

                        break;
                    }
                }
            }
        }

        async Task<Dictionary<string, string>> GetMainUserSettings() {
            var settings = new Dictionary<string, string>();
            var settingsFromMainUser = await fleetDbContext.ClientAppSettings.Where(t => t.ClientId == clientUser.UserId).ToArrayAsync();
            foreach (var item in settingsFromMainUser) {
                settings[item.SettingsName] = item.SettingsValue.Trim();
            }

            return settings;
        }

        async Task<Dictionary<string, string>> GetSubUserSettings() {
            var settings = new Dictionary<string, string>();

            if (clientUser.IsRole) {
                var settingsFromRole = await fleetDbContext.ClientSubuserAppSettings.Where(t => t.SubClientId == clientUser.ClientUserId).ToArrayAsync();
                foreach (var item in settingsFromRole) {
                    settings[item.SettingsName] = item.SettingsValue.Trim();
                }

                return settings;
            }

            //If this a sub-user with a role, get the role settings first then overwrite it with the sub-user settings
            var settingsFromRole2 = await fleetDbContext.ClientSubuserAppSettings.Where(t => t.SubClientId == clientUser.ClientUserRoleId).ToArrayAsync();
            var settingsFromSubUser = await fleetDbContext.ClientSubuserAppSettings.Where(t => t.SubClientId == clientUser.ClientUserId).ToArrayAsync();
            foreach (var item in settingsFromRole2) {
                settings[item.SettingsName] = item.SettingsValue.Trim();
            }

            //overwrite with sub-user settings if it exists
            foreach (var item in settingsFromSubUser) {
                settings[item.SettingsName] = item.SettingsValue.Trim();
            }

            return settings;
        }
    }
}