﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Domain.Permissions;
using Cartrack.Fleet.User.Features.GetDepartmentsById;
using Cartrack.Fleet.User.Features.GetPermissions;
using Cartrack.Fleet.User.IO.Sql;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    
    [HttpGet]
    [Route("{clientUserId}/permissions")]
    public async Task<ActionResult<GetPermissionsResponse>> GetPermissions(string clientUserId) {
        var request = new GetPermissionsRequest();
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = clientUserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetPermissionsResponse, SetOfPermissions>(this.HttpContext);
    }
}