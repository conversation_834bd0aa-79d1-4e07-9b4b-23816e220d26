﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.Domain.Permissions;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetPermissions;

public class GetPermissionsHandler(IUserAppSettingsRepository repo, IHttpContextAccessor context, ILogger<GetPermissionsHandler> logger)
    : IRequestHandler<GetPermissionsRequest, GetPermissionsResponse> {
    public async Task<GetPermissionsResponse> Handle(GetPermissionsRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.ClientUserId, nameof(request.ClientUserId));
            Requires.IsTrue(() => request.UserId > 0, () => "UserId must be greater than 0");

            var isMainAccount = long.TryParse(request.ClientUserId, out var id) && request.UserId == id;
            var permissions = isMainAccount ? SetOfPermissions.FullAccess() : await repo.GetPermissions(request.ClientUserId);
            if (permissions == null)
                return new GetPermissionsResponse(null, new Exception($"Permissions not found for client user id {request.ClientUserId} : {request.Account}"));    
            
            return new GetPermissionsResponse(permissions);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving permissions", context.HttpContext?.TraceIdentifier ?? "");
            return new GetPermissionsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving permissions", context.HttpContext?.TraceIdentifier ?? "");
            return new GetPermissionsResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}