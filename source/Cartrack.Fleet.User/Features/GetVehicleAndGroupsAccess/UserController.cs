﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Domain.Permissions;
using Cartrack.Fleet.User.Domain.UserDataAccess;
using Cartrack.Fleet.User.Features.GetDepartmentsById;
using Cartrack.Fleet.User.Features.GetVehicleAndGroupsAccess;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    
    [HttpGet]
    [Route("{clientUserId}/vehicle-and-groups-access")]
    public async Task<ActionResult<GetVehicleAndGroupsAccessResponse>> GetVehicleAndGroupsAccess(string clientUserId) {
        var request = new GetVehicleAndGroupsAccessRequest();
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = clientUserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetVehicleAndGroupsAccessResponse, VehiclesAndGroupsAccess>(this.HttpContext);
    }
}