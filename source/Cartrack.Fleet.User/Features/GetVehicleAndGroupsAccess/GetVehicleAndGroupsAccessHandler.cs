﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.Domain.Permissions;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetVehicleAndGroupsAccess;

public class GetVehicleAndGroupsAccessHandler(IUserRepository repo, IHttpContextAccessor context, ILogger<GetVehicleAndGroupsAccessHandler> logger)
    : IRequestHandler<GetVehicleAndGroupsAccessRequest, GetVehicleAndGroupsAccessResponse> {
    public async Task<GetVehicleAndGroupsAccessResponse> Handle(GetVehicleAndGroupsAccessRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.NotNullOrEmpty(request.UserId, nameof(request.UserId));
            var access = await repo.GetVehicleAndGroupsDataAccessById(request.ClientUserId);
            if (access == null)
                return new GetVehicleAndGroupsAccessResponse(null);    
            
            return new GetVehicleAndGroupsAccessResponse(access);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving driver and groups access", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehicleAndGroupsAccessResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving driver and groups access", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehicleAndGroupsAccessResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}