﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.User.Domain.UserDataAccess;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.User.IO.Sql;

public partial class UserRepository {
    private record VehiclesAndGroupsAccessRecord(string ClientUserId, string VehicleGroupName, long VehicleGroupId, int? PermissionId);
    private record VehiclesAccessRecord(string ClientUserId, string Registration, long VehicleId);
    
    public async Task<VehiclesAndGroupsAccess?> GetVehicleAndGroupsDataAccessById(string clientUserId) {
        var clientUser = await fleetDbContext.ClientUsers.FirstOrDefaultAsync(t => t.ClientUserId == clientUserId);
        if (clientUser == null)
            return null;

        var vehicles = await ctDbContext.Vehicles.Where(t => t.UserId == clientUser.UserId)
            .Select(t => new { t.VehicleId, t.Registration })
            .ToDictionaryAsync(t => t.VehicleId, t => t.Registration);
            
        var resp = new VehiclesAndGroupsAccess();
        await ReadClientUserToVehicleGroups(resp);
        await ReadClientUserVehicle(resp);
        return resp;

        async Task ReadClientUserVehicle(VehiclesAndGroupsAccess vehiclesAndGroupsAccess) {
            var all = new List<VehiclesAccessRecord>();
            if (!clientUser.IsRole) {
           
                //Add the groups this role has access
                var recordsFromRole = from cud in fleetDbContext.ClientUserToVehicles
                    where cud.ClientUserId == clientUser.ClientUserRoleId
                    select new VehiclesAccessRecord(clientUserId, "", cud.VehicleId);
                var v1 = (await recordsFromRole.ToArrayAsync()).Select(t => t with { Registration = vehicles[t.VehicleId] });
                all.AddRange(v1);
            
                //Add the groups this sub user has access
                var recordsFromUser = from cud in fleetDbContext.ClientUserToVehicles
                    where cud.ClientUserId == clientUser.ClientUserId
                    select new VehiclesAccessRecord(clientUserId, "", cud.VehicleId);
                var v = (await recordsFromUser.ToArrayAsync()).Select(t => t with { Registration = vehicles[t.VehicleId] });
                all.AddRange(v);
            }
            else {
                var recordsFromUser =  from cud in fleetDbContext.ClientUserToVehicles
                    join gd in fleetDbContext.ClientDrivers on cud.ClientUserId equals gd.ClientDriverId
                    where cud.ClientUserId == clientUser.ClientUserId && gd.IsDeleted == false
                    select new VehiclesAccessRecord(clientUserId, "", cud.VehicleId);
                var v = (await recordsFromUser.ToArrayAsync()).Select(t => t with { Registration = vehicles[t.VehicleId] });
                all.AddRange(v);
            }
            
            foreach (var i in all.DistinctBy(t => t.Registration)) {
                vehiclesAndGroupsAccess.Vehicles.Add( new VehicleAccess() {
                    CanViewVehicle = true,
                    VehicleRegistration = i.Registration
                } );
            }
            
        }
        
        async  Task ReadClientUserToVehicleGroups(VehiclesAndGroupsAccess vehiclesAndGroupsAccess) {
            var all = new List<VehiclesAndGroupsAccessRecord>();
            if (!clientUser.IsRole) {
           
                //Add the groups this role has access
                var recordsFromRole = from cud in fleetDbContext.ClientUserToVehicleGroups
                    join gd in fleetDbContext.GroupVehicles on cud.GroupVehicleId equals (int)gd.GroupVehicleId
                    where cud.GroupVehicleId != null && cud.ClientUserId == clientUser.ClientUserRoleId
                    select new VehiclesAndGroupsAccessRecord(clientUserId, gd.Name, cud.GroupVehicleId.Value, cud.PermissionId);
                
                all.AddRange(await recordsFromRole.ToArrayAsync());
            
                //Add the groups this sub user has access
                var recordsFromUser =  from cud in fleetDbContext.ClientUserToVehicleGroups
                    join gd in fleetDbContext.GroupVehicles on cud.GroupVehicleId equals (int)gd.GroupVehicleId
                    where cud.GroupVehicleId != null && cud.ClientUserId == clientUser.ClientUserId
                    select new VehiclesAndGroupsAccessRecord(clientUserId, gd.Name, cud.GroupVehicleId.Value, cud.PermissionId);
                all.AddRange(await recordsFromUser.ToArrayAsync());
            }
            else {
                var recordsFromUser = from cud in fleetDbContext.ClientUserToVehicleGroups
                    join gd in fleetDbContext.GroupVehicles on cud.GroupVehicleId equals (int)gd.GroupVehicleId
                    where cud.GroupVehicleId != null && cud.ClientUserId == clientUser.ClientUserId
                    select new VehiclesAndGroupsAccessRecord(clientUserId, gd.Name, cud.GroupVehicleId.Value, cud.PermissionId);
                all.AddRange(await recordsFromUser.ToArrayAsync());
            }
            
            var groupVehicleIds = all.DistinctBy(g => g.VehicleGroupName).Select(g => g.VehicleGroupId).ToArray();
            var clientUserIds = all.DistinctBy(g => g.ClientUserId).Select(g => g.ClientUserId).ToArray();
            var allGroupAccesses = await fleetDbContext.ClientUserToVehicleGroupAccesses
                .Where(t => clientUserIds.Contains(t.ClientUserId) && groupVehicleIds.Contains(t.GroupVehicleId ?? 0))
                .ToArrayAsync();
            
            foreach (var i in all.DistinctBy(t => t.VehicleGroupName)) {
                var accessCount = allGroupAccesses.Count(f => f.GroupVehicleId == i.VehicleGroupId);
                var v = CreateVehicleGroupAccess(i, accessCount == 1);
                vehiclesAndGroupsAccess.VehicleGroups.Add(v);
            }
        }
        
        VehicleGroupAccess CreateVehicleGroupAccess(VehiclesAndGroupsAccessRecord vehiclesAndGroupsAccessRecord, bool canViewVehicles) {
            var ga = new VehicleGroupAccess() {
                CanViewVehicles = canViewVehicles, 
                VehicleGroupId = vehiclesAndGroupsAccessRecord.VehicleGroupId,
                VehicleGroupName = vehiclesAndGroupsAccessRecord.VehicleGroupName,
            };
            if (vehiclesAndGroupsAccessRecord.PermissionId == 0) {
                ga.CanViewGroup = false;
                ga.CanEditGroup = false;
                ga.CanRemoveGroup = false;
            }
            else if (vehiclesAndGroupsAccessRecord.PermissionId == 1) {
                ga.CanViewGroup = true;
                ga.CanEditGroup = false;
                ga.CanRemoveGroup = false;
            }
            else if (vehiclesAndGroupsAccessRecord.PermissionId == 2) {
                ga.CanViewGroup = false;
                ga.CanEditGroup = true;
                ga.CanRemoveGroup = false;
            }
            else if (vehiclesAndGroupsAccessRecord.PermissionId == 3) {
                ga.CanViewGroup = false;
                ga.CanEditGroup = false;
                ga.CanRemoveGroup = true;
            }
            else if (vehiclesAndGroupsAccessRecord.PermissionId == 4) {
                ga.CanViewGroup = true;
                ga.CanEditGroup = true;
                ga.CanRemoveGroup = false;
            }
            else if (vehiclesAndGroupsAccessRecord.PermissionId == 5) {
                ga.CanViewGroup = true;
                ga.CanEditGroup = false;
                ga.CanRemoveGroup = true;
            }
            else if (vehiclesAndGroupsAccessRecord.PermissionId == 6) {
                ga.CanViewGroup = false;
                ga.CanEditGroup = true;
                ga.CanRemoveGroup = true;
            }
            else if (vehiclesAndGroupsAccessRecord.PermissionId == 7) {
                ga.CanViewGroup = true;
                ga.CanEditGroup = true;
                ga.CanRemoveGroup = true;
            }
            else {
                throw new NotSupportedException("PermissionId " + vehiclesAndGroupsAccessRecord.PermissionId + " is not supported.");
            }

            return ga;
        }
    }
}