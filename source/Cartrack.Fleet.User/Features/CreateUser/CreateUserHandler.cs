﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.CreateUser.Spf;

public class CreateUserHandler(IUserRepository repo, IHttpContextAccessor context, ILogger<CreateUserHandler> logger)
    : IRequestHandler<CreateUserRequest, CreateUserResponse> {
    public async Task<CreateUserResponse> Handle(CreateUserRequest request, CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.UserId > 0, "User Id must be greater than zero");
            Requires.NotNullOrEmpty(request.UserName, nameof(request.UserName));
            Requires.NotNullOrEmpty(request.Email, nameof(request.Email));
            Requires.NotNullOrEmpty(request.CellNumber, nameof(request.CellNumber));
            Requires.NotNullOrEmpty(request.RoleCode, nameof(request.RoleCode));
            logger.LogInformation("[{TraceId}] Retrieving the user with userName={userName}", context.HttpContext?.TraceIdentifier ?? "", request.UserName);

            var clientUser = await repo.GetUserByUserName(request.UserId, request.UserName);
            if (clientUser == null) {
                var clientUserRole = await repo.GetUserRoleByCode(request.RoleCode);
                if (clientUserRole != null) {
                    var UserObj = new ClientUser() {
                        UserId = request.UserId,
                        ClientUserId = Guid.NewGuid().ToString(),
                        UserName = request.UserName?.ToLower(),
                        PasswordHash = "XXXXXXXXXX",
                        EMail = request.Email,
                        Cts = DateTime.UtcNow,
                        Uts = DateTime.UtcNow,
                        CellNumber = request.CellNumber,
                        ClientUserRoleId = clientUserRole.RoleUuid,
                        ForceRoleSetting = false,
                    };

                    var newClientUser = await repo.CreateUser(UserObj);
                    if (newClientUser != null) {
                        var clientUserStateObj = new ClientUserState {
                            ClientActionId = Guid.NewGuid().ToString(),
                            ClientUserId = newClientUser.ClientUserId,
                            ClientUserStatusId = 10,
                            Cts = DateTime.UtcNow,
                            Uts = DateTime.UtcNow,
                        };
                        var clientUserState = await repo.AddClientUserState(clientUserStateObj);
                        return new CreateUserResponse(new ResponseStatus(UserObj.ClientUserId, true,
                            "User has been created"));
                    }
                    else {
                        var requiresException = new RequiresException("");
                        return new CreateUserResponse(new ResponseStatus(UserObj.ClientUserId, false,
                            "Update skipped: User state doesn't exist"));
                    }
                }
                else {
                    return new CreateUserResponse(new ResponseStatus(null, false,
                        "The client user role doesn't exist"));
                }
            }
            else {
                return new CreateUserResponse(new ResponseStatus(clientUser.ClientUserId, false,
                    "Update skipped: User already exists"));
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new CreateUserResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new CreateUserResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}