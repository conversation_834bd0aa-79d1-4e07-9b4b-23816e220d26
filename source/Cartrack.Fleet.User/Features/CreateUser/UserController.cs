﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.CreateUser.Spf;
using Cartrack.Fleet.User.Features.Infratructure;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpPost]
    [Route("create")]
    public async Task<ActionResult<CreateUserResponse>> UpdateUserById([FromBody] CreateUserRequest request) {
        var resp = await this._mediator.Send(new CreateUserRequest(request.UserId, request.UserName, request.Email,
            request.CellNumber, request.RoleCode, request.ServiceType));
        return resp.ToContentResult<CreateUserResponse, ResponseStatus>(this.HttpContext);
    }
}