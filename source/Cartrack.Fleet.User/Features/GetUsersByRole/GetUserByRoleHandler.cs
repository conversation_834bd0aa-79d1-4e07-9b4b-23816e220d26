﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetUsersByRole;

public class GetUserByRoleHandler(IUserRepository repo, IHttpContextAccessor context, ILogger<GetUserByRoleHandler> logger)
    : IRequestHandler<GetUserByRoleRequest, GetUserByRoleResponse> {
    public async Task<GetUserByRoleResponse> Handle(GetUserByRoleRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.RoleCode, nameof(request.RoleCode));
            Requires.IsTrue(() => request.UserId > 0, "User Id must be greater than zero");
            logger.LogInformation("[{TraceId}] [{userId}] Retrieving the user with roleCode={roleCode}",
                context.HttpContext?.TraceIdentifier ?? "",
                request.UserId,
                request.RoleCode);

            var user = await repo.GetUserByRole(request.UserId, request.RoleCode);
            if (user.Any()) {
                return new GetUserByRoleResponse(user);
            }

            return new GetUserByRoleResponse(null, new Exception("The client user doesn't exist")) {
                IsServerError = false
            };
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value", context.HttpContext?.TraceIdentifier ?? "");
            return new GetUserByRoleResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new GetUserByRoleResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}