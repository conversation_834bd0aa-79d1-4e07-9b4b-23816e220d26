﻿using Cartrack.EFCore.Models.Fleet;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.User.IO.Sql;
public partial class UserRepository {

    public async Task<List<ClientUser>> GetUserByRole(long agency, string roleCode) {
        var roleMaps = await appTfmsCustomDbContext.ClientUserRoleIdMaps
            .Where(r => r.RoleId.ToLower() == roleCode.ToLower())
            .Select(r => r.RoleUuid)
            .ToListAsync();

        var users = await fleetDbContext.ClientUsers
            .Where(cu => cu.UserId == agency && roleMaps.Contains(cu.ClientUserRoleId))
            .ToListAsync();

        return users;
    }
}
