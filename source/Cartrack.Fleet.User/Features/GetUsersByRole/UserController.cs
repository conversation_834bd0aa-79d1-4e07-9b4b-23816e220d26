﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.GetUsersByRole;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpGet]
    [Route("getbyrole")]
    public async Task<ActionResult<GetUserByRoleResponse>> GetUserByRole(long userId, string roleName) {
        var resp = await this._mediator.Send(new GetUserByRoleRequest(userId, roleName));
        return resp.ToContentResult<GetUserByRoleResponse, List<ClientUser>>(this.HttpContext);
    }
}