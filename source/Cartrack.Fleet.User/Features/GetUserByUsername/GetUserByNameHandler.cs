﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetUserByUsername;

public class GetUserByNameHandler(IUserRepository repo, IHttpContextAccessor context, ILogger<GetUserByNameHandler> logger)
    : IRequestHandler<GetUserByNameRequest, GetUserByNameResponse> {
    public async Task<GetUserByNameResponse> Handle(GetUserByNameRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.UserId > 0, "User Id must be greater than zero");
            Requires.NotNullOrEmpty(request.UserName, nameof(request.UserName));
            logger.LogInformation("[{TraceId}] [{userId}] Retrieving the user with userName={userName}", 
                context.HttpContext?.TraceIdentifier ?? "", 
                request.UserId,
                request.UserName);

            var user = await repo.GetUserByUserName(request.UserId, request.UserName);
            if (user != null) {
                return new GetUserByNameResponse(user);
            }
            else {
                return new GetUserByNameResponse(null, new RequiresException("The client user doesn't exist")) {
                    IsServerError = false
                };
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new GetUserByNameResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new GetUserByNameResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}