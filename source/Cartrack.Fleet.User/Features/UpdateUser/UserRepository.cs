﻿using Cartrack.EFCore.Models.Fleet;

namespace Cartrack.Fleet.User.IO.Sql;
public partial class UserRepository {
    public async Task<ClientUser> UpdateUser(ClientUser clientUser, string email, string cellNumber,
       string? clientUserRoleId) {
        if (clientUser != null) {
            clientUser.EMail = email;
            clientUser.CellNumber = cellNumber;
            clientUser.ClientUserRoleId = clientUserRoleId;
            fleetDbContext.Update(clientUser);
            await fleetDbContext.SaveChangesAsync();
        }

        return clientUser;
    }
}
