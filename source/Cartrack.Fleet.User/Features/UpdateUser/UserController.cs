﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.Features.UpdateUser;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpPatch]
    [Route("updatebyid")]
    public async Task<ActionResult<UpdateUserResponse>> UpdateUserById([FromBody] UpdateUserRequest request) {
        var resp = await this._mediator.Send(new UpdateUserRequest(request.ClientUserId, request.Email,
            request.CellNumber, request.RoleCode));
        return resp.ToContentResult<UpdateUserResponse, ResponseStatus>(this.HttpContext);
    }
}