﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.UpdateUser;

public class UpdateUserHandler(IUserRepository repo, IHttpContextAccessor context, ILogger<UpdateUserHandler> logger)
    : IRequestHandler<UpdateUserRequest, UpdateUserResponse> {
    public async Task<UpdateUserResponse> Handle(UpdateUserRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.ClientUserId, nameof(request.ClientUserId));
            //Requires.NotNullOrEmpty(request.email, nameof(request.email));
            //Requires.NotNullOrEmpty(request.cellNumber, nameof(request.cellNumber));
            //Requires.NotNullOrEmpty(request.roleCode, nameof(request.roleCode));
            logger.LogInformation("[{TraceId}] Retrieving the user with clientUserId={clientUserId}", context.HttpContext?.TraceIdentifier ?? "", request.ClientUserId);
            ResponseStatus? updateStatus = null;
            var clientUser = await repo.GetUserById(request.ClientUserId);
            if (clientUser != null) {
                var emailToBeUpdated = string.IsNullOrEmpty(request.Email) ? clientUser.EMail : request.Email;
                var cellToBeUpdated = string.IsNullOrEmpty(request.CellNumber)
                    ? clientUser.CellNumber
                    : request.CellNumber;
                var clientUserRole = await repo.GetUserRoleByCode(request.RoleCode);
                var roleToBeUpdated = string.IsNullOrEmpty(clientUserRole.RoleUuid)
                    ? clientUser.ClientUserRoleId
                    : clientUserRole?.RoleUuid;

                var updatedClientUser =
                    await repo.UpdateUser(clientUser, emailToBeUpdated, cellToBeUpdated, roleToBeUpdated);
                updateStatus = new ResponseStatus(clientUser.ClientUserId, true, "User details have been updated");
                return new UpdateUserResponse(updateStatus);
            }

            updateStatus = new ResponseStatus(request.ClientUserId, false,
                "Update skipped: User account doesn't exist");
            return new UpdateUserResponse(updateStatus);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new UpdateUserResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new UpdateUserResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}