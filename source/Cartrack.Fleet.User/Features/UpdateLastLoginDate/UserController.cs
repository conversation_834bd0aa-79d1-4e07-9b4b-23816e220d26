﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.Features.UpdateLastLoginDate;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpPatch]
    [Route("updatelastlogin")]
    public async Task<ActionResult<UpdateLastLoginResponse>> UpdateLastLoginById(
        [FromBody] UpdateLastLoginRequest request) {
        var resp = await this._mediator.Send(new UpdateLastLoginRequest(request.UserId, request.ClientUserId,
            request.LastLoginDate));
        return resp.ToContentResult<UpdateLastLoginResponse, ResponseStatus>(this.HttpContext);
    }
}