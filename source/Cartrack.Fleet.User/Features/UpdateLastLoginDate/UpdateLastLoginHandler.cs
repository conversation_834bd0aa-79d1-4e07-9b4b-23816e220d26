﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.CT;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.UpdateLastLoginDate;

public class UpdateLastLoginHandler(IUserRepository repo, IHttpContextAccessor context, ILogger<UpdateLastLoginHandler> logger)
    : IRequestHandler<UpdateLastLoginRequest, UpdateLastLoginResponse> {
    public async Task<UpdateLastLoginResponse> Handle(UpdateLastLoginRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.ClientUserId, nameof(request.ClientUserId));
            //Requires.NotNullOrEmpty(request.lastLoginDate, nameof(request.email));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving the user with clientUserId={clientUserId}", 
                context.HttpContext?.TraceIdentifier ?? "",
                request.UserId,
                request.ClientUserId);
            
            ResponseStatus? updateStatus = null;
            var clientUser = await repo.GetUserById(request.ClientUserId);
            if (clientUser != null) {
                var userLastLoginObj = new ClientLoginHistory {
                    SubClientId = clientUser.ClientUserId,
                    EventTs = request.LastLoginDate.ToUniversalTime(),
                    ClientId = request.UserId,
                    LoginSourceId = 60,
                    LoginSourceIp = "0.0.0.0",
                    ClientDetails = "60"
                };
                var clientUserState = await repo.AddUserLastLogin(userLastLoginObj);
                updateStatus = new ResponseStatus(clientUser.ClientUserId, true, "Last login date has been updated");
                return new UpdateLastLoginResponse(updateStatus);
            }

            updateStatus = new ResponseStatus(request.ClientUserId, false, "Update skipped: User account doesn't exist");
            return new UpdateLastLoginResponse(updateStatus);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new UpdateLastLoginResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new UpdateLastLoginResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}