﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.CT;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetUsers;

public class GetUserHandler(IUserRepository repo, IHttpContextAccessor context, ILogger<GetUserHandler> logger)
    : IRequestHandler<GetUserRequest, GetUserResponse> {
    public async Task<GetUserResponse> Handle(GetUserRequest request, CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.UserId > 0, "User Id must be greater than zero");
            logger.LogInformation("[{TraceId}] [{userId}] Retrieving the users", context.HttpContext?.TraceIdentifier ?? "", request.UserId);

            var users = await repo.GetUsers(request.UserId);
            if (users.Any()) {
                return new GetUserResponse(users);
            }
            else {
                return new GetUserResponse(null, new Exception("Client users doesn't exist")) {
                    IsServerError = false
                };
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new GetUserResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new GetUserResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}