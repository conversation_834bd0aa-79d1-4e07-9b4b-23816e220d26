﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.GetUsers;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpGet]
    [Route("{userId}")]
    public async Task<ActionResult<GetUserResponse>> GetUsers(long userId) {
        var resp = await this._mediator.Send(new GetUserRequest(userId));
        return resp.ToContentResult<GetUserResponse, List<ClientUser>>(this.HttpContext);
    }
}