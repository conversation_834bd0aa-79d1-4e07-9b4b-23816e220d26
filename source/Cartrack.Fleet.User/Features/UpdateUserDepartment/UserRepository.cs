﻿using Cartrack.EFCore.Models.Fleet;

namespace Cartrack.Fleet.User.IO.Sql;
public partial class UserRepository {
    public async Task<ClientUserDepartment> UpdateUserDepartment(ClientUserDepartment clientUserDepartment,
        long departmentIdToBeUpdated) {
        if (clientUserDepartment != null) {
            clientUserDepartment.DepartmentId = departmentIdToBeUpdated;
            fleetDbContext.Update(clientUserDepartment);
            await fleetDbContext.SaveChangesAsync();
        }
        return clientUserDepartment;
    }
}
