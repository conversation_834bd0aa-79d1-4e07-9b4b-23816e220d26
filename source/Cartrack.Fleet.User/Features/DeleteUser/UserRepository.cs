﻿using Cartrack.EFCore.Models.Fleet;

namespace Cartrack.Fleet.User.IO.Sql;
public partial class UserRepository {
    public async Task<ClientUser> DeleteUserById(ClientUser clientUser) {
        if (clientUser != null) {
            clientUser.IsDeleted = true;
            fleetDbContext.Update(clientUser);
            await fleetDbContext.SaveChangesAsync();
        }
        return clientUser;
    }
}
