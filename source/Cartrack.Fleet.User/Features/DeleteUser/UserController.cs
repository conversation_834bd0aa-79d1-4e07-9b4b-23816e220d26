﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.DeleteUser;
using Cartrack.Fleet.User.Features.Infratructure;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpDelete]
    [Route("{clientUserId}")]
    public async Task<ActionResult<DeleteUserResponse>> DeleteUserById(string clientUserId) {
        var resp = await this._mediator.Send(new DeleteUserRequest(clientUserId));
        return resp.ToContentResult<DeleteUserResponse, ResponseStatus>(this.HttpContext);
    }
}