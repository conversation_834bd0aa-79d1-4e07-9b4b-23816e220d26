﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.Features.AddUserDepartment;
using Cartrack.Fleet.User.Features.GetUserById;
using Cartrack.Fleet.User.Features.GetUsers;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.DeleteUser;

public class DeleteUserHandler(IUserRepository repo, IHttpContextAccessor context, ILogger<DeleteUserHandler> logger)
    : IRequestHandler<DeleteUserRequest, DeleteUserResponse> {
    public async Task<DeleteUserResponse> Handle(DeleteUserRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.UserId, nameof(request.UserId));
            logger.LogInformation("[{TraceId}] Retrieving the user with userUserId={userUserId}", context.HttpContext?.TraceIdentifier ?? "", request.UserId);
            var clientUser = await repo.GetUserById(request.UserId);
            if (clientUser != null) {
                if (clientUser.IsDeleted != null && !clientUser.IsDeleted.Value) {
                    var deletedClientUser = await repo.DeleteUserById(clientUser);
                    return new DeleteUserResponse(new ResponseStatus(request.UserId, true, "Delete skipped: User has been deleted"));
                }
                else {
                    return new DeleteUserResponse(new ResponseStatus(request.UserId, false, "The user has already been deleted"));
                }
            }
            else {
                return new DeleteUserResponse(new ResponseStatus(request.UserId, false, "Delete skipped: User doesn't exist"));
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new DeleteUserResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new DeleteUserResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}