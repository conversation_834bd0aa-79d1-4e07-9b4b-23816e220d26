﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.Domain.Permissions;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetDriverAndGroupsAccess;

public class GetDriverAndGroupsAccessHandler(IUserRepository repo, IHttpContextAccessor context, ILogger<GetDriverAndGroupsAccessHandler> logger)
    : IRequestHandler<GetDriverAndGroupsAccessRequest, GetDriverAndGroupsAccessResponse> {
    public async Task<GetDriverAndGroupsAccessResponse> Handle(GetDriverAndGroupsAccessRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.NotNullOrEmpty(request.UserId, nameof(request.UserId));
            var access = await repo.GetDriverAndGroupsDataAccessById(request.ClientUserId);
            if (access == null)
                return new GetDriverAndGroupsAccessResponse(null);    
            
            return new GetDriverAndGroupsAccessResponse(access);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving driver and groups access", context.HttpContext?.TraceIdentifier ?? "");
            return new GetDriverAndGroupsAccessResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving driver and groups access", context.HttpContext?.TraceIdentifier ?? "");
            return new GetDriverAndGroupsAccessResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}