﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.User.Domain.UserDataAccess;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.User.IO.Sql;

public partial class UserRepository {
    private record DriversAndGroupsAccessRecord(string ClientUserId, string GroupName, int GroupDriverId, int? PermissionId);
    private record DriversAccessRecord(string ClientUserId, string DriverName, string DriverId, int? PermissionId);
    
    public async Task<DriversAndGroupsAccess?> GetDriverAndGroupsDataAccessById(string clientUserId) {
        var clientUser = await fleetDbContext.ClientUsers.FirstOrDefaultAsync(t => t.ClientUserId == clientUserId);
        if (clientUser == null)
            return null;

        var resp = new DriversAndGroupsAccess();
        await ReadClientUserToDriverGroups(resp);
        await ReadClientUserToDriver(resp);
        return resp;

        async Task ReadClientUserToDriver(DriversAndGroupsAccess driversAndGroupsAccess) {
            var all = new List<DriversAccessRecord>();
            if (!clientUser.IsRole) {
           
                //Add the groups this role has access
                var recordsFromRole = from cud in fleetDbContext.ClientUserToDrivers
                    join gd in fleetDbContext.ClientDrivers on cud.DriverId equals gd.ClientDriverId
                    where cud.ClientUserId == clientUser.ClientUserRoleId && gd.IsDeleted == false
                    select new DriversAccessRecord(clientUserId, gd.DriverName, gd.ClientDriverId, cud.PermissionId);
                all.AddRange(await recordsFromRole.ToArrayAsync());
            
                //Add the groups this sub user has access
                var recordsFromUser = from cud in fleetDbContext.ClientUserToDrivers
                    join gd in fleetDbContext.ClientDrivers on cud.DriverId equals gd.ClientDriverId
                    where cud.ClientUserId == clientUser.ClientUserId && gd.IsDeleted == false
                    select new DriversAccessRecord(clientUserId, gd.DriverName, gd.ClientDriverId, cud.PermissionId);
                all.AddRange(await recordsFromUser.ToArrayAsync());
            }
            else {
                var recordsFromUser = from cud in fleetDbContext.ClientUserToDrivers
                    join gd in fleetDbContext.ClientDrivers on cud.DriverId equals gd.ClientDriverId
                    where cud.ClientUserId == clientUser.ClientUserId && gd.IsDeleted == false
                    select new DriversAccessRecord(clientUserId, gd.DriverName, gd.ClientDriverId, cud.PermissionId);
                all.AddRange(await recordsFromUser.ToArrayAsync());
            }
            
            foreach (var i in all.DistinctBy(t => t.DriverName)) {
                driversAndGroupsAccess.Drivers.Add(CreateDriverAccess(i));
            }
            
        }
        
        async  Task ReadClientUserToDriverGroups(DriversAndGroupsAccess driversAndGroupsAccess) {
            var all = new List<DriversAndGroupsAccessRecord>();
            if (!clientUser.IsRole) {
           
                //Add the groups this role has access
                var recordsFromRole = from cud in fleetDbContext.ClientUserToDriverGroups
                    join gd in fleetDbContext.GroupDrivers on cud.GroupDriverId equals (int)gd.GroupDriverId
                    where cud.GroupDriverId != null && cud.ClientUserId == clientUser.ClientUserRoleId && gd.IsDeleted == false
                    select new DriversAndGroupsAccessRecord(clientUserId, gd.Name, cud.GroupDriverId.Value, cud.PermissionId);
                all.AddRange(await recordsFromRole.ToArrayAsync());
            
                //Add the groups this sub user has access
                var recordsFromUser = from cud in fleetDbContext.ClientUserToDriverGroups
                    join gd in fleetDbContext.GroupDrivers on cud.GroupDriverId equals (int)gd.GroupDriverId
                    where cud.GroupDriverId != null && cud.ClientUserId == clientUser.ClientUserId && gd.IsDeleted == false
                    select new DriversAndGroupsAccessRecord(clientUserId, gd.Name, cud.GroupDriverId.Value, cud.PermissionId);
                all.AddRange(await recordsFromUser.ToArrayAsync());
            }
            else {
                var recordsFromUser = from cud in fleetDbContext.ClientUserToDriverGroups
                    join gd in fleetDbContext.GroupDrivers on cud.GroupDriverId equals (int)gd.GroupDriverId
                    where cud.GroupDriverId != null && cud.ClientUserId == clientUserId && gd.IsDeleted == false
                    select new DriversAndGroupsAccessRecord(clientUserId, gd.Name, cud.GroupDriverId.Value, cud.PermissionId);
                all.AddRange(await recordsFromUser.ToArrayAsync());
            }
            
            var groupDriverIds = all.DistinctBy(g => g.GroupName).Select(g => g.GroupDriverId).ToArray();
            var clientUserIds = all.DistinctBy(g => g.ClientUserId).Select(g => g.ClientUserId).ToArray();
            var allGroupAccesses = await fleetDbContext.ClientUserToDriverGroupAccesses
                .Where(t => clientUserIds.Contains(t.ClientUserId) && groupDriverIds.Contains(t.GroupDriverId ?? 0))
                .ToArrayAsync();
            
            foreach (var i in all.DistinctBy(t => t.GroupName)) {
                var accessCount = allGroupAccesses.Count(f => f.GroupDriverId == i.GroupDriverId);
                driversAndGroupsAccess.DriverGroups.Add(CreateDriverGroupAccess(i, accessCount == 1));
            }
        }
        
        DriverGroupAccess CreateDriverGroupAccess(DriversAndGroupsAccessRecord driversAndGroupsAccessRecord, bool canViewDrivers) {
            var ga = new DriverGroupAccess() {
                CanViewDrivers = canViewDrivers, 
                DriverGroupId = driversAndGroupsAccessRecord.GroupDriverId, 
                DriverGroupName = driversAndGroupsAccessRecord.GroupName
            };
            if (driversAndGroupsAccessRecord.PermissionId == 0) {
                ga.CanViewGroup = false;
                ga.CanEditGroup = false;
                ga.CanRemoveGroup = false;
            }
            else if (driversAndGroupsAccessRecord.PermissionId == 1) {
                ga.CanViewGroup = true;
                ga.CanEditGroup = false;
                ga.CanRemoveGroup = false;
            }
            else if (driversAndGroupsAccessRecord.PermissionId == 2) {
                ga.CanViewGroup = false;
                ga.CanEditGroup = true;
                ga.CanRemoveGroup = false;
            }
            else if (driversAndGroupsAccessRecord.PermissionId == 3) {
                ga.CanViewGroup = false;
                ga.CanEditGroup = false;
                ga.CanRemoveGroup = true;
            }
            else if (driversAndGroupsAccessRecord.PermissionId == 4) {
                ga.CanViewGroup = true;
                ga.CanEditGroup = true;
                ga.CanRemoveGroup = false;
            }
            else if (driversAndGroupsAccessRecord.PermissionId == 5) {
                ga.CanViewGroup = true;
                ga.CanEditGroup = false;
                ga.CanRemoveGroup = true;
            }
            else if (driversAndGroupsAccessRecord.PermissionId == 6) {
                ga.CanViewGroup = false;
                ga.CanEditGroup = true;
                ga.CanRemoveGroup = true;
            }
            else if (driversAndGroupsAccessRecord.PermissionId == 7) {
                ga.CanViewGroup = true;
                ga.CanEditGroup = true;
                ga.CanRemoveGroup = true;
            }
            else {
                throw new NotSupportedException("PermissionId " + driversAndGroupsAccessRecord.PermissionId + " is not supported.");
            }

            return ga;
        }
        
        DriverAccess CreateDriverAccess(DriversAccessRecord d) {
            var ga = new DriverAccess() {
                DriverId = d.DriverId,
                DriverName = d.DriverName 
            };
            if (d.PermissionId == 0) {
                ga.CanViewDriver = false;
                ga.CanEditDriver = false;
                ga.CanDeactivateDriver = false;
            }
            else if (d.PermissionId == 1) {
                ga.CanViewDriver = true;
                ga.CanEditDriver = false;
                ga.CanDeactivateDriver = false;
            }
            else if (d.PermissionId == 2) {
                ga.CanViewDriver = false;
                ga.CanEditDriver = true;
                ga.CanDeactivateDriver = false;
            }
            else if (d.PermissionId == 3) {
                ga.CanViewDriver = false;
                ga.CanEditDriver = false;
                ga.CanDeactivateDriver = true;
            }
            else if (d.PermissionId == 4) {
                ga.CanViewDriver = true;
                ga.CanEditDriver = true;
                ga.CanDeactivateDriver = false;
            }
            else if (d.PermissionId == 5) {
                ga.CanViewDriver = true;
                ga.CanEditDriver = false;
                ga.CanDeactivateDriver = true;
            }
            else if (d.PermissionId == 6) {
                ga.CanViewDriver = false;
                ga.CanEditDriver = true;
                ga.CanDeactivateDriver = true;
            }
            else if (d.PermissionId == 7) {
                ga.CanViewDriver = true;
                ga.CanEditDriver = true;
                ga.CanDeactivateDriver = true;
            }
            else {
                throw new NotSupportedException("PermissionId " + d.PermissionId + " is not supported.");
            }

            return ga;
        }
    }
}