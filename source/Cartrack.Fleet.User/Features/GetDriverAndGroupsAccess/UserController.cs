﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Domain.Permissions;
using Cartrack.Fleet.User.Domain.UserDataAccess;
using Cartrack.Fleet.User.Features.GetDepartmentsById;
using Cartrack.Fleet.User.Features.GetDriverAndGroupsAccess;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    
    [HttpGet]
    [Route("{clientUserId}/driver-and-groups-access")]
    public async Task<ActionResult<GetDriverAndGroupsAccessResponse>> GetDriverAndGroupsAccess(string clientUserId) {
        var request = new GetDriverAndGroupsAccessRequest();
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = clientUserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetDriverAndGroupsAccessResponse, DriversAndGroupsAccess>(this.HttpContext);
    }
}