﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetDepartmentsById;

public class GetUserDepartmentHandler(IUserRepository repo, IHttpContextAccessor context, ILogger<GetUserDepartmentHandler> logger)
    : IRequestHandler<GetUserDepartmentRequest, GetUserDepartmentResponse> {
    public async Task<GetUserDepartmentResponse> Handle(GetUserDepartmentRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.UserId, nameof(request.UserId));
            // Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            // logger.LogInformation("[{Agency}] Retrieving the user with userUserId={userUserId}", request.Account,
            //     request.UserId);

            logger.LogInformation("[{TraceId}] Retrieving the user with userUserId={UserId}", context.HttpContext?.TraceIdentifier ?? "", request.UserId);
            var userDepartments = await repo.GetUserDepartmentById(request.UserId);
            return new GetUserDepartmentResponse(userDepartments);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new GetUserDepartmentResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new GetUserDepartmentResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}