﻿using Cartrack.EFCore.Models.Fleet;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.User.IO.Sql;
public partial class UserRepository {
    public async Task<List<ClientUserDepartment>> GetUserDepartmentById(string clientUserId) {
        var userDepartments = await fleetDbContext.ClientUserDepartments.Where(b => b.ClientUserId == clientUserId)
            .ToListAsync();
        return userDepartments;
    }
}
