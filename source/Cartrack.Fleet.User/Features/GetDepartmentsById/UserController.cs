﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.GetDepartmentsById;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    
    [HttpGet]
    [Route("getdepartmentbyid/{clientUserId}")]
    public async Task<ActionResult<GetUserDepartmentResponse>> GetUserDepartmentById(string clientUserId) {
        var resp = await this._mediator.Send(new GetUserDepartmentRequest(clientUserId));
        return resp.ToContentResult<GetUserDepartmentResponse, List<ClientUserDepartment>>(this.HttpContext);
    }
}