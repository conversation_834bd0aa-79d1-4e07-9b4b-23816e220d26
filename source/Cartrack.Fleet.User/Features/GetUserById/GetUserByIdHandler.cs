﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetUserById;

public class GetUserByIdHandler(IUserRepository repo, IHttpContextAccessor context, ILogger<GetUserByIdHandler> logger) : IRequestHandler<GetUserByRequest, GetUserByResponse> {
    public async Task<GetUserByResponse> Handle(GetUserByRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.UserId, nameof(request.UserId));
            logger.LogInformation("[{TraceId}] Retrieving the user with userUserId={userUserId}", context.HttpContext?.TraceIdentifier ?? "", request.UserId);

            var user = await repo.GetUserById(request.UserId);
            if (user != null) {
                return new GetUserByResponse(user);
            }
            else {
                return new GetUserByResponse(null, new Exception("The client user doesn't exist")) {
                    IsServerError = false
                };
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new GetUserByResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving booking", context.HttpContext?.TraceIdentifier ?? "");
            return new GetUserByResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}