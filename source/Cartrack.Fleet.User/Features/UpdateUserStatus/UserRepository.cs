﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.User.Infratructure;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace Cartrack.Fleet.User.IO.Sql;
public partial class UserRepository {
    public async Task<ClientDriver> GetDriverByClientUserId(string clientUserId) {
        var clientUserStates = await fleetDbContext.ClientDrivers
            .Where(b => b.ClientUserId == clientUserId)
            .FirstOrDefaultAsync();

        return clientUserStates;
    }

    public async Task<ClientUserState> UpdateUserStatus(ClientUserState clientUserState, string clientActionId,
        int desiredStatusId) {
        if (clientUserState != null) {
            fleetDbContext.ClientUserStates.Remove(clientUserState);

            var clientUserStateObj = new ClientUserState() {
                ClientUserId = clientUserState.ClientUserId,
                ClientUserStatusId = desiredStatusId,
                ClientActionId = clientActionId,
                Cts = DateTime.UtcNow,
                Uts = DateTime.UtcNow,
            };

            fleetDbContext.ClientUserStates.Add(clientUserStateObj);
            await fleetDbContext.SaveChangesAsync();
        }

        return clientUserState;
    }

    public async Task<ClientAction> AddClientAction(string clientUserId, string note, long userId) {
        var ClientActioneObj = new ClientAction() {
            ClientUserId = clientUserId,
            UserId = userId,
            Note = note,
            ClientActionId = Guid.NewGuid().ToString(),
            NoteTs = DateTime.UtcNow,
            Cts = DateTime.UtcNow,
            Uts = DateTime.UtcNow,
        };

        fleetDbContext.ClientActions.Add(ClientActioneObj);
        await fleetDbContext.SaveChangesAsync();
        return ClientActioneObj;
    }

    public async Task<ClientUserState?> GetUserStatusById(string clientUserId) {
        var statusIds = new[] {
            UserDriverStatus.Enabled, UserDriverStatus.Disabled
        };
        var clientUserStates = await fleetDbContext.ClientUserStates
            .Where(b => b.ClientUserId == clientUserId && statusIds.Contains(b.ClientUserStatusId))
            .FirstOrDefaultAsync();

        return clientUserStates;
    }
}
