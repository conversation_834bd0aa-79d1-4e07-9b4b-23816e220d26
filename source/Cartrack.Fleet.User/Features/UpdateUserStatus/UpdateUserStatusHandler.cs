﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.Infratructure;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.UpdateUserStatus;

public class UpdateUserStatusHandler(IUserRepository repo, IHttpContextAccessor context, ILogger<UpdateUserStatusHandler> logger)
    : IRequestHandler<UpdateUserStatusRequest, UpdateUserStatusResponse> {
    public async Task<UpdateUserStatusResponse> Handle(UpdateUserStatusRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.ClientUserId, nameof(request.ClientUserId));

            logger.LogInformation("[{TraceId}] Retrieving the user with clientUserId={clientUserId}", context.HttpContext?.TraceIdentifier ?? "", request.ClientUserId);

            int desiredStatusId = request.IsEnabled ? UserDriverStatus.Enabled : UserDriverStatus.Disabled;

            var clientUser = await repo.GetUserById(request.ClientUserId);
            if (clientUser == null) {
                return new UpdateUserStatusResponse(new ResponseStatus(request.ClientUserId, false,
                    "Update skipped: User account doesn't exist"));
            }

            var clientUserState = await repo.GetUserStatusById(request.ClientUserId);
            if (clientUserState == null) {
                return new UpdateUserStatusResponse(new ResponseStatus(request.ClientUserId, false,
                    "Update skipped: User state doesn't exist"));
            }

            if (clientUserState.ClientUserStatusId == desiredStatusId) {
                string statusMessage = request.IsEnabled
                    ? "Update skipped: Current status is already enabled"
                    : "Update skipped: Current status is already disabled";
                return new UpdateUserStatusResponse(new ResponseStatus(request.ClientUserId, false, statusMessage));
            }

            if (request.IsEnabled) {
                var clientDriver = await repo.GetDriverByClientUserId(request.ClientUserId);
                if (clientDriver == null) {
                    return new UpdateUserStatusResponse(new ResponseStatus(request.ClientUserId, false,
                        "Update skipped: Driver profile doesn't exist"));
                }
            }

            string note = request.IsEnabled ? "User enabled" : "User disabled";
            var clientAction = await repo.AddClientAction(request.ClientUserId, note, clientUser.UserId);
            await repo.UpdateUserStatus(clientUserState, clientAction.ClientActionId, desiredStatusId);

            string successMessage = request.IsEnabled
                ? "User account has been enabled"
                : "User account has been disabled";
            return new UpdateUserStatusResponse(new ResponseStatus(request.ClientUserId, true, successMessage));
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error updating user status", context.HttpContext?.TraceIdentifier ?? "");
            return new UpdateUserStatusResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error updating user status", context.HttpContext?.TraceIdentifier ?? "");
            return new UpdateUserStatusResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}