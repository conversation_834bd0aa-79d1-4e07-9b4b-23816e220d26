﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Cartrack.Fleet.Common\Cartrack.Fleet.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Cartrack.AppHost" Version="2.5.7" />
    <PackageReference Include="Cartrack.EFCore.Models.Fleet" Version="1.3.4" />
    <PackageReference Include="Cartrack.EFCore.Models.TfmsCustom" Version="1.2.1" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Domain\Permissions\VehiclePermissions.cs" />
    <Compile Remove="Domain\Permissions\DriverPermissions.cs" />
    <Compile Remove="Domain\Permissions\IssuancePermissions.cs" />
    <Compile Remove="Domain\Permissions\MapPermissions.cs" />
    <Compile Remove="Domain\Permissions\ReportPermissions.cs" />
    <Compile Remove="Domain\Permissions\UserPermissions.cs" />
    <Compile Remove="Features\GetPermissions\GetPermissionsRequest.cs" />
    <Compile Remove="Features\GetPermissions\GetPermissionsResponse.cs" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Domain\Permissions\" />
  </ItemGroup>

</Project>
