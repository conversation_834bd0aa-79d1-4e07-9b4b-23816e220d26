FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER app
WORKDIR /app
 

FROM gitlab.cartrack.com:5050/cartrack-base/cartrack-infrastructure-devops/cartrack-base-images/leap:15.6 AS build-env
WORKDIR /app

COPY . ./

RUN dotnet restore Cartrack.Fleet.Host/*.csproj
#RUN dotnet restore Cartrack.DeviceApi.Logic/*.csproj
RUN dotnet publish Cartrack.Fleet.Host/*.csproj -c Release -o out -v n


# Install dotnet debug tools
RUN dotnet tool install --tool-path /tools dotnet-trace \
 && dotnet tool install --tool-path /tools dotnet-counters \
 && dotnet tool install --tool-path /tools dotnet-dump \
 && dotnet tool install --tool-path /tools dotnet-gcdump 


FROM base AS final 
WORKDIR /tools
COPY --from=build-env /tools .

WORKDIR /app
COPY --from=build-env /app/out .
# Copy dotnet-tools


ENTRYPOINT ["dotnet", "Cartrack.Fleet.Host.dll"]

# docker build -t bi_vis_api .
# docker run -d -p 5001:8080 --name bi_vis_api_dev bi_vis_api