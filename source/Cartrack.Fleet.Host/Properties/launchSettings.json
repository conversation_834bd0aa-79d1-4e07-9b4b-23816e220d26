﻿{
  "profiles": {
    "Cartrack.Fleet.Host": {
      "commandName": "Project",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "APPHOST_TELEMETRY_FREQSEC": "5000",
        "APPHOST_LOG_LEVEL": "info",
        "APPHOST_SERVICE_NAME": "FleetHost",
        "APPHOST_API_PORT": "5001",
        "APPHOST_TRACE_ENABLED": "False",
        "APPHOST_TRACE_SERVER": "alerts-dev.cartrack.com",
        "APPHOST_TRACE_PORT": "7777",
        "PORT": "5105",
        "CONNECTION_STRING": "HOST=db-dev-tfms.cartrack.com;PORT=5432;USER ID=CAMS_NET;PASSWORD=masterkey;DATABASE=ct_fleet;CommandTimeout=0",
        "CT_CONNECTION_STRING": "HOST=db-dev-tfms.cartrack.com;PORT=5432;USER ID=CAMS_NET;PASSWORD=masterkey;DATABASE=cartrack;CommandTimeout=0",
        "CLICKHOUSE_CONNECTION_STRING": "Host=app-logs-as.cartrack.com;Protocol=http;Port=8123;Username=SYSDBA;Password=g8keeper",
        "AUTH_REFRESH_TOKEN_VALIDITY_DAYS": "7",
        "AUTH_TOKEN_VALIDITY_MINUTES": "30",
        "AUTH_PRIVATE_KEY_PATH": "/Users/<USER>/Projects/Work/cartrack.fleet.services/source/Cartrack.Fleet.Host/certs/private-key.pem",
        "AUTH_PUBLIC_KEY_PATH": "/Users/<USER>/Projects/Work/cartrack.fleet.services/source/Cartrack.Fleet.Host/certs/public-key.pem",
        "AUTH_ISSUER": "tfms-dev",
        "AUTH_AUDIENCE": "tfms-dev",
        "IS_VALIDATING_PASSWORD": "false",
        "MAIN_USER": "scdf00001,spf000001",
        "SCDF_BOOKINGS_CRON_SCHEDULE": "00:00:01",
        "IS_SCDF_BOOKINGS_CRON_ENABLED": "False",
        "FS_ENCRYPTION_KEY": "bfba90d9efc3bf0e89b52bc1b6be417a",
        "FS_ENCRYPTION_VECTOR": "312351bff07989769097660a56395065",
        "FS_PUBLIC_URL_SEGMENT": "share",
        
        "FS_SCDF00001_STORAGE_PROVIDER":"MinIo",
        "FS_SCDF00001_ROOT_UPLOAD_PATH":"https://web-dev-tfms.cartrack.com:9000/tfms-scdf",
        "FS_SCDF00001_MINIO_WITH_SSL": "True",
        "FS_SCDF00001_MINIO_KEY": "Yn4bSIdqKlCEKvjtkm0R",
        "FS_SCDF00001_MINIO_SECRET": "90GMVGwn6t7EDUBPlbyKHgXpkR2MM0CpMJZ6suSK",
        "FS_SCDF00001_MINIO_COMPRESS_IMAGE_SIZE_LIMIT": "1048576",
        "FS_SCDF00001_MINIO_PROXY": "",
        "FS_SCDF00001_MINIO_X509_CERT": "cert/client_cert.pfx",
        "FS_SCDF00001_MINIO_POST_TIMEOUT": "00:00:30",
        "FS_SCDF00001_MINIO_VALIDATE_SSL_CERT": "False"
      }
    }
  }
}
