﻿using Cartrack.Core.Auth.Domain;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.IdentityModel.Tokens.Jwt;

namespace Cartrack.Fleet.Host.Middleware;
public class RequestJwtAuthMiddleware {
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestJwtAuthMiddleware> _logger;

    public RequestJwtAuthMiddleware(RequestDelegate next, ILogger<RequestJwtAuthMiddleware> logger) {
        _next = next;
        _logger = logger;
    }

    public async Task Invoke(HttpContext httpContext, IServiceProvider serviceProvider) {
        var token = httpContext.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();

        if (string.IsNullOrEmpty(token)) {
            httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
            await httpContext.Response.WriteAsync("Missing token");
            return;
        }
        
        var jwtService = serviceProvider.GetRequiredService<IJwtService>();
        var isTokenExpired = jwtService.IsTokenExpired(token);
        if (isTokenExpired) {
            httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
            await httpContext.Response.WriteAsync("Token has expired!");
            return;
        }

        
        var principal = jwtService.GetPrincipalFromToken(token);

        if (principal == null) {
            httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
            await httpContext.Response.WriteAsync("Unauthorized");
            return;
        }

        httpContext.User = principal;
        await _next(httpContext);
    }
}