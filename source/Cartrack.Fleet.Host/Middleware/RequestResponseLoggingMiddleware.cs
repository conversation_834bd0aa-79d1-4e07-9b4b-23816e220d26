﻿using Cartrack.Core.Files.Domain;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Text;

namespace Cartrack.Fleet.Host.Middleware;

public class RequestResponseLoggingMiddleware {
    private const string Empty = "{Empty}";
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestResponseLoggingMiddleware> _logger;

    public RequestResponseLoggingMiddleware(RequestDelegate next, ILogger<RequestResponseLoggingMiddleware> logger)
    {
        this._next = next;
        this._logger = logger;
    }

    public async Task InvokeAsync(HttpContext context) {
        void LogIncoming() {
            this._logger.LogInformation(
                "[{Trace}] HTTP {RequestMethod} Request to {Path}?{QueryString} - Headers: {Headers}",
                context.TraceIdentifier,
                context.Request.Method,
                context.Request.Path,
                context.Request.QueryString,
                HeadersAsString(context.Request.Headers)
            );
        }

        if (context.Request.Path == "/logs") {
            LogIncoming();
            await this._next(context);
            return;
        }

        if (context.Request.ContentType == null) {
            LogIncoming();
            await this._next(context);
            return;
        }
        
        var isTextContent = IsTextContent(context.Request.ContentType);
        if (!isTextContent) {
            LogIncoming();
            await this._next(context);
            return;
        }


        await this.LogRequest(context);
        var originalBodyStream = context.Response.Body;

        // Create a new memory stream to capture response
        using var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        await this._next(context);
        await this.LogResponse(context);
        await responseBody.CopyToAsync(originalBodyStream);
    }

    private static bool IsTextContent(string? contentType) {
        if (string.IsNullOrEmpty(contentType))
            return false;
        
        var canLog =
            contentType.Contains("/xml") ||
            contentType.Contains("text/plain") ||
            contentType.Contains("/json") ||
            contentType.Contains("/xml") ||
            contentType.Contains("/csv");
        return canLog;
    }

    private async Task LogRequest(HttpContext context)
    {
        context.Request.EnableBuffering(); // Allow multiple reads

        // Read the request body
        var request = context.Request;
        var bodyStr = Empty;
        if (request.ContentLength > 0) {
            bodyStr = await new StreamReader(request.Body).ReadToEndAsync();
            request.Body.Position = 0; // Reset position for subsequent reads
        }

        // Log request details
        var headers = HeadersAsString(context.Request.Headers);
        this._logger.LogInformation(
            "[{Trace}] HTTP {RequestMethod} Request to {RequestPath}?{QueryString} - Body: {RequestBody} - Headers: {Headers}",
            context.TraceIdentifier,
            request.Method,
            request.Path,
            request.QueryString,
            bodyStr,
            headers
        );
    }

    private async Task LogResponse(HttpContext context) {
        var isTextResponse = IsTextContent(context.Response.ContentType);
        // Capture the response body
        context.Response.Body.Seek(0, SeekOrigin.Begin);
        var responseBody = $"Response body is {context.Response.ContentType ?? Empty}";
        if (context.Response.Body.Length > 0 && isTextResponse) {
            responseBody = await new StreamReader(context.Response.Body).ReadToEndAsync();
            context.Response.Body.Seek(0, SeekOrigin.Begin);
        }

        var headers = HeadersAsString(context.Response.Headers);
        if (context.Response.StatusCode < 400) {
            // Log response details
            this._logger.LogInformation(
                "[{Trace}] - HTTP {StatusCode} Response - Body: {ResponseBody} - Headers: {Headers}",
                context.TraceIdentifier,
                context.Response.StatusCode,
                responseBody,
                headers
            );
        }
        else if (context.Response.StatusCode is >= 400 and < 500) {
            // Log response details
            this._logger.LogWarning(
                "[{Trace}] - HTTP {StatusCode} Response - Body: {ResponseBody} - Headers: {Headers}",
                context.TraceIdentifier,
                context.Response.StatusCode,
                responseBody,
                headers
            );
        }
        else {
            this._logger.LogError(
                "[{Trace}] - HTTP {StatusCode} Response - Body: {ResponseBody} - Headers: {Headers}",
                context.TraceIdentifier,
                context.Response.StatusCode,
                responseBody,
                headers
            );
        }
    }

    private static string HeadersAsString(IHeaderDictionary headers) {
        var headersStrings = Empty;
        if (headers.Count > 0) {
            var headerBuilder = new StringBuilder("\n");
            foreach (var header in headers) {
                headerBuilder.AppendLine($"{header.Key}={header.Value}, ");
            }
            
            headerBuilder.Length -= 2;
            headersStrings = headerBuilder.ToString();
        }

        return headersStrings;
    }
}