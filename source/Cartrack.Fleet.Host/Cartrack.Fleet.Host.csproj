﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Cartrack.AppHost" Version="2.5.7" />
      <PackageReference Include="Cartrack.EFCore.Models.Pool" Version="1.1.0" />
      <PackageReference Include="Cartrack.EFCore.Models.TfmsCustom" Version="1.2.1" />
      <PackageReference Include="Minio.AspNetCore" Version="6.0.1" />
      <PackageReference Include="Scalar.AspNetCore" Version="1.2.74" />
      <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Cartrack.Core.Files\Cartrack.Core.Files.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Accessory\Cartrack.Fleet.Accessory.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Audit\Cartrack.Fleet.Audit.csproj" />
      <ProjectReference Include="..\Cartrack.Core.Auth\Cartrack.Core.Auth.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Booking\Cartrack.Fleet.Booking.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Common\Cartrack.Fleet.Common.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Cron\Cartrack.Fleet.Cron.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Driver\Cartrack.Fleet.Driver.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.License\Cartrack.Fleet.License.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Logs\Cartrack.Fleet.Logs.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Vehicle\Cartrack.Fleet.Vehicle.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.User\Cartrack.Fleet.User.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Update="certs\private-key.pem">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="certs\public-key.pem">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>
