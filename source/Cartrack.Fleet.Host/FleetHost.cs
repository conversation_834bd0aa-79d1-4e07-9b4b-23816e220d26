﻿using Cartrack.AppHost;
using Cartrack.AppHost.WorkerHosting;
using Cartrack.Core.Files;
using Cartrack.Fleet.Accessory;
using Cartrack.Fleet.Audit;
using Cartrack.Core.Auth;
using Cartrack.Core.Files.Features.FileDownload;
using Cartrack.Fleet.Booking;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.Domain;
using Cartrack.Fleet.Common.IO.FileSystem;
using Cartrack.Fleet.Common.IO.Http;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Cron;
using Cartrack.Fleet.Driver;
using Cartrack.Fleet.Host.Middleware;
using Cartrack.Fleet.License;
using Cartrack.Fleet.Logs;
using Cartrack.Fleet.User;
using Cartrack.Fleet.Vehicle;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IO;
using Microsoft.OpenApi.Models;
using Minio;
using Scalar.AspNetCore;
using System.Net;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;

namespace Cartrack.Fleet.Host;

public class FleetHost(string[] args, bool autoShutdown = true, uint healthCheckFrequencySec = 30)
    : MultiWorkerHost(args, autoShutdown, healthCheckFrequencySec) {
    public override Task OnShutdown() {
        return Task.CompletedTask;
    }

    public override Task OnStartup(CancellationToken token) {
        return Task.CompletedTask;
    }

    protected override void BuildHost(CancellationToken token) {
        var appSetting = new AppSettings(this.EnvVars);
        var env = this.EnvVars;

        var builder = this.Builder;

        builder.WebHost.ConfigureKestrel((context, options) => {
            options.Listen(IPAddress.Any, appSetting.Port, listenOptions => {
                listenOptions.Protocols = HttpProtocols.Http1;
            });

            options.Limits.MaxRequestBodySize = 500 * 1024 * 1024; // 500 MB
            options.Limits.MinRequestBodyDataRate = null;
            options.Limits.MaxRequestBufferSize = null; //unlimited
        });

        builder.Services.Configure<FormOptions>(x => {
            x.ValueLengthLimit = int.MaxValue;
            x.MultipartBodyLengthLimit = int.MaxValue; // I
        });
         
        builder.Services.AddCors(options => {
            options.AddPolicy("AllowAllOrigins", policy => {
                policy.AllowAnyOrigin()
                    .AllowAnyMethod()
                    .AllowAnyHeader();
            });
        });

        LogsStartup.Register(builder.Services,appSetting);
        if (appSetting.IsAuthServiceEnabled)
            AuthStartup.Register(builder.Services, appSetting);
        if (appSetting.IsFilesServiceEnabled)
            FilesStartup.Register(builder.Services, appSetting);
        if (appSetting.IsAuditServiceEnabled)
            AuditStartup.Register(builder.Services, appSetting);
        if (appSetting.IsBookingServiceEnabled)
            BookingStartup.Register(builder.Services, appSetting);
        if (appSetting.IsAccessoryServiceEnabled)
            AccessoryStartup.Register(builder.Services, appSetting);
        if (appSetting.IsDriverServiceEnabled)
            DriverStartup.Register(builder.Services, appSetting);
        if (appSetting.IsVehicleServiceEnabled)
            VehicleStartup.Register(builder.Services, appSetting);
        if (appSetting.IsUserServiceEnabled)
            UserStartup.Register(builder.Services, appSetting);
        if (appSetting.IsLicenseServiceEnabled)
            LicenseStartup.Register(builder.Services,appSetting);
        if (appSetting.IsScdfBookingsCronEnabled)
            CronStartup.Register(builder.Services,appSetting);
        
        builder.Services.AddScoped<IJwtService, JwtService>();
        builder.Services.AddScoped<IRsaKeyService, RsaKeyService>();
        builder.Services.AddScoped<IFileIo, FileIo>();

        builder.Services.AddAntiforgery();
        builder.Services.AddSingleton<AppSettings>(_ => appSetting);
        builder.Services.AddScoped<IMinioClient>(sp => {
            var context = sp.GetRequiredService<IHttpContextAccessor>();
            Requires.NotNull(context.HttpContext?.User, "LoggedInUser", _ => new Exception("User must be logged in to access this API. Please login and try again"));
            var user = AuthClaims.From(context.HttpContext!.User);
            var storage = appSetting.GetStorageSettings(user);
            Requires.IsTrue(() => (storage.ProviderType == StorageProviderType.MinIo), () => $"{user.Account} is not configured to use MinIo");
            return MinioClientBuilder.Create(storage.RootPath, storage.MinIo);
        });
        
        builder.Services.AddHttpContextAccessor();
        builder.Services.AddEntityFrameworkNpgsql();
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddDbContext<AppTfmsCustomDbContext>();
        builder.Services.AddDbContext<AppFleetDbContext>();
        builder.Services.AddDbContext<AppCtDbContext>();
        builder.Services.AddDbContext<AppPoolDbContext>();
        builder.Services.AddSwaggerGen(options => {
            options.SwaggerDoc("v1",
                new OpenApiInfo {
                    Title = "Extended Fleet Services",
                    Version = "v1",
                    Description = "Extended Fleet Services API"
                });

            options.CustomSchemaIds(type => type.FullName);
        });
        //builder.Services.AddAuthentication("CustomJwtScheme")
        //    .AddScheme<AuthenticationSchemeOptions, CustomJwtAuthenticationHandler>("CustomJwtScheme", null);

        base.BuildHost(token);

        var app = (WebApplication)this.Host;
        
        if (app.Environment.IsDevelopment()) {
            app.UseDeveloperExceptionPage();
            app.UseSwagger(options => {
                options.RouteTemplate = "/openapi/{documentName}.json";
               
            });
            app.MapScalarApiReference();
        }

        //app.UseHttpsRedirection();
        //app.UseAuthentication();
        //app.UseJwtAuthentication();
        //app.UseAuthorization();
        app.UseCors("AllowAllOrigins");
        app.MapControllers();
        app.UseWhen(
            context => !context.Request.Path.StartsWithSegments("/auth/login") && 
                       !context.Request.Path.StartsWithSegments("/auth/refresh-token") && 
                       !context.Request.Path.StartsWithSegments("/logs") &&
                       !context.Request.Path.StartsWithSegments($"/{appSetting.FsPublicUrlSegment}") &&
                       !context.Request.Path.StartsWithSegments("/scalar"),
            appBuilder => appBuilder.UseMiddleware<RequestJwtAuthMiddleware>()
        );
        
        app.UseRequestResponseLogging();
        if (appSetting.IsFilesServiceEnabled)
            app.UseCustomFileDownload();

        app.Run();
    }
}