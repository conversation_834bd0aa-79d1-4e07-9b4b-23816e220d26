services:
  tfms-services-api:
    container_name: tfms-services-api
    restart: always
    image: as-docker-registry.cartrack.com/cartrack.tfms.services/api:TAG_PLACEHOLDER
    networks:
      - cams
    ports:      
      - 6105:6105
    env_file:
      - .env-dev
    volumes:
      - /opt/tfms_services_api/logs:/app/logs
      - /opt/tfms_services_api/certs:/app/certs
    environment:
      # ----------------------
      # Generic AppHost Section
      # ---------------------      
      - APPHOST_TELEMETRY_FREQSEC=300
      - APPHOST_LOG_LEVEL=info
      - APPHOST_SERVICE_NAME=Tfms-Service-Api
      - APPHOST_API_PORT=5001
      - HEALTH_CHECK_EVERY_SECS=600
      - APPHOST_TRACE_ENABLED=True
      - APPHOST_TRACE_SERVER=web-dev-tfms.cartrack.com
      - APPHOST_TRACE_PORT=5010
      - APPHOST_TRACE_PROTOCOL=gRPC
      
      - PORT=6105            
      - TOKEN_EXPIRY_IN_SECONDS=3600
      - ISSUER=Cartrack      
      - IS_RSA_JWT_TOKEN_VALIDATION=False
      - CONNECTION_STRING=Host=db-dev-tfms.cartrack.com;Port=5432;Persist Security Info=True;Password=masterkey;Username=SYSDBA;Database=ct_fleet;
      - CLICKHOUSE_CONNECTION_STRING=Host=web-dev-tfms.cartrack.com;Protocol=http;Port=8123;Username=SYSDBA;Password=g8keeper
      - TFMS_ENCRYPTION_CYPHER=AES-256-CBC
      - TFMS_ENCRYPTION_KEY=vPWXWxv4rJ7iFQZhnQfq9z1jaO748Ps2
      - TFMS_ENCRYPTION_IV=X4Vwegf5FyXp2MSo
      - AUTH_REFRESH_TOKEN_VALIDITY_DAYS=7
      - AUTH_TOKEN_VALIDITY_MINUTES=1440
      - AUTH_AUDIENCE=tfms-dev
      - AUTH_ISSUER=tfms-dev
      - IS_VALIDATING_PASSWORD=false
      - MAIN_USER=SCDF00001,SPF000001
      - SCDF_USER_ID=302739
      - SCDF_ACCOUNT=SCDF00001
      - SCDF_BOOKINGS_CRON_SCHEDULE=00:01:00
      - SPF_USER_ID=302737
      - SPF_ACCOUNT=SPF000001
      - IS_ACCESSORY_SERVICE_ENABLED=true
      - IS_AUDIT_SERVICE_ENABLED=true
      - IS_AUTH_SERVICE_ENABLED=true
      - IS_BOOKING_SERVICE_ENABLED=true
      - IS_DRIVER_SERVICE_ENABLED=true
      - IS_USER_SERVICE_ENABLED=true
      - IS_VEHICLE_SERVICE_ENABLED=true
      - IS_LICENSE_SERVICE_ENABLED=true
      - IS_FILES_SERVICE_ENABLED=false
      - IS_SCDF_BOOKINGS_CRON_ENABLED=true
      - CACHE_EXPIRY=00:05:00
      - MINIO_WITH_SSL=True
      - MINIO_URL=web-dev.tfms.cartrack.com
      - MINIO_PORT=9000
      - MINIO_KEY=Yn4bSIdqKlCEKvjtkm0R
      - MINIO_SECRET=90GMVGwn6t7EDUBPlbyKHgXpkR2MM0CpMJZ6suSK
      - MINIO_BUCKET=fleet-tfms
      - MINIO_VALIDATE_SSL_CERT=false
      - MINIO_X509_CERT=cert/client_cert.pfx
      - MINIO_POST_TIMEOUT=00:00:30
      #- WEB_PROXY=
      
      # Files Service Settings
      - FS_MAX_FILE_SIZE_BYTES=*********      
      - FS_MAX_CALL_DURATION=00:00:00.500
      - FS_SERVER_BASE_URL=http://web-dev-tfms.cartrack.com:5105
      - FS_ENCRYPTION_KEY=bfba90d9efc3bf0e89b52bc1b6be417a
      - FS_ENCRYPTION_VECTOR=312351bff07989769097660a56395065
      - FS_PUBLIC_URL_SEGMENT=tfms-share
      
      # SPF MINIO Settings
      - FS_SPF000001_STORAGE_PROVIDER=MinIo
      - FS_SPF000001_ROOT_UPLOAD_PATH=https://web-dev-tfms.cartrack.com:9000/tfms-spf
      - FS_SPF000001_MINIO_WITH_SSL=True
      - FS_SPF000001_MINIO_KEY=Yn4bSIdqKlCEKvjtkm0R
      - FS_SPF000001_MINIO_SECRET=90GMVGwn6t7EDUBPlbyKHgXpkR2MM0CpMJZ6suSK
      - FS_SPF000001_MINIO_COMPRESS_IMAGE_SIZE_LIMIT=1048576
      - FS_SPF000001_MINIO_PROXY=
      - FS_SPF000001_MINIO_X509_CERT=cert/client_cert.pfx
      - FS_SPF000001_MINIO_POST_TIMEOUT=00:00:30
      
      # SCDF MINIO Settings
      - FS_SCDF00001_STORAGE_PROVIDER=MinIo
      - FS_SCDF00001_ROOT_UPLOAD_PATH=https://web-dev-tfms.cartrack.com:9000/tfms-scdf       
      - FS_SCDF00001_MINIO_WITH_SSL=True
      - FS_SCDF00001_MINIO_KEY=Yn4bSIdqKlCEKvjtkm0R
      - FS_SCDF00001_MINIO_SECRET=90GMVGwn6t7EDUBPlbyKHgXpkR2MM0CpMJZ6suSK
      - FS_SCDF00001_MINIO_COMPRESS_IMAGE_SIZE_LIMIT=1048576
      - FS_SCDF00001_MINIO_PROXY=
      - FS_SCDF00001_MINIO_X509_CERT=cert/client_cert.pfx
      - FS_SCDF00001_MINIO_POST_TIMEOUT=00:00:30
      - FS_SCDF00001_MINIO_VALIDATE_SSL_CERT=False
      
  apphost-trace:
    container_name: apphost-trace
    restart: always
    image: as-docker-registry.cartrack.com/apphost-fastlog:R1.1.7
    ports:
      - "7777:7777/udp"
      - "5010:5001/tcp"
    environment:
      - APPHOST_TELEMETRY_FREQSEC=300
      - APPHOST_LOG_LEVEL=info
      - APPHOST_SERVICE_NAME=AppHost-Trace
      - APPHOST_API_PORT=5001
      - APPHOST_TRACE_ENABLED=False
      - APPHOST_TRACE_SERVER=web-dev-tfms.cartrack.com
      - APPHOST_TRACE_PORT=7777
      - BATCH_SIZE=50
      - UDP_PORT:7777
      - CLICKHOUSE_CONNECTION=Host=web-dev-tfms.cartrack.com;Protocol=http;Port=8123;Username=SYSDBA;Password=g8keeper
      - CLICKHOUSE_FLAGS_PATH=/opt/tfms_services_api/trace/clickhouse/data/flags
      - DAYS_TO_KEEP=14
      - MAX_TIME_IN_CACHE_SEC=60
    logging:
      driver: "json-file"
      options:
        max-file: "5"
        max-size: "50m"
    volumes:
      - /opt/tfms_services_api/trace/apphost-trace/logs:/app/logs
      - /opt/tfms_services_api/trace/apphost-trace/db:/app/db

  clickhouse:
      image: clickhouse/clickhouse-server
      #deploy:
      #  resources:
      #    limits:
      #      memory: "5G"
      #user: "101:101"
      container_name: clickhouse
      hostname: clickhouse
      environment:
        - CLICKHOUSE_USER=SYSDBA
        - CLICKHOUSE_PASSWORD=g8keeper
      volumes:
        - /opt/tfms_services_api/trace/clickhouse/data:/var/lib/clickhouse/
        - /opt/tfms_services_api/trace/clickhouse/logs:/var/log/clickhouse-server/
        #- /opt/clickhouse/config/config.xml:/etc/clickhouse-server/config.d/config.xml
        - /opt/tfms_services_api/trace/clickhouse/users/:/etc/clickhouse-server/users.d/
      ports:
        - "8123:8123"
        - "9010:9000"



networks:
  cams:
    external: true