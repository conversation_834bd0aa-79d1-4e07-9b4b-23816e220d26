﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Logs.IO.Clickhouse;
using Cartrack.Fleet.Logs.IO.Http;
using Microsoft.Extensions.DependencyInjection;

namespace Cartrack.Fleet.Logs;

public class LogsStartup {
    public static void Register(IServiceCollection builderServices, AppSettings appSetting) {
        //builderServices.AddScoped<IAuthRepository, AuthRepository>();
        builderServices.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<LogsController>());
        builderServices.AddControllers().AddApplicationPart(typeof(LogsController).Assembly);
        builderServices.AddScoped<ILogsRepository, LogsRepository>();
    }
}