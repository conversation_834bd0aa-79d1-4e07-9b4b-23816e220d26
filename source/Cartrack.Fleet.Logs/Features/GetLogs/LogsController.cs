﻿ 
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.License.Features.GetLogs;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Logs.IO.Http;

public partial class LogsController {
    
    [HttpGet]
    public async Task<ActionResult<GetLogsResponse>> GetLogs([FromQuery] string query) {
        query = query.Trim();
        if (query.StartsWith("select", StringComparison.InvariantCultureIgnoreCase)) {
            var resp = await mediator.Send(new GetLogsRequest(query));
            return  resp.ToContentResult<GetLogsResponse, Domain.Log[]>(this.HttpContext);
        }

        
        var r = new GetLogsResponse(null, new Exception("Invalid query. Only 'SELECT *' queries are allowed"));
        r.<PERSON><GetLogsResponse, Domain.Log[]>(this.HttpContext);
        return r;
    }
}