﻿using Cartrack.AppHost.Channels;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.License.Features.GetLogs;
using Cartrack.Fleet.Logs.Domain;
using Cartrack.Fleet.Logs.IO.Clickhouse;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Logs.Features.GetLogs;

public class GetLogsHandler(AppSettings appSettings, ILogsRepository repo, ILogger<GetLogsHandler> logger)
    : IRequestHandler<GetLogsRequest, GetLogsResponse> {
   
    public async Task<GetLogsResponse> Handle(GetLogsRequest request, CancellationToken cancellationToken) {
        var logs = await repo.GetLogs(request.Query);
        return new GetLogsResponse(logs);
    }
}