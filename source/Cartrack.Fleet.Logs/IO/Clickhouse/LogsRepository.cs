﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Logs.Domain;
using ClickHouse.Client.ADO;
using ClickHouse.Client.Utility;

namespace Cartrack.Fleet.Logs.IO.Clickhouse;

public class LogsRepository(AppSettings appSettings) : ILogsRepository {
    public async Task<Log[]> GetLogs(string query) {
        if (string.IsNullOrEmpty(appSettings.ClickhouseConnectionString)) {
            return [new Log("System", "WRN", "System", "Clickhouse connection string is empty", DateTime.Now)];
        }

        await using var connection = new ClickHouseConnection(appSettings.ClickhouseConnectionString);
        var reader = await connection.ExecuteReaderAsync(query);

        if (reader.FieldCount != typeof(Log).GetProperties().Length) {
            return [new Log("System", "WRN", "System", "use: select * from {table}", DateTime.Now)];
        }
            
        if (reader.HasRows) {
            var rows = new List<Log>();
            while (reader.Read()) {
                var log = new Log(reader.GetString(0), reader.GetString(2), reader.GetString(3), reader.GetString(4), reader.GetDateTime(1));
                rows.Add(log);
            }

            return rows.ToArray();
        }
        
        return [new Log("System", "WRN", "System", "query returned 0 rows", DateTime.Now)];
    }
}