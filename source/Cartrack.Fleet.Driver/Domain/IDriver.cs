﻿using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Driver.IO.Sql;

namespace Cartrack.Fleet.Driver.Domain;

public interface IDriver {
    string ClientDriverId { get; set; }
    long UserId { get; set; }
    string FirstName { get; set; }
    string Surname { get; set; }
    string IdNumber { get; set; }
    Gender Gender { get; set; }
    string PhoneNumber { get; set; }
    DateTime? CreatedTs { get; set; }
    DateTime? UpdatedTs { get; set; }
    string? ClientUserId { get; set; }
    string Email { get; set; }
    List<DriverDepartment>? Departments { get; set; }
    List<DriverQdlLicense>? DriverLicenses { get; set; }
    List<DriverPdpLicense>? SpecialDriverLicenses { get; set; }
    string EmployeeNumber { get; set; }
    Task Validate();
}