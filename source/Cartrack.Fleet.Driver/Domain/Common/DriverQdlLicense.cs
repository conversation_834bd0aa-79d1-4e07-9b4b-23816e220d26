﻿namespace Cartrack.Fleet.Driver.Domain;

public class DriverQdlLicense : IDriverQdlLicense {
    public required long LicenseTypeId { get; set; }
    public string? LicenseName { get; set; }
    public string? LicenseNumber { get; set; }
    public DateTime? LicenseValidStart { get; set; }
    public DateTime? LicenseValidEnd { get; set; }
    public DateTime? LicenseFirstIssueDate { get; set; }
    public string? LicenseIssuedCountry { get; set; }
    public int? LicensePoints { get; set; }
    public string? LicenseDriverRestrictions { get; set; }
}

