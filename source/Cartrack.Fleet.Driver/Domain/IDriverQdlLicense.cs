﻿namespace Cartrack.Fleet.Driver.Domain;

public interface IDriverQdlLicense {
    long LicenseTypeId { get; set; }
    string? LicenseName { get; set; }
    string? LicenseNumber { get; set; }
    DateTime? LicenseValidStart { get; set; }
    DateTime? LicenseValidEnd { get; set; }
    DateTime? LicenseFirstIssueDate { get; set; }
    string? LicenseIssuedCountry { get; set; }
    int? LicensePoints { get; set; }
    string? LicenseDriverRestrictions { get; set; }
}