﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Cartrack.AppHost" Version="2.5.7" />
        <PackageReference Include="Cartrack.EFCore.Models.CT" Version="1.2.1" />
        <PackageReference Include="Cartrack.EFCore.Models.Fleet" Version="1.3.4" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Cartrack.Fleet.Common\Cartrack.Fleet.Common.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Features\GetDriversForVehicle\Scdf\GetDriversForVehicleHandler.cs" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Features\GetDriversForVehicle\Scdf\" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Features\GetDriversForVehicle\Scdf\" />
    </ItemGroup>

</Project>
