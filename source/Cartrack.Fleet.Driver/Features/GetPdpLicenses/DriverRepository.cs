﻿using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Driver.IO.Sql;

public partial class DriverRepository {
    public async Task<List<DriverPdpLicense>> GetPdpLicensesById(string clientDriverId) {
        var pdpLicenses = await fleetDbContext.ClientDriverSpecialLicenses
            .Include(d => d.DriverSpecialLicenseType)
            .Where(c => c.ClientDriverId.Equals(clientDriverId))
            .ToListAsync();

        List<DriverPdpLicense> driverPdpLicenses = [];
        driverPdpLicenses.AddRange(pdpLicenses
            .Select(p => new DriverPdpLicense() {
                LicenseTypeId = p.DriverSpecialLicenseTypeId,
                LicenseName = p.DriverSpecialLicenseType.LicenseName,
                LicenseNumber = p.SpecialLicenseNumber ?? "",
                LicenseIssueDate = p.SpecialLicenseIssueDate,
                LicenseExpiryDate = p.SpecialLicenseExpiryDate
            })
        );

        return driverPdpLicenses;
    }
}