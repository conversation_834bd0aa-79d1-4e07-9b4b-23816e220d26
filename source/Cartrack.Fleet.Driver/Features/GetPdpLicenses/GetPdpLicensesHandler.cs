﻿using Cartrack.AppHost;
using Cartrack.Fleet.Driver.IO;
using Cartrack.Fleet.Driver.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Driver.Features.GetPdpLicenses;

public class GetPdpLicensesHandler(IDriverRepository repo, IHttpContextAccessor context, ILogger<GetPdpLicensesHandler> logger)
    : IRequestHandler<GetPdpLicensesRequest, GetPdpLicensesResponse> {
    public async Task<GetPdpLicensesResponse>
        Handle(GetPdpLicensesRequest request, CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.Id > 0, "Accessory ID must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving the PDP Licenses with Id={Id}", context.HttpContext?.TraceIdentifier ?? "", 
                request.Account, request.Id);

            var pdpLicenses = await repo.GetPdpLicensesById(request.Id);
            var apiDriver = pdpLicenses?.ToHttpPdpLicenses();
            return new GetPdpLicensesResponse(apiDriver);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving PDP Licenses", context.HttpContext?.TraceIdentifier ?? "");
            return new GetPdpLicensesResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving PDP Licenses", context.HttpContext?.TraceIdentifier ?? "");
            return new GetPdpLicensesResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}