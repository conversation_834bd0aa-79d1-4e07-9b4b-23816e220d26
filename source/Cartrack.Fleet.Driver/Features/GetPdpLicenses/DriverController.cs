﻿using Cartrack.Fleet.Driver.Features.GetPdpLicenses;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpGet]
    [Route("pdplicenses/{clientDriverId}")]
    public async Task<ActionResult<GetPdpLicensesResponse>> GetPdpLicenses(string clientDriverId) {
        var request = new GetPdpLicensesRequest(clientDriverId);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetPdpLicensesResponse, DriverPdpLicenses>(this.HttpContext);
    }
}