﻿using Cartrack.AppHost;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Driver.Features.GetVehiclesForDriver.SCDF;

public class GetVehiclesForDriverHandler(IHttpContextAccessor context, ILogger<GetVehiclesForDriverHandler> logger)
    : IRequestHandler<GetVehiclesForDriverRequest, GetVehiclesForDriverResponse> {
    public async Task<GetVehiclesForDriverResponse> Handle(GetVehiclesForDriverRequest request, CancellationToken cancellationToken) {
        try {
            
            throw new NotImplementedException();
            
            
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving all vehicles", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            // var vehicles = await repo.GetVehiclesForDriver(request.UserId, request.Page, request.PageSize);
            // var total = await repo.GetTotal(request.UserId);
            // var apiVehicle = vehicles?.ToHttpVehicles(total);
            return new GetVehiclesForDriverResponse(null);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Vehicles List", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehiclesForDriverResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Vehicles List", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehiclesForDriverResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}