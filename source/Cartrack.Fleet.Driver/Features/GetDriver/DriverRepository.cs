﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common.Domain;
using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Algorithm;
using static Cartrack.Fleet.Driver.Domain.Status;

namespace Cartrack.Fleet.Driver.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class DriverRepository {

    private static ConcurrentCache<long, List<DepartmentRecord>> CachedDepartments = new();
    private static ConcurrentCache<long, List<DriverLicenseTypeRecord>> CachedDriverLicenseTypes = new();
    private static ConcurrentCache<long, List<DriverSpecialLicenseTypeRecord>> CachedDriverSpecialLicenseTypes = new();

    private record DepartmentRecord(long DepartmentId, string DepartmentName, string? Description);

    private record DriverLicenseTypeRecord(long DriverLicenseTypeId, string Name);
        
    private record DriverSpecialLicenseTypeRecord(long DriverSpecialLicenseTypeId, string LicenseName);

    public async Task<DriverBase?> GetDriverById(long userId, string clientDriverId) {
        return await CachedDrivers.GetOrAddAsync(new DriverCacheKey(userId, clientDriverId) , async _ => {
                var driver = await GetDriverByIdInternal(userId, clientDriverId);
                return driver;
            },
            expiry: appSettings.CacheExpiry,
            keepAlive: true);

        async Task<DriverBase?> GetDriverByIdInternal(long userId2, string clientDriverId2) {
            var departments = await CachedDepartments.GetOrAddAsync(userId2, async _ => {
                return await fleetDbContext.Departments
                    .Where(y => y.UserId == userId2 && !y.IsDeleted)
                    .Select(x => new DepartmentRecord( x.DepartmentId, x.DepartmentName, x.Description ))
                    .ToListAsync();
            });

            var qdlLicenses = await CachedDriverLicenseTypes.GetOrAddAsync(userId2, async _ => {
                return await fleetDbContext.DriverLicenseTypes
                    .Where(x => x.UserId == userId2 || x.UserId == null)
                    .Select(x => new DriverLicenseTypeRecord(x.DriverLicenseTypeId, x.Name))
                    .ToListAsync();
            });

            var pdpLicenses = await CachedDriverSpecialLicenseTypes.GetOrAddAsync(userId, async _ => {
                return await fleetDbContext.DriverSpecialLicenseTypes
                    .Where(x => x.UserId == userId2 || x.UserId == null)
                    .Select(x => new DriverSpecialLicenseTypeRecord(x.DriverSpecialLicenseTypeId, x.LicenseName))
                    .ToListAsync();
            }); 
                
               
            var drivers = await fleetDbContext.ClientDrivers
                .Where(x => x.UserId == userId2 && x.ClientDriverId.Equals(clientDriverId2))
                .ToListAsync();

            var clientDrivers = (from driver in drivers
                    join cdd in fleetDbContext.ClientDriverDepartments on driver.ClientDriverId equals cdd.ClientDriverId
                        into cddJoin
                    from cdd in cddJoin.Where(x => x.IsActive == true).DefaultIfEmpty()
                    join qdl in fleetDbContext.ClientDriverLicenses on driver.ClientDriverId equals qdl.ClientDriverId into
                        qdlJoin
                    from qdl in qdlJoin.DefaultIfEmpty()
                    join pdp in fleetDbContext.ClientDriverSpecialLicenses on driver.ClientDriverId equals pdp
                        .ClientDriverId into pdpJoin
                    from pdp in pdpJoin.DefaultIfEmpty()
                    join cds in fleetDbContext.ClientDriverStates on driver.ClientDriverId equals cds.ClientDriverId into
                        cdsJoin
                    from cds in cdsJoin
                        .Where(s => s.ClientDriverStatusId == (int)Active || s.ClientDriverStatusId == (int)Disabled)
                        .DefaultIfEmpty()
                    group new {
                        driver,
                        cdd,
                        pdp,
                        qdl,
                        cds
                    } by driver
                    into g
                    select new DriverRecord(
                        g.Key.ClientDriverId,
                        g.Key.UserId,
                        g.Key.DriverName,
                        g.Key.DriverSurname,
                        g.Key.IdNumber,
                        g.Key.Gender,
                        g.Key.CellNumber,
                        g.Key.Cts,
                        g.Key.Uts,
                        g.Key.EMail,
                        g.Key.EmployeeNumber,
                        g.Key.IsEncrypted,
                        g.First().cds.ClientDriverStatusId,
                        g.Where(x => x.cdd != null).GroupBy(x => x.cdd.DepartmentId).Select(x =>
                                new DriverDepartment { Id = x.Key ?? 0, Name = departments.FirstOrDefault(a => a.DepartmentId == x.Key).DepartmentName, }).ToList(),
                        g.Where(x => x.qdl != null).GroupBy(x => x.qdl.DriverLicenseTypeId).Select(x =>
                                new DriverQdlLicense {
                                    LicenseTypeId = x.Key,
                                    LicenseName =
                                        qdlLicenses.Where(a => a.DriverLicenseTypeId == x.Key).Select(a => a.Name)
                                            .FirstOrDefault(),
                                    LicenseNumber = x.First().qdl.LicenseNumber,
                                    LicenseValidStart = x.First().qdl.LicenseValidStart,
                                    LicenseValidEnd = x.First().qdl.LicenseValidEnd,
                                    LicenseFirstIssueDate = x.First().qdl.LicenseFirstIssueDate,
                                    LicensePoints = x.First().qdl.LicensePoints,
                                    LicenseIssuedCountry = x.First().qdl.LicenseIssuedCountry,
                                    LicenseDriverRestrictions = x.First().qdl.LicenseDriverRestrictions
                                }).ToList(),
                        g.Where(x => x.pdp != null).GroupBy(x => x.pdp.DriverSpecialLicenseTypeId)
                            .Select(x => new DriverPdpLicense {
                                LicenseTypeId = x.Key,
                                LicenseName =
                                    pdpLicenses.Where(a => a.DriverSpecialLicenseTypeId == x.Key).Select(a => a.LicenseName)
                                        .FirstOrDefault(),
                                LicenseNumber = x.First().pdp.SpecialLicenseNumber,
                                LicenseIssueDate = x.First().pdp.SpecialLicenseIssueDate,
                                LicenseExpiryDate = x.First().pdp.SpecialLicenseExpiryDate
                            }).ToList()
                    )
                );

            var clientDriver = clientDrivers.FirstOrDefault();
            if (clientDriver is null) {
                return null;
            }

            return ToDriver(clientDriver, appSettings);
        }
    }
}