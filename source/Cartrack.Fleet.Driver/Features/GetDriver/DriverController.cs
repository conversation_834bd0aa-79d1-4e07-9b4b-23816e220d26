﻿using Cartrack.Fleet.Driver.Features.GetDriver;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpGet]
    [Route("{clientDriverId}")]
    public async Task<ActionResult<GetDriverResponse>> GetDriver(string clientDriverId) {
        var request = new GetDriverRequest(clientDriverId);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
       
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetDriverResponse, Driver>(this.HttpContext);
    }
}