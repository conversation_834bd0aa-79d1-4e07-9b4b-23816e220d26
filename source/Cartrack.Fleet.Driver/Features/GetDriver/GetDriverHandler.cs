﻿using Cartrack.AppHost;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.IO;
using Cartrack.Fleet.Driver.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Driver.Features.GetDriver;

public class GetDriverHandler(IDriverRepository repo, IHttpContextAccessor context, AppSettings appSettings, ILogger<GetDriverHandler> logger)
    : IRequestHandler<GetDriverRequest, GetDriverResponse> {
    public async Task<GetDriverResponse> Handle(GetDriverRequest request, CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.Id > 0, "Accessory ID must be greater than zero");
            Requires.NotNullOrEmpty(request.ClientDriverId, nameof(request.ClientDriverId));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving the driver with Id={ClientDriverId}", context.HttpContext?.TraceIdentifier ?? "", request.Account,
                request.ClientDriverId);

            var driver = await repo.GetDriverById(request.UserId, request.ClientDriverId);
            var apiDriver = driver?.ToHttpDriver();
            return new GetDriverResponse(apiDriver);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Driver", context.HttpContext?.TraceIdentifier ?? "");
            return new GetDriverResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Driver", context.HttpContext?.TraceIdentifier ?? "");
            return new GetDriverResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}