﻿using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Driver.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class DriverRepository {
    public async Task<List<DriverQdlLicense>> GetQdlLicensesById(string clientDriverId) {
        var qdlLicenses = await fleetDbContext.ClientDriverLicenses
            .Include(d => d.DriverLicenseType)
            .Where(c => c.ClientDriverId.Equals(clientDriverId))
            .ToListAsync();

        List<DriverQdlLicense> driverQdlLicenses = [];
        driverQdlLicenses.AddRange(qdlLicenses
            .Select(p => new DriverQdlLicense() {
                LicenseTypeId = p.DriverLicenseTypeId,
                LicenseName = p.DriverLicenseType.Name,
                LicenseNumber = p.LicenseNumber ?? "",
                LicenseValidStart = p.LicenseValidStart,
                LicenseValidEnd = p.LicenseValidEnd,
                LicenseFirstIssueDate = p.LicenseFirstIssueDate,
                LicenseIssuedCountry = p.LicenseIssuedCountry,
                LicensePoints = p.LicensePoints,
                LicenseDriverRestrictions = p.LicenseDriverRestrictions
            })
        );

        return driverQdlLicenses;
    }
}