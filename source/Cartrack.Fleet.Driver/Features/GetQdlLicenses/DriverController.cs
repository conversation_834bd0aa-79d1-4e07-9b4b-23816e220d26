﻿using Cartrack.Fleet.Driver.Features.GetQdlLicenses;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Driver.Features.GetDriver;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpGet]
    [Route("qdllicenses/{clientDriverId}")]
    public async Task<ActionResult<GetQdlLicensesResponse>> GetQdlLicenses(string clientDriverId) {
        var request = new GetQdlLicensesRequest(clientDriverId);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetQdlLicensesResponse, DriverQdlLicenses>(this.HttpContext);
    }
}