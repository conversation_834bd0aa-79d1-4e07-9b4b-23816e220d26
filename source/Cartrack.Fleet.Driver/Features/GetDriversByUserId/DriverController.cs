﻿using Cartrack.Fleet.Driver.Features.GetDriversByUserId;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.Domain.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpGet]
    [Route("active")]
    public async Task<ActionResult<GetDriversByUserIdResponse>> GetDriversByUserId() {
        var request = new GetDriversByUserIdRequest();
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetDriversByUserIdResponse, DriverSlim[]>(this.HttpContext);
    }
}