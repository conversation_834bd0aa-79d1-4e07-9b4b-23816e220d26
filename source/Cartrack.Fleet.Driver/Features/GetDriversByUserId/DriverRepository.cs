﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Driver.IO.Http;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Driver.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class DriverRepository {
    
    public async Task<List<DriverSlim>> GetAllDrivers(long userId) {
        
        var qdlLicenses = await CachedDriverLicenseTypes.GetOrAddAsync(userId, async _ => {
            return await fleetDbContext.DriverLicenseTypes
                .Where(x => x.UserId == userId || x.UserId == null)
                .Select(x => new DriverLicenseTypeRecord(x.DriverLicenseTypeId, x.Name))
                .ToListAsync();
        });

        var pdpLicenses = await CachedDriverSpecialLicenseTypes.GetOrAddAsync(userId, async _ => {
            return await fleetDbContext.DriverSpecialLicenseTypes
                .Where(x => x.UserId == userId || x.UserId == null)
                .Select(x => new DriverSpecialLicenseTypeRecord(x.DriverSpecialLicenseTypeId, x.LicenseName))
                .ToListAsync();
        }); 

       var driversWithSpecialLicenses =
           from c in fleetDbContext.ClientDrivers
           join c2 in fleetDbContext.ClientDriverSpecialLicenses on c.ClientDriverId equals c2.ClientDriverId
           join s in fleetDbContext.DriverSpecialLicenseTypes on c2.DriverSpecialLicenseTypeId equals s.DriverSpecialLicenseTypeId
           where c.UserId == userId && c.ClientDriverStates.Any(s => s.ClientDriverStatusId == ActiveDriver)
           select new {
               c.ClientDriverId,
               c.EMail,
               c.DriverName,
               c.DriverSurname,
               c.IdNumber,
               c.IsEncrypted,
               c.Gender,
               c.CellNumber,
               c2.DriverSpecialLicenseTypeId,
               s.LicenseName
           };
       
       var driversWithNormalLicenses =
           from c in fleetDbContext.ClientDrivers
           join c2 in fleetDbContext.ClientDriverLicenses on c.ClientDriverId equals c2.ClientDriverId
           join s in fleetDbContext.DriverLicenseTypes on c2.DriverLicenseTypeId equals s.DriverLicenseTypeId
           where c.UserId == userId && c.ClientDriverStates.Any(s => s.ClientDriverStatusId == ActiveDriver)
           select new {
               c.ClientDriverId,
               c.EMail,
               c.DriverName,
               c.DriverSurname,
               c.IdNumber,
               c.IsEncrypted,
               c.Gender,
               c.CellNumber,
               c2.DriverLicenseTypeId,
               s.Name
           };

       var dnl = (await driversWithNormalLicenses.ToArrayAsync()).Select(r => new { Driver = new DriverSlim(
           r.ClientDriverId,
           GetIdNumber(r.IdNumber, r.IsEncrypted ?? false),
           r.EMail,
           r.CellNumber,
           [], [] ), r.DriverLicenseTypeId }).ToArray();
       
       var dsl = (await driversWithSpecialLicenses.ToArrayAsync()).Select(r => new { Driver = new DriverSlim(
           r.ClientDriverId,
           GetIdNumber(r.IdNumber, r.IsEncrypted ?? false),
           r.EMail,
           r.CellNumber,
           [], [] ), r.DriverSpecialLicenseTypeId }).ToArray();

       Dictionary<string, DriverSlim> drivers = []; // <ClientDriverId, DriverSlim
       foreach (var d in dnl) {
           if (!drivers.TryGetValue(d.Driver.ClientDriverId, out var slim)) {
               slim = d.Driver;
             drivers[d.Driver.ClientDriverId] = slim;  
           }
           
           slim.NormalLicenses.Add(new DriverQdlLicenseSlim() { 
               LicenseTypeId = d.DriverLicenseTypeId, 
               LicenseName = qdlLicenses.First(l => l.DriverLicenseTypeId == d.DriverLicenseTypeId).Name }  );
       }
       
       foreach (var d in dsl) {
           if (!drivers.TryGetValue(d.Driver.ClientDriverId, out var slim)) {
               slim = d.Driver;
               drivers[d.Driver.ClientDriverId] = slim;  
           }
           
           slim.SpecialLicenses.Add(new DriverPdpLicenseSlim() { 
               LicenseTypeId = d.DriverSpecialLicenseTypeId, 
               LicenseName = pdpLicenses.First(l => l.DriverSpecialLicenseTypeId == d.DriverSpecialLicenseTypeId).LicenseName }  );
       }
       
        return drivers.Values.ToList();
        
        string GetIdNumber(string idNumber, bool isEncrypted) {
            return !string.IsNullOrEmpty(idNumber) && isEncrypted
                ? AesEncryption.Decrypt(idNumber, appSettings.TfmsEncryptionKey,
                    appSettings.TfmsEncryptionIv)
                : idNumber;
        }
    }
}