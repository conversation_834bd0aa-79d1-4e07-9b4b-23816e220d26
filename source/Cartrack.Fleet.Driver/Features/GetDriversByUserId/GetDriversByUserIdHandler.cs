﻿using Cartrack.AppHost;
using Cartrack.Fleet.Driver.Features.GetDriversByUserId;
using Cartrack.Fleet.Driver.IO;
using Cartrack.Fleet.Driver.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Timeouts;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Driver.Features.GetDriversByUserId;

public class GetDriversByUserIdHandler(IDriverRepository repo, IHttpContextAccessor context, ILogger<GetDriversByUserIdHandler> logger)
    : IRequestHandler<GetDriversByUserIdRequest, GetDriversByUserIdResponse> {
    public async Task<GetDriversByUserIdResponse> Handle(GetDriversByUserIdRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving all drivers", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var drivers = await repo.GetAllDrivers(request.UserId);
            return new GetDriversByUserIdResponse(drivers.ToArray());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Drivers List", context.HttpContext?.TraceIdentifier ?? "");
            return new GetDriversByUserIdResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Drivers List", context.HttpContext?.TraceIdentifier ?? "");
            return new GetDriversByUserIdResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}