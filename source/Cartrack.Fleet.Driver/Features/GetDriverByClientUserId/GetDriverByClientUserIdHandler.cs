﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Driver.Features.GetDriverByClientUserId;
using Cartrack.Fleet.Driver.IO;
using Cartrack.Fleet.Driver.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http.Timeouts;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Driver.Features.GetDriverByClientUserId;

public class GetDriverByClientUserIdHandler(IDriverRepository repo, ILogger<GetDriverByClientUserIdHandler> logger)
    : IRequestHandler<GetDriverByClientUserIdRequest, GetDriverByClientUserIdResponse> {
    public async Task<GetDriverByClientUserIdResponse> Handle(GetDriverByClientUserIdRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Retrieving driver by client user id {userId}", request.Account, request.ClientUserId);

            var driver = await repo.GetDriverByClientUserId(request.ClientUserId);
            return new GetDriverByClientUserIdResponse(driver.ToHttpDriver());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving Drivers List");
            return new GetDriverByClientUserIdResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving Drivers List");
            return new GetDriverByClientUserIdResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}