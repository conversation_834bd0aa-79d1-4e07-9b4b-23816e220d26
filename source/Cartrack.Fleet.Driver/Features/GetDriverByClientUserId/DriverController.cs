﻿using Cartrack.Fleet.Driver.Features.GetDriverByClientUserId;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpGet]
    [Route("clientUserId/{clientUserId}")]
    public async Task<ActionResult<GetDriverByClientUserIdResponse>> GetDriverByClientUserId(string clientUserId) {
        var request = new GetDriverByClientUserIdRequest(clientUserId);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetDriverByClientUserIdResponse, Driver>(this.HttpContext);
    }
}