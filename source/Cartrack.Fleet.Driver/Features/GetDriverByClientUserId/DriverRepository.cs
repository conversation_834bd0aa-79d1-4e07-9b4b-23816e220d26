﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Driver.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class DriverRepository {
   
    public async  Task<DriverBase?> GetDriverByClientUserId(string clientUserId) {
        var d = await fleetDbContext.ClientDrivers.FirstOrDefaultAsync(t => t.ClientUserId == clientUserId);
        return await this.GetDriverById(d.<PERSON>d, d.ClientDriverId);
    }
}