﻿using Cartrack.AppHost;
using Cartrack.Fleet.Driver.Features.GetDrivers;
using Cartrack.Fleet.Driver.IO;
using Cartrack.Fleet.Driver.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Timeouts;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Driver.Features.GetDrivers;

public class GetDriversHandler(IDriverRepository repo, IHttpContextAccessor context, ILogger<GetDriversHandler> logger)
    : IRequestHandler<GetDriversRequest, GetDriversResponse> {
    public async Task<GetDriversResponse> Handle(GetDriversRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving all drivers", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var drivers = await repo.GetDrivers(request.UserId, request.Page, request.PageSize);
            var total = await repo.GetTotal(request.UserId);
            var apiDriver = drivers?.ToHttpDrivers(total);
            return new GetDriversResponse(apiDriver);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Drivers List", context.HttpContext?.TraceIdentifier ?? "");
            return new GetDriversResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Drivers List", context.HttpContext?.TraceIdentifier ?? "");
            return new GetDriversResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}