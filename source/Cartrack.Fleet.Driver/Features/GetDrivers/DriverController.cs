﻿using Cartrack.Fleet.Driver.Features.GetDrivers;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Driver.Features.GetDriver;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpGet]
    public async Task<ActionResult<GetDriversResponse>> GetDrivers([FromQuery] int page = 1,
        [FromQuery] int pageSize = 10) {
        var request = new GetDriversRequest(page, pageSize);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetDriversResponse, Drivers>(this.HttpContext);
    }
}