﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Driver.IO.Sql;

public partial class DriverRepository {
    private const string AppSettingsIsDriverEncrypted = "driver_fields_encrypt";

    public async Task UpdateDriver(long userId, string clientDriverId, DriverBase driver) {
        // Get Necessary AppSettings for Driver
        var dbAppSettings = new[] {
            AppSettingsIsDriverEncrypted
        };
        var dbAppSettingForDriver = await fleetDbContext.AppSettings
            .Where(x => dbAppSettings.Contains(x.SettingsName) && x.Enabled == true)
            .ToDictionaryAsync(x => x.SettingsName, x => x.SettingsValue);

        // Check if the driver already exists
        var existingDriver = await fleetDbContext.ClientDrivers
            .FirstOrDefaultAsync(d => d.UserId == userId && d.ClientDriverId == clientDriverId);

        if (existingDriver != null) {
            if (!string.IsNullOrWhiteSpace(driver.FirstName)) { existingDriver.DriverName = driver.FirstName; }

            if (!string.IsNullOrWhiteSpace(driver.Surname)) { existingDriver.DriverSurname = driver.Surname; }

            if (!string.IsNullOrWhiteSpace(driver.IdNumber)) {
                existingDriver.IdNumber = driver.IdNumber ?? "";
                if (dbAppSettingForDriver[AppSettingsIsDriverEncrypted] == "true") {
                    existingDriver.IdNumber = AesEncryption.Encrypt(existingDriver.IdNumber,
                        appSettings.TfmsEncryptionKey, appSettings.TfmsEncryptionIv);
                }
            }

            if (!string.IsNullOrWhiteSpace(driver.PhoneNumber)) { existingDriver.CellNumber = driver.PhoneNumber; }

            if (!string.IsNullOrWhiteSpace(driver.Email)) { existingDriver.EMail = driver.Email; }

            if (!string.IsNullOrWhiteSpace(driver.EmployeeNumber)) {
                existingDriver.EmployeeNumber = driver.EmployeeNumber;
                if (dbAppSettingForDriver[AppSettingsIsDriverEncrypted] == "true") {
                    existingDriver.EmployeeNumber = AesEncryption.Encrypt(existingDriver.EmployeeNumber,
                        appSettings.TfmsEncryptionKey, appSettings.TfmsEncryptionIv);
                }
            }

            existingDriver.Uts = DateTime.UtcNow;
            existingDriver.Gender = driver.Gender switch {
                Gender.Female => 2,
                Gender.Male => 1,
                _ => 0
            };
        }
        else {
            throw new Exception("Driver is not found. Please create a driver first.");
        }

        // Check Client Driver State Table
        if (driver.Enabled != null && driver.Enabled.Value != 0) {
            var existingDriverState = await fleetDbContext.ClientDriverStates
                .FirstOrDefaultAsync(d =>
                    (d.ClientDriverStatusId == (int)Status.Active || d.ClientDriverStatusId == (int)Status.Disabled)
                    && d.ClientDriverId == clientDriverId);
            if (existingDriverState != null) {
                fleetDbContext.ClientDriverStates.Remove(existingDriverState);
                await fleetDbContext.ClientDriverStates.AddAsync(new ClientDriverState {
                    ClientDriverId = clientDriverId,
                    ClientDriverStatusId = driver.Enabled switch {
                        Status.Active => 10,
                        Status.Disabled => 11,
                        _ => 11
                    },
                    ClientActionId = existingDriverState.ClientActionId ?? ""
                });
            }
        }

        //////////////////////////////////////////////
        // To update Client Driver Departments into DB
        ////
        if (driver.Departments != null) {
            await fleetDbContext.ClientDriverDepartments
                .Where(e => e.ClientDriverId.Equals(clientDriverId))
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(e => e.IsActive, e => false));
            foreach (var d in driver.Departments) {
                var existingDepartment = await fleetDbContext.ClientDriverDepartments
                    .FirstOrDefaultAsync(x => x.ClientDriverId.Equals(clientDriverId) && x.DepartmentId == d.Id);

                if (existingDepartment != null) {
                    existingDepartment.IsActive = true;
                }
                else {
                    fleetDbContext.ClientDriverDepartments.Add(new ClientDriverDepartment {
                        ClientDriverId = clientDriverId,
                        DepartmentId = d.Id,
                        ServiceType = "",
                        IsActive = true
                    });
                }
            }
        }

        if (driver.DriverLicenses != null) {
            var driverDriverLicenses = await fleetDbContext.ClientDriverLicenses
                .Where(e => e.ClientDriverId.Equals(clientDriverId))
                .ToListAsync();
            fleetDbContext.ClientDriverLicenses.RemoveRange(driverDriverLicenses);

            foreach (var dl in driver.DriverLicenses) {
                await fleetDbContext.ClientDriverLicenses.AddAsync(new ClientDriverLicense {
                    ClientDriverId = clientDriverId,
                    DriverLicenseTypeId = dl.LicenseTypeId,
                    LicenseNumber = dl.LicenseNumber,
                    LicenseValidStart =
                        dl.LicenseValidStart.HasValue
                            ? DateTime.SpecifyKind(dl.LicenseValidStart.Value, DateTimeKind.Utc)
                            : null,
                    LicenseValidEnd =
                        dl.LicenseValidEnd.HasValue
                            ? DateTime.SpecifyKind(dl.LicenseValidEnd.Value, DateTimeKind.Utc)
                            : null,
                    LicenseFirstIssueDate =
                        dl.LicenseFirstIssueDate.HasValue
                            ? DateTime.SpecifyKind(dl.LicenseFirstIssueDate.Value, DateTimeKind.Utc)
                            : null,
                    LicenseIssuedCountry = dl.LicenseIssuedCountry,
                    LicensePoints = dl.LicensePoints,
                    LicenseDriverRestrictions = dl.LicenseDriverRestrictions
                });
            }
        }

        if (driver.SpecialDriverLicenses != null) {
            var driverSpecialDriverLicenses = await fleetDbContext.ClientDriverSpecialLicenses
                .Where(e => e.ClientDriverId.Equals(clientDriverId))
                .ToListAsync();
            fleetDbContext.ClientDriverSpecialLicenses.RemoveRange(driverSpecialDriverLicenses);

            foreach (var dl in driver.SpecialDriverLicenses) {
                await fleetDbContext.ClientDriverSpecialLicenses.AddAsync(new ClientDriverSpecialLicense {
                    ClientDriverId = clientDriverId,
                    DriverSpecialLicenseTypeId = dl.LicenseTypeId,
                    SpecialLicenseNumber = dl.LicenseNumber,
                    SpecialLicenseIssueDate =
                        dl.LicenseIssueDate.HasValue
                            ? DateTime.SpecifyKind(dl.LicenseIssueDate.Value, DateTimeKind.Utc)
                            : null,
                    SpecialLicenseExpiryDate = dl.LicenseExpiryDate.HasValue
                        ? DateTime.SpecifyKind(dl.LicenseExpiryDate.Value, DateTimeKind.Utc)
                        : null,
                });
            }
        }

        await fleetDbContext.SaveChangesAsync();
    }
}