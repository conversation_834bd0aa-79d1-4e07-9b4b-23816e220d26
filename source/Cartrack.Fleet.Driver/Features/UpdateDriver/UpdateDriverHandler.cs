﻿using Cartrack.AppHost;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Driver.Domain.Scdf;
using Cartrack.Fleet.Driver.IO;
using Cartrack.Fleet.Driver.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Driver.Features.UpdateDriver;

public class UpdateDriverHandler(IDriverRepository repo, IHttpContextAccessor context, AppSettings appSettings, ILogger<UpdateDriverHandler> logger)
    : IRequestHandler<UpdateDriverRequest, UpdateDriverResponse> {
    public async Task<UpdateDriverResponse> Handle(UpdateDriverRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            //Requires.NotNullOrEmpty(request.DriverName, nameof(request.DriverName));
            logger.LogInformation("[{TraceId}] [{Agency}] Updating a driverId : {ClientDriverId}", context.HttpContext?.TraceIdentifier ?? "", request.Account,
                request.ClientDriverId);

            DriverBase driver = await CreateFromRequest(request);
            await repo.UpdateDriver(request.UserId, request.ClientDriverId, driver);
            var updatedDriver = await repo.GetDriverById(request.UserId, request.ClientDriverId);
            return new UpdateDriverResponse(updatedDriver?.ToHttpDriver());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error updating driver. ", context.HttpContext?.TraceIdentifier ?? "");
            return new UpdateDriverResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error updating driver.", context.HttpContext?.TraceIdentifier ?? "");
            return new UpdateDriverResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private async Task<DriverBase> CreateFromRequest(UpdateDriverRequest request) {
        // Department Setup
        List<DriverDepartment> driverDepartments = [];
        if (request.DepartmentIds != null) {
            driverDepartments.AddRange(request.DepartmentIds.Select(dl => new DriverDepartment {
                ClientDriverId = request.ClientDriverId,
                Id = dl,
                IsActive = true
            }));
        }

        // Normal Driver Licenses Setup
        List<DriverQdlLicense> listQdlLicenses = [];
        if (request.DriverLicenses != null) {
            listQdlLicenses.AddRange(request.DriverLicenses.Select(dl => new DriverQdlLicense {
                LicenseTypeId = dl.LicenseTypeId,
                LicenseName = "",
                LicenseNumber = dl.LicenseNumber,
                LicenseValidStart = dl.LicenseValidStart,
                LicenseValidEnd = dl.LicenseValidEnd,
                LicenseFirstIssueDate = dl.LicenseFirstIssueDate,
                LicenseIssuedCountry = dl.LicenseIssuedCountry,
                LicensePoints = dl.LicensePoints,
                LicenseDriverRestrictions = dl.LicenseDriverRestrictions
            }));
        }

        // Special Driver Licenses Setup
        List<DriverPdpLicense> listPdpLicenses = [];
        if (request.SpecialDriverLicenses != null) {
            listPdpLicenses.AddRange(request.SpecialDriverLicenses.Select(dl => new DriverPdpLicense {
                LicenseTypeId = dl.LicenseTypeId,
                LicenseName = "",
                LicenseNumber = dl.LicenseNumber,
                LicenseIssueDate = dl.LicenseIssueDate,
                LicenseExpiryDate = dl.LicenseExpiryDate
            }));
        }

        //2. Create Base Driver
        var driver = new DriverBase {
            UserId = request.UserId,
            ClientDriverId = request.ClientDriverId,
            UpdatedTs = DateTime.UtcNow,
        };

        if (request.DriverName != null) { driver.FirstName = request.DriverName ?? ""; }

        if (request.DriverSurName != null) { driver.Surname = request.DriverSurName ?? ""; }

        if (request.IdNumber != null) { driver.IdNumber = request.IdNumber ?? ""; }

        if (request.EMail != null) { driver.Email = request.EMail ?? ""; }

        if (request.PhoneNumber != null) { driver.PhoneNumber = request.PhoneNumber ?? ""; }

        if (request.Gender != null) {
            driver.Gender = request.Gender switch {
                1 => Gender.Male,
                2 => Gender.Female,
                _ => Gender.Unknown
            };
        }

        if (request.Enabled != null) {
            driver.Enabled = request.Enabled switch {
                1 => Status.Active,
                0 => Status.Disabled,
                _ => Status.Unknown
            };
        }

        if (request.DepartmentIds != null) { driver.Departments = driverDepartments; }

        if (request.DriverLicenses != null) { driver.DriverLicenses = listQdlLicenses; }

        if (request.SpecialDriverLicenses != null) { driver.SpecialDriverLicenses = listPdpLicenses; }

        if (request.EmployeeNumber != null) { driver.EmployeeNumber = request.EmployeeNumber; }

        driver.DepartmentsMasterList = await repo.GetDepartments(request.UserId);
        driver.QdlMasterList = await repo.GetQdlLicenses(request.UserId);
        driver.PdpMasterList = await repo.GetPdpLicenses(request.UserId);

        //2. Validate the driver
        await driver.Validate();

        return driver;
    }
}