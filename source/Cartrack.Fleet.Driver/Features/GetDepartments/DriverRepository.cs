﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Driver.Domain.Common;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Driver.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class DriverRepository {
    public async Task<List<Department>> GetDepartments(long userId) {
        var departments = await fleetDbContext.Departments
            .Where(x => x.UserId == userId && !x.IsDeleted)
            .ToListAsync();

        List<Department> masterDepartmentsList = [];
        masterDepartmentsList.AddRange(departments
            .Select(c => new Department() {
                DepartmentId = c.DepartmentId,
                DepartmentName = c.DepartmentName,
                IsDeleted = c.<PERSON>
            })
        );

        return masterDepartmentsList;
    }

    public async Task<List<DriverDepartment>> GetDepartmentsByDriverId(long userId, string? clientDriverId) {
        var driverDepartments = await (
            from cdd in fleetDbContext.ClientDriverDepartments
            join dept in fleetDbContext.Departments on cdd.DepartmentId equals dept.DepartmentId into deptJoined
            from dept in deptJoined.DefaultIfEmpty() // This ensures a LEFT JOIN
            join d in fleetDbContext.ClientDrivers on cdd.ClientDriverId equals d.ClientDriverId into dj
            from d in dj.DefaultIfEmpty()
            where cdd.ClientDriverId.Equals(clientDriverId) && dept.UserId == userId && cdd.IsActive == true
            select new {
                ClientDriverDepartment = cdd,
                ClientDriver = d,
                Department = dept
            }
        ).ToListAsync();

        List<DriverDepartment> driverDepartmentsList = [];
        driverDepartmentsList.AddRange(driverDepartments
            .Select(c => new DriverDepartment() {
                Id = c.Department.DepartmentId,
                Name = c.Department.DepartmentName,
                ServiceType = c.ClientDriverDepartment.ServiceType
            })
        );

        return driverDepartmentsList;
    }
}