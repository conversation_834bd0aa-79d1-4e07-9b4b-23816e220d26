﻿using Cartrack.AppHost;
using Cartrack.Fleet.Driver.IO;
using Cartrack.Fleet.Driver.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Driver.Features.GetDepartments;

public class GetDepartmentsHandler(IDriverRepository repo, IHttpContextAccessor context, ILogger<GetDepartmentsHandler> logger)
    : IRequestHandler<GetDepartmentsRequest, GetDepartmentsResponse> {
    public async Task<GetDepartmentsResponse>
        Handle(GetDepartmentsRequest request, CancellationToken cancellationToken) {
        try {
            //Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving all driver department belong to {ClientDriverId}", context.HttpContext?.TraceIdentifier ?? "",
                request.Account, request.ClientDriverId);

            var driverDepartments = await repo.GetDepartmentsByDriverId(request.UserId, request.ClientDriverId);
            var apiDriver = driverDepartments?.ToHttpDriverDepartments();
            return new GetDepartmentsResponse(apiDriver);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Client Driver Departments", context.HttpContext?.TraceIdentifier ?? "");
            return new GetDepartmentsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Client Driver Department", context.HttpContext?.TraceIdentifier ?? "");
            return new GetDepartmentsResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}