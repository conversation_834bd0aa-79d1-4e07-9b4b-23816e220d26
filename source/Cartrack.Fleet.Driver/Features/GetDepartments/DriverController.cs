﻿using Cartrack.Fleet.Driver.Features.GetDepartments;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Driver.Features.GetDriver;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpGet]
    [Route("departments/{clientDriverId}")]
    public async Task<ActionResult<GetDepartmentsResponse>> GetDepartments(string clientDriverId) {
        var request = new GetDepartmentsRequest(clientDriverId);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetDepartmentsResponse, DriverDepartments>(this.HttpContext);
    }
}