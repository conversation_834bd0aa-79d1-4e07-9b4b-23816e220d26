﻿using Cartrack.Fleet.Driver.IO.Http;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.Common;
using Microsoft.Extensions.DependencyInjection;

namespace Cartrack.Fleet.Driver;

public static class DriverStartup {
    public static void Register(IServiceCollection builderServices, AppSettings appSetting) {
        //builderServices.AddScoped<IAuthRepository, AuthRepository>();
        builderServices.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<DriverController>());
        builderServices.AddControllers().AddApplicationPart(typeof(DriverController).Assembly);
        builderServices.AddScoped<IDriverRepository, DriverRepository>();
    }
}