﻿using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Driver.IO.Http;

[ApiController]
[Route("driver")]
public partial class DriverController : ControllerBase {
    private readonly AppSettings _appSettings;
    private readonly ILogger<DriverController> _logger;
    private readonly IMediator _mediator;

    public DriverController(ILogger<DriverController> logger, IMediator mediator, AppSettings appSettings) {
        this._logger = logger;
        this._mediator = mediator;
        this._appSettings = appSettings;
    }
}