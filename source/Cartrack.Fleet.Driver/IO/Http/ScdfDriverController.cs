﻿using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Driver.IO.Http;

[ApiController]
[Route("scdf/driver")]
public partial class ScdfDriverController : ControllerBase {
    private readonly AppSettings _appSettings;
    private readonly ILogger<ScdfDriverController> _logger;
    private readonly IMediator _mediator;

    public ScdfDriverController(ILogger<ScdfDriverController> logger, IMediator mediator, AppSettings appSettings) {
        this._logger = logger;
        this._mediator = mediator;
        this._appSettings = appSettings;
    }
}