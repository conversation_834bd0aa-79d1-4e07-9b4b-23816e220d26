﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Driver.Domain;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Driver.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class DriverRepository(AppFleetDbContext fleetDbContext, AppSettings appSettings) : IDriverRepository {
    private record DriverCacheKey(long UserId, string ClientDriverId);
    
    private static ConcurrentCache<DriverCacheKey, DriverBase?> CachedDrivers = new();

    private record DriverRecord(
        string ClientDriverId,
        long UserId,
        string DriverName,
        string DriverSurname,
        string IdNumber,
        short Gender,
        string CellNumber,
        DateTime? Cts,
        DateTime? Uts,
        string EMail,
        string EmployeeNumber,
        bool? IsEncrypted,
        int Enabled,
        List<DriverDepartment> Departments,
        List<DriverQdlLicense> DriverLicenses,
        List<DriverPdpLicense> SpecialDriverLicenses);
    
    public async Task<List<DriverPdpLicense>> GetPdpLicenses(long userId) {
        var pdpLicenses = await fleetDbContext.DriverSpecialLicenseTypes
            .Where(c => c.UserId == userId || c.UserId == null)
            .ToListAsync();

        List<DriverPdpLicense> driverPdpLicenses = [];
        driverPdpLicenses.AddRange(pdpLicenses
            .Select(p => new DriverPdpLicense() {
                LicenseTypeId = p.DriverSpecialLicenseTypeId, LicenseName = p.LicenseName
            })
        );

        return driverPdpLicenses;
    }

    public async Task<List<DriverQdlLicense>> GetQdlLicenses(long userId) {
        var qdlLicenses = await fleetDbContext.DriverLicenseTypes
            .Where(c => c.UserId == userId || c.UserId == null)
            .ToListAsync();

        List<DriverQdlLicense> driverQdlLicenses = [];
        driverQdlLicenses.AddRange(qdlLicenses
            .Select(p => new DriverQdlLicense() {
                LicenseTypeId = p.DriverLicenseTypeId, LicenseName = p.Name
            })
        );

        return driverQdlLicenses;
    }
}