﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Driver.IO.Http;
using MainCommon = Cartrack.Fleet.Common;

namespace Cartrack.Fleet.Driver.IO.Sql;

public interface IDriverRepository {
    Task<DriverBase?> GetDriverById(long userId, string clientUserId);
    Task<List<DriverBase>> GetDrivers(long userId, int page, int pageSize);
    Task<List<DriverSlim>> GetAllDrivers(long userId);
    Task<int> GetTotal(long userId);
    Task<List<Department>> GetDepartments(long userId);
    Task<List<DriverDepartment>> GetDepartmentsByDriverId(long userId, string clientDriverId);
    Task<List<DriverPdpLicense>> GetPdpLicensesById(string clientDriverId);
    Task<List<DriverQdlLicense>> GetQdlLicensesById(string clientDriverId);
    Task<List<DriverPdpLicense>> GetPdpLicenses(long userId);
    Task<List<DriverQdlLicense>> GetQdlLicenses(long userId);
    Task UpdateDriver(long userId, string clientUserId, DriverBase driver);
    Task<DriverBase?> GetDriverByClientUserId(string clientUserId);
}