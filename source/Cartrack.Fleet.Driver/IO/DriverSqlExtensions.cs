﻿using Cartrack.Fleet.Driver.Domain.Common;
using Db = Cartrack.EFCore.Models.Fleet;

namespace Cartrack.Fleet.Driver.IO;

public static class DriverSqlExtensions {
    public static DriverBase ToBooking(this Db.ClientDriver dbBooking) {
        throw new NotImplementedException("TODO");
    }


    public static Db.ClientDriver FromBooking(this DriverBase booking) {
        throw new NotImplementedException("TODO");
    }
}