﻿using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Api = Cartrack.Fleet.Driver.IO.Http;

namespace Cartrack.Fleet.Driver.IO;

public static class DriverApiExtensions {
    public static Api.Driver? ToHttpDriver(this DriverBase? entity) {
        if (entity == null) return null;

        return new Api.Driver {
            ClientDriverId = entity.ClientDriverId,
            FirstName = entity.FirstName,
            Surname = entity.Surname,
            IdNumber = entity.IdNumber,
            Gender = entity.Gender,
            PhoneNumber = entity.PhoneNumber,
            CreatedTs = entity.CreatedTs,
            UpdatedTs = entity.UpdatedTs,
            Email = entity.Email,
            Enabled = entity.Enabled switch {
                Status.Active => "Active",
                Status.Disabled => "Disabled",
                _ => "Unknown"
            },
            EmployeeNumber = entity.EmployeeNumber,
            Departments = entity.Departments ?? [],
            DriverLicenses = entity.DriverLicenses ?? [],
            SpecialDriverLicenses = entity.SpecialDriverLicenses ?? []
        };
    }

    public static Api.Drivers ToHttpDrivers(this List<DriverBase>? entities, int totalCount) {
        if (entities == null)
            return new Api.Drivers {
                Data = [], TotalCount = totalCount
            };

        var drivers = entities
            .Select(e => e.ToHttpDriver())
            .Where(d => d != null)
            .ToList();

        return new Api.Drivers {
            Data = drivers, TotalCount = totalCount
        };
    }

    public static Api.DriverDepartments? ToHttpDriverDepartments(this List<DriverDepartment>? entities) {
        if (entities == null)
            return null;
        return new Api.DriverDepartments { Data = entities };
    }

    public static Api.DriverPdpLicenses? ToHttpPdpLicenses(this List<DriverPdpLicense>? entities) {
        if (entities == null) return null;

        return new Api.DriverPdpLicenses {
            Data = entities ?? null,
        };
    }

    public static Api.DriverQdlLicenses? ToHttpQdlLicenses(this List<DriverQdlLicense>? entities) {
        if (entities == null) return null;

        return new Api.DriverQdlLicenses {
            Data = entities ?? null,
        };
    }
}