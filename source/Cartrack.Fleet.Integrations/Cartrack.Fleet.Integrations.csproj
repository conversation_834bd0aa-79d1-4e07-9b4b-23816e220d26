﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Cartrack.Fleet.Accessory\Cartrack.Fleet.Accessory.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Audit\Cartrack.Fleet.Audit.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Auth\Cartrack.Fleet.Auth.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Booking\Cartrack.Fleet.Booking.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Common\Cartrack.Fleet.Common.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Driver\Cartrack.Fleet.Driver.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.User\Cartrack.Fleet.User.csproj" />
      <ProjectReference Include="..\Cartrack.Fleet.Vehicle\Cartrack.Fleet.Vehicle.csproj" />
    </ItemGroup>

</Project>
