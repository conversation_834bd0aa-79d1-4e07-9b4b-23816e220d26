﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Domain.Permissions;
using Cartrack.Fleet.User.Features.GetPermissions;
using MediatR;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;

namespace Cartrack.Fleet.Vehicle.IO.Http.Filters;

public class PermissionsContextAttribute : ActionFilterAttribute
{
    public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next) {
        var httpContext = context.HttpContext;
        var mediator = context.HttpContext.RequestServices.GetService<IMediator>();
        
        var request = new GetPermissionsRequest();
        var authClaims = AuthClaims.From(httpContext.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = authClaims.ClientUserId;
        var permissionResponse = await mediator!.Send(request);
       
        var permContext = new PermissionsContext(authClaims, permissionResponse.IsOk ? permissionResponse.Value! : new SetOfPermissions());
        httpContext.Items[Constants.PermissionsContext] = permContext;
        
        await base.OnActionExecutionAsync(context, next);
        
    }
}