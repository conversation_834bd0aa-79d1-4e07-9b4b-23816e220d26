﻿using Cartrack.AppHost;
using Cartrack.Fleet.Common.IO.FileSystem;
using Minio;
using System.Security.Authentication;

namespace Cartrack.Fleet.Common.IO.Http;

public static class MinioClientBuilder {
    public static IMinioClient Create(string rootPath, MinIoSettings minIo) {
        var client = new HttpClient() { Timeout = minIo.PostTimeout };
        if (minIo.ValidateSslCertificate) {
            var handler = new HttpClientHandler {
                SslProtocols = SslProtocols.Tls12,
                ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => true
            };
                
            handler.ClientCertificates.Add(FileIo.LoadCertificate(minIo.X509Certificate));
            client = new HttpClient(handler) { Timeout = minIo.PostTimeout };
        }
            
        var uri = new Uri(rootPath);
        return new MinioClient()
            .WithHttpClient(client)
            .WithEndpoint(uri.Authority)
            .WithCredentials(minIo.SecurityKey, minIo.SecretKey)
            .WithSSL(minIo.UseSsl)
            .Build();
    }
}