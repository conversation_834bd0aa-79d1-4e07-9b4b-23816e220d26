﻿using Cartrack.Fleet.Common.Domain;
using Cartrack.EFCore.Models.Pool;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace Cartrack.Fleet.Common.IO.Sql;

public class AppPoolDbContext : PoolDbContext {
    private const string TfmsApplicationText = "TfmsServiceApi";
    public AppPoolDbContext(string conString, IHttpContextAccessor httpContext, IServiceProvider sp) : base(conString) {
        var token = httpContext.HttpContext!.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
        if (!string.IsNullOrEmpty(token)) {
            var jwtService = sp.GetRequiredService<Cartrack.Fleet.Common.Domain.IJwtService>();
            var principal = jwtService.GetPrincipalFromToken(token) ?? null;
            if (principal != null) {
                this.SetCustomNameForAuditTable(principal);
            }
        }
    }
    public AppPoolDbContext(AppSettings settings, IHttpContextAccessor httpContext, IServiceProvider sp) : this( settings.ConnectionString, httpContext, sp) {
    }

    public AppPoolDbContext(string conString) : base(conString) {
        
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder) {
        base.OnConfiguring(optionsBuilder);
        optionsBuilder.LogTo(Console.WriteLine);
    }
    
    private void SetCustomNameForAuditTable(ClaimsPrincipal principal)
    {
        var connection = Database.GetDbConnection();

        if (connection.State != System.Data.ConnectionState.Open)
            connection.Open();

        var userName = principal.FindFirst(ClaimTypes.Name)!.Value;
        
        using var command = connection.CreateCommand();
        command.CommandText = $"SET custom.user_name TO '{userName}';SET custom.application TO '{TfmsApplicationText}';";
        command.ExecuteNonQuery();
    }
}
