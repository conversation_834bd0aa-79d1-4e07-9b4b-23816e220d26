﻿using Cartrack.EFCore.Models.Fleet;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System.Security.Claims;

namespace Cartrack.Fleet.Common.IO.Sql;

public class AppFleetDbContext: FleetDbContext{
    private const string TfmsApplicationText = "TfmsServiceApi";
    
    public AppFleetDbContext(string conString) : base(conString){}
    public AppFleetDbContext(string conString, IHttpContextAccessor httpContext, IServiceProvider sp) : base(conString) {
        var token = httpContext.HttpContext!.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
        if (!string.IsNullOrEmpty(token)) {
            var jwtService = sp.GetRequiredService<Cartrack.Fleet.Common.Domain.IJwtService>();
            var principal = jwtService.GetPrincipalFromToken(token) ?? null;
            if (principal != null) {
                this.SetCustomNameForAuditTable(principal);
            }
        }
    }
    public AppFleetDbContext(AppSettings settings, IHttpContextAccessor httpContext, IServiceProvider sp) : this(settings.ConnectionString, httpContext, sp) {
        
    }
    
    private void SetCustomNameForAuditTable(ClaimsPrincipal principal)
    {
        var connection = Database.GetDbConnection();

        if (connection.State != System.Data.ConnectionState.Open)
            connection.Open();

        var userName = principal.FindFirst(ClaimTypes.Name)!.Value;
        
        using var command = connection.CreateCommand();
        command.CommandText = $"SET custom.user_name TO '{userName}';SET custom.application TO '{TfmsApplicationText}';";
        command.ExecuteNonQuery();
    }
}