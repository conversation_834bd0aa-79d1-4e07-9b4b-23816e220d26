﻿using System.Security.Cryptography.X509Certificates;

namespace Cartrack.Fleet.Common.IO.FileSystem;

public class FileIo : IFileIo {
    public string ReadAllText(string path) {
        return File.ReadAllText(path);
    }
    public static X509Certificate LoadCertificate(string certificateFullPath) {
        var certFile = Path.GetFullPath(certificateFullPath);
        if (File.Exists(certFile)) return new X509Certificate2(certFile);

        throw new FileNotFoundException("Unable to find certificate", certFile);
    }
}