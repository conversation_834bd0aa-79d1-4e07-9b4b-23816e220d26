﻿namespace Cartrack.Fleet.Common;

public class ClientUserChange {
    public ChangeType Type { get; init; }
    public long? UserId { get; set; }
    public string? ClientUserId { get; set; } = "";
    public string UserName { get; set; } = "";
    public Dictionary<string, string> CurrentValue { get; init; } = new Dictionary<string, string>();
    public Dictionary<string, string> PreviousValue { get; init; } = new Dictionary<string, string>();
    public string ChangedBy { get; init; } = "";
    public DateTime? CreatedTime { get; init; } = null;
}