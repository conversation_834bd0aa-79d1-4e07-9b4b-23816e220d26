﻿using Grpc.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Cartrack.Fleet.Common;
public record AuthClaims(string Account, long UserId, string UserName, string ClientUserId, bool IsMainAccount) {

    public static AuthClaims From(ClaimsPrincipal claims) {
        var account = claims.Claims.First(c => c.Type.ToLowerInvariant() == "account").Value;
        var accountId = claims.Claims.First(c => c.Type.ToLowerInvariant() == "accountid").Value;
        var userName = claims.Claims.First(c => c.Type == ClaimTypes.Name).Value;
        var clientUserId = claims.Claims.First(c => c.Type == ClaimTypes.NameIdentifier).Value.Trim();
        
        var isMainAccount = account == userName;
        return new AuthClaims(account, long.Parse(accountId), userName, clientUserId, isMainAccount);
    }
}
