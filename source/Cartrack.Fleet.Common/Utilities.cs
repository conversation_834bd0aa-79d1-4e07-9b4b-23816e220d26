﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;

namespace Cartrack.Fleet.Common;

public static class Utilities {
    public static object[][] CompareObjectAndFindDifference(object obj1, object obj2, HashSet<string> keysToIgnore) {
        var differences = new Dictionary<string, List<string>>();

        var newValue = new List<string>();

        if (obj1 != null && obj2 != null) {
            var properties1 = obj1.GetType().GetProperties();
            var properties3 = obj2.GetType().GetProperties();

            object[][] obj = new object[properties3.Count()][];

            int index = 0;
            // Compare each property in obj1 with the corresponding property in obj2
            foreach (var property1 in properties1) {
                var property2 = properties3.FirstOrDefault(p => p.Name == property1.Name);
                if (property2 != null) {
                    // Skip keys that are in the 'keysToIgnore' set
                    if (keysToIgnore.Contains(property1.Name)) {
                        continue; // Skip this key and move to the next iteration
                    }

                    // Get the values of the properties from both objects
                    var value1 = property1.GetValue(obj1);
                    var value2 = property2.GetValue(obj2);

                    // Compare the values
                    if (!EqualityComparer<object>.Default.Equals(value1, value2)) {
                        obj[index] = new object[] {
                            property1.Name, value1 ?? "null", value2 ?? "null"
                        };
                        index++;
                    }
                }
            }

            return obj;
        }

        return new object[0][];
    }
}