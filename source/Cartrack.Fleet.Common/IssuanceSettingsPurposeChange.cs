﻿namespace Cartrack.Fleet.Common;

public class IssuanceSettingsPurposeChange {
    public ChangeType Type { get; init; }
    public long? BookingPurposeId { get; set; }
    public string BookingPurposeName { get; set; } = "";
    public Dictionary<string, string> CurrentValue { get; init; } = new Dictionary<string, string>();
    public Dictionary<string, string> PreviousValue { get; init; } = new Dictionary<string, string>();
    public string ChangedBy { get; init; } = "";
    public DateTime? CreatedTime { get; init; } = null;
}