﻿using Cartrack.AppHost;

namespace Cartrack.Fleet.Common;

public class AppSettings(IEnvVars env) {
    
    public bool IsAccessoryServiceEnabled { get; }  = env.Get("IS_ACCESSORY_SERVICE_ENABLED", true);
    public bool IsAuditServiceEnabled { get; }  = env.Get("IS_AUDIT_SERVICE_ENABLED", true);
    public bool IsAuthServiceEnabled { get; }  = env.Get("IS_AUTH_SERVICE_ENABLED", true);
    public bool IsFilesServiceEnabled { get; }  = env.Get("IS_FILES_SERVICE_ENABLED", true);
    public bool IsBookingServiceEnabled { get; }  = env.Get("IS_BOOKING_SERVICE_ENABLED", true);
    public bool IsDriverServiceEnabled { get; }  = env.Get("IS_DRIVER_SERVICE_ENABLED", true);
    public bool IsUserServiceEnabled { get; }  = env.Get("IS_USER_SERVICE_ENABLED", true);
    public bool IsVehicleServiceEnabled { get; }  = env.Get("IS_VEHICLE_SERVICE_ENABLED", true);
    public bool IsLicenseServiceEnabled { get; }  = env.Get("IS_LICENSE_SERVICE_ENABLED", true);
    public bool IsScdfBookingsCronEnabled { get; }  = env.Get("IS_SCDF_BOOKINGS_CRON_ENABLED", true);

    public int Port { get; set; } = env.Get("PORT", 5105);
    public string ConnectionString { get; set; } = env.Get("CONNECTION_STRING", "HOST=localhost;PORT=5432;USER ID=CAMS_NET;PASSWORD=masterkey;DATABASE=ct_fleet;CommandTimeout=0\"")!;
    public int RefreshTokenValidityDays { get; set; } = env.Get("AUTH_REFRESH_TOKEN_VALIDITY_DAYS", 7);
    public int TokenValidityMinutes { get; set; } = env.Get("AUTH_TOKEN_VALIDITY_MINUTES", 30);
    public string PrivateKeyPath { get; set; } = env.Get("AUTH_PRIVATE_KEY_PATH", "")!;
    public string PublicKeyPath { get; set; } = env.Get("AUTH_PUBLIC_KEY_PATH", "")!;
    public string Issuer { get; set; } = env.Get("AUTH_ISSUER", "")!;
    public string Audience { get; set; } = env.Get("AUTH_AUDIENCE", "tfms-dev")!;
    public bool IsValidatingPassword { get; set; } = env.Get("IS_VALIDATING_PASSWORD", false)!;
    public string MainUser { get; set; } = env.Get("MAIN_USER", "scdf00001,spf000001")!;
    public int UserId { get; set; } = env.Get("USER_ID", 302737);
    public string TfmsEncryptionKey { get; set; } = env.Get("TFMS_ENCRYPTION_KEY", "vPWXWxv4rJ7iFQZhnQfq9z1jaO748Ps2")!;
    public string TfmsEncryptionIv { get; set; } = env.Get("TFMS_ENCRYPTION_IV", "X4Vwegf5FyXp2MSo")!;
    //public int MinIoCompressImageSizeLimit { get; set; } = env.Get("MINIO_COMPRESS_IMAGE_SIZE_LIMIT",1 * 1024 * 1024);
    //public string MinIoProxy { get; } =  env.Get("MINIO_PROXY", "")!; //set this up in UAT and PROD
    public bool ElitesValidateSslCertificate { get; set; } = env.Get("ELITES_VALIDATE_SSL_CERT", true);
    public TimeSpan ElitesPostTimeout { get; set; } = TimeSpan.Parse(env.Get("ELITES_POST_TIMEOUT", "00:00:30")!);
    public string ElitesClientId { get; set; } = env.Get("ELITES_CLIENT_ID", string.Empty)!;
    public string ElitesClientSecret { get; set; } = env.Get("ELITES_CLIENT_SECRET", string.Empty)!;
    public string ElitesX509Certificate { get; set; } = env.Get("ELITES_X509_CERT", "cert/client_cert.pfx")!;
    public Uri ElitesQdlApiUrl { get; set; } = new(env.Get("ELITES_QDL_API_URI", "http://localhost:9091/qdl")!);
    public Uri ElitesPdpApiUrl { get; set; } = new(env.Get("ELITES_PDP_API_URI", "http://localhost:9091/pdp")!);
    public string ClickhouseConnectionString { get; set; } = env.Get("CLICKHOUSE_CONNECTION_STRING", "")!;
    public TimeSpan ScdfBookingsCronSchedule { get; set; } = TimeSpan.Parse(env.Get("SCDF_BOOKINGS_CRON_SCHEDULE", "00:01:00")!);
    public long ScdfUserId { get;  }= env.Get("SCDF_USER_ID", 302739L);
    public string ScdfAccount { get;  }= env.Get("SCDF_ACCOUNT", "SCDF00001")!;
    
    public long SpfUserId { get;  }= env.Get("SPF_USER_ID", 302737L);
    public string SpfAccount { get;  }= env.Get("SPF_ACCOUNT", "SPF000001")!;

    public TimeSpan CacheExpiry { get; } = TimeSpan.Parse(env.Get("CACHE_EXPIRY", "00:10:00")!);
    
    //----------------------------
    // FilesService settings
    //---------------------------
    public TimeSpan FsMaxCallDurationMsec { get; init; } = TimeSpan.Parse(env.Get("FS_MAX_CALL_DURATION", "00:00:01")!);
    public string FsServerBaseUrl { get; init; } = env.Get("FS_SERVER_BASE_URL", $"http://localhost:5105")!;
    public int FsMaxFileSizeBytes { get; init; } = env.Get("FS_MAX_FILE_SIZE_BYTES", 100 * 1024 * 1024);
    public int FsBufferSize { get; init; } = env.Get("FS_BUFFER_SIZE_BYTES", 24 * 1024);
    public string FsEncryptionKey { get; init; } = env.Get("FS_ENCRYPTION_KEY", string.Empty)!;
    public string FsEncryptionVector { get; init; } = env.Get("FS_ENCRYPTION_VECTOR", string.Empty)!;
    public string FsPublicUrlSegment { get; init; } = env.Get("FS_PUBLIC_URL_SEGMENT", "PublicUrlSegment")!;
    public int FsWorkerCount { get; init; } = env.Get("FS_WORKER_THREAD_COUNT", 10);
    
    public StorageSettings GetStorageSettings(AuthClaims claims) {
        return this.GetStorageSettings(claims.Account);
    }

    private StorageSettings GetStorageSettings(string group) {
        Requires.NotNullOrEmpty(group, nameof(group));

        group = group.ToUpper();
        var rootPathEnv = $"FS_{group}_ROOT_UPLOAD_PATH";
        var rootPath = env.Get(rootPathEnv, string.Empty)!;
        Requires.NotNullOrEmpty(rootPath, rootPathEnv);

        var providerEnv = $"FS_{group}_STORAGE_PROVIDER";
        var prov = env.Get(providerEnv, string.Empty)!;
        Requires.NotNullOrEmpty(prov, providerEnv);
        var isProviderCorrect = Enum.TryParse<StorageProviderType>(prov, out var storageProviderType);
        Requires.IsTrue(() => isProviderCorrect, () => $"{providerEnv} setting {prov} is invalid");

        if (storageProviderType == StorageProviderType.MinIo) {
            Uri uri = new Uri(rootPath);
            var provBucketName = uri.AbsolutePath.Trim('/');
            Requires.NotNullOrEmpty(provBucketName, provBucketName);

            return new StorageSettings(env, group, storageProviderType, rootPath,  this.FsServerBaseUrl, provBucketName);
        }

        
        //FileSystem Provider
        var rootFolderName = Path.GetFileName(Path.GetFullPath(rootPath));
        return new StorageSettings(env, group, storageProviderType, rootPath, this.FsServerBaseUrl, rootFolderName);
    }
    
    
}
public enum StorageProviderType {
    FileSystem,
    MinIo,  
    AzureBlobStorage,
    PostgresDb,
    Mock
}

public class MinIoSettings(IEnvVars env, string group) {
    public bool UseSsl { get; set; } = env.Get($"FS_{group}_MINIO_WITH_SSL", true)!;
    public bool ValidateSslCertificate { get; set; } = env.Get($"FS_{group}_MINIO_VALIDATE_SSL_CERT", false);
    public string SecurityKey { get; set; } = env.Get($"FS_{group}_MINIO_KEY", "Yn4bSIdqKlCEKvjtkm0R")!;
    public string SecretKey { get; set; } = env.Get($"FS_{group}_MINIO_SECRET", "90GMVGwn6t7EDUBPlbyKHgXpkR2MM0CpMJZ6suSK")!;
    public int CompressImageSizeLimit { get; set; } = env.Get($"FS_{group}_MINIO_COMPRESS_IMAGE_SIZE_LIMIT",1 * 1024 * 1024);
    public string Proxy { get; } =  env.Get($"FS_{group}_MINIO_PROXY", "")!; //set this up in UAT and PROD
    public string X509Certificate { get; set; } = env.Get($"FS_{group}_MINIO_X509_CERT", "cert/client_cert.pfx")!;
    public TimeSpan PostTimeout { get; set; } = TimeSpan.Parse(env.Get($"FS_{group}_MINIO_POST_TIMEOUT", "00:00:30")!);
}
public class StorageSettings(IEnvVars env, string groupName, StorageProviderType providerType, string rootPath, string serverBaseUrl, string bucketName) {
    public string GroupName { get; init; } = groupName;
    public StorageProviderType ProviderType { get; init; } = providerType;

    public MinIoSettings MinIo { get; } = new(env, groupName);
    public string RootPath { get; init; } = rootPath;
    public string BucketName { get; init; } = bucketName;
    public string ServerBaseUrl { get; init; } = serverBaseUrl;
    
    
    // //Define defaults for the storage provider for SCDF
    // public string ScdfRootUploadPath { get; init; } = env.Get("FS_SCDF00001_ROOT_UPLOAD_PATH", "/var/tfms-scdf")!;
    // public string ScdfStorageProvider { get; init; } = env.Get("FS_SCDF00001_STORAGE_PROVIDER", "FileSystem")!;
    // public string ScdfMinIoSecurityKey { get; init; } = env.Get("FS_SCDF00001_STORAGE_PROVIDER_SECURITY_KEY", "")!;
    // public string ScdfMinIoSecretKey { get; init; } = env.Get("FS_SCDF00001_STORAGE_PROVIDER_SECRET_KEY", "")!;
    

    public string GetTempPath() {
        Requires.NotNullOrEmpty(RootPath, nameof(RootPath));
        
        var tempPath = Path.Combine(Path.GetTempPath(), this.BucketName);
        Directory.CreateDirectory(tempPath);
        return tempPath;
    }
}
