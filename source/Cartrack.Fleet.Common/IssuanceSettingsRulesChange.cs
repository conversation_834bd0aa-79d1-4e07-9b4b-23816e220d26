﻿namespace Cartrack.Fleet.Common;

public class IssuanceSettingsRulesChange {
    public ChangeType Type { get; init; }
    public long? BookingRuleId { get; set; }
    public string BookingRuleName { get; set; } = "";
    public Dictionary<string, string> CurrentValue { get; init; } = new Dictionary<string, string>();
    public Dictionary<string, string> PreviousValue { get; init; } = new Dictionary<string, string>();
    public string ChangedBy { get; init; } = "";
    public DateTime? CreatedTime { get; init; } = null;
}