﻿using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Common;

public record BaseResponse<T>(T? Value, Exception? Error = null) {
    private readonly bool _isServerError;
    public T? Value { get; init; } = Value;

    [JsonIgnore] public Exception? Error { get; init; } = Error;

    [JsonPropertyName("Success")] public bool IsOk { get; } = Error == null;

    [JsonIgnore]
    public bool IsServerError {
        get => !this.IsOk && this._isServerError;
        init => this._isServerError = value;
    }
}