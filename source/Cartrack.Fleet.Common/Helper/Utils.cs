﻿
namespace Cartrack.Fleet.Common.Helper {
    public class Utils {
        public static string DashesToCamelCase(string input) {
            if (string.IsNullOrEmpty(input))
                return input;

            var parts = input.Split('_');

            // First part stays lowercase
            var result = parts[0];

            // Capitalize the rest
            for (int i = 1; i < parts.Length; i++)
            {
                if (!string.IsNullOrEmpty(parts[i]))
                {
                    result += char.ToUpper(parts[i][0]) + parts[i].Substring(1);
                }
            }

            return result;
        }
    }
}