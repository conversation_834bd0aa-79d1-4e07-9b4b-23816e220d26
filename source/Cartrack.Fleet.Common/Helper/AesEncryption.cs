﻿using System.Security.Cryptography;
using System.Text;

namespace Cartrack.Fleet.Common.Helper {
    public class AesEncryption {
        public static string Encrypt(string plaintext, string key, string iv) {
            using (Aes aesAlg = Aes.Create()) {
                aesAlg.Key = Encoding.UTF8.GetBytes(key);
                aesAlg.IV = Encoding.UTF8.GetBytes(iv);
                aesAlg.Padding = PaddingMode.PKCS7; // Equivalent to OPENSSL_RAW_DATA in PHP

                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                using MemoryStream msEncrypt = new MemoryStream();
                using CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
                using StreamWriter swEncrypt = new StreamWriter(csEncrypt);
                swEncrypt.Write(plaintext);

                byte[] encrypted = msEncrypt.ToArray();
                return Convert.ToBase64String(encrypted);
            }
        }

        public static string Decrypt(string ciphertextBase64, string key, string iv) {
            try {
                byte[] cipherText = Convert.FromBase64String(ciphertextBase64);

                using Aes aesAlg = Aes.Create();
                aesAlg.Key = Encoding.UTF8.GetBytes(key);
                aesAlg.IV = Encoding.UTF8.GetBytes(iv);
                aesAlg.Padding = PaddingMode.PKCS7; // Equivalent to OPENSSL_RAW_DATA in PHP

                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                using MemoryStream msDecrypt = new MemoryStream(cipherText);
                using CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using StreamReader srDecrypt = new StreamReader(csDecrypt);
                return srDecrypt.ReadToEnd();
            }
            catch (FormatException fEx) {
                return string.Empty;
            }
        }
    }
}