﻿namespace Cartrack.Fleet.Common;

public class VehicleChange {
    public ChangeType Type { get; init; }
    public long? VehicleId { get; set; }
    public string VehicleRegistration { get; set; } = "";
    public Dictionary<string, string> CurrentValue { get; init; } = new Dictionary<string, string>();
    public Dictionary<string, string> PreviousValue { get; init; } = new Dictionary<string, string>();
    public string ChangedBy { get; init; } = "";
    public DateTime? CreatedTime { get; init; } = null;
}