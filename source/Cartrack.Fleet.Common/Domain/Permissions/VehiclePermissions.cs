﻿namespace Cartrack.Fleet.User.Domain.Permissions;

public class VehiclePermissions {
    [SettingsName("list_vehicles")]
    public bool CanViewVehicles { get; set; }
    
    [SettingsName("vehicles_addGroup")]
    public bool CanAddGroup { get; set; }
    
    [SettingsName("update_vehicle_odometer")]
    public bool CanAllowUpdatingOfVehicleOdometer { get; set; }
    
    [SettingsName("vehicles_deactivateVehicle")]
    public bool CanDeactivateVehicle { get; set; }
    
    [SettingsName("vehicles_editVehicle")]
    public bool CanEditVehicle { get; set; }
    
    [SettingsName("vehicles_importVehiclesToGroup")]
    public bool CanImportVehiclesToGroups { get; set; }
    
    [SettingsName("vehicles_viewStatus")]
    public bool CanViewStatus { get; set; }

    public static VehiclePermissions FullAccess() {
        var i = new VehiclePermissions();
        foreach (var pi in typeof(VehiclePermissions).GetProperties().Where(p => p.CanWrite && p.PropertyType == typeof(bool))) {
            pi.SetValue(i, true);
        }

        return i;
    }
}