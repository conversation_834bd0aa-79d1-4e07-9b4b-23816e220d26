﻿namespace Cartrack.Fleet.User.Domain.Permissions;

public class MapPermissions {
    
    [SettingsName("map")]
    public bool CanViewMap { get; init; }
    
    [SettingsName("map_compare_vehicles")]
    public bool CanCompareVehicles { get; init; }
    
    [SettingsName("map_timeline_date_range")]
    public bool CanEnableDateSelection { get; init; }
    
    [SettingsName("map_enable_trip_download")]
    public bool CanEnableTripDownload { get; init; }
    
    [SettingsName("map_follow_vehicle")]
    public bool CanFollowVehicle { get; init; }
    
    public static MapPermissions FullAccess() {
        var i = new MapPermissions();
        foreach (var pi in typeof(MapPermissions).GetProperties().Where(p => p.CanWrite && p.PropertyType == typeof(bool))) {
            pi.SetValue(i, true);
        }

        return i;
    }
}