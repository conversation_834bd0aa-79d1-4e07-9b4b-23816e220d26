﻿namespace Cartrack.Fleet.User.Domain.Permissions;

public class SetOfPermissions {
    //public bool HasAllAvailablePermission { get; init; }
    
    [SettingsName("userManageUsers")]
    public bool CanManageUsers { get; init; }
    
    [SettingsName("user_manage_roles")]
    public bool CanManageRoles { get; init; }
    public IssuancePermissions IssuancePermissions { get; init; } = new();
    public ListPermissions ListPermissions { get; init; } = new();
    public MapPermissions MapPermissions { get; init; }= new();
    public ReportPermissions ReportPermissions { get; init; }= new();
    public UserPermissions UserPermissions { get; init; }= new();

    public static SetOfPermissions FullAccess() {
        return new SetOfPermissions() {
            CanManageRoles = true, 
            CanManageUsers = true,
            ListPermissions = ListPermissions.FullAccess(),
            MapPermissions = MapPermissions.FullAccess(),
            ReportPermissions = ReportPermissions.FullAccess(),
            UserPermissions = UserPermissions.FullAccess(),
            IssuancePermissions = IssuancePermissions.FullAccess()
        };
    }
}