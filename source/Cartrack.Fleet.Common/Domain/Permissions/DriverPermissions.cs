﻿namespace Cartrack.Fleet.User.Domain.Permissions;

public class DriverPermissions {
    
    [SettingsName("list_drivers")]
    public bool CanViewDriverList { get; set;  }
    
    [SettingsName("drivers_addDriver")]
    public bool CanAddDriver { get; set; }
    
    [SettingsName("drivers_addGroup")]
    public bool CanAddGroup { get; set; }
    
    [SettingsName("drivers_deactivateDriver")]
    public bool CanDeactivateDriver { get; set; }
    
    [SettingsName("drivers_editDriver")]
    public bool CanEditDriver { get; set; }
    
    [SettingsName("drivers_importDrivers")]
    public bool CanImportDrivers { get; set; }
    public static DriverPermissions FullAccess() {
        var i = new DriverPermissions();
        foreach (var pi in typeof(DriverPermissions).GetProperties().Where(p => p.CanWrite && p.PropertyType == typeof(bool))) {
            pi.SetValue(i, true);
        }

        return i;
    }
}

