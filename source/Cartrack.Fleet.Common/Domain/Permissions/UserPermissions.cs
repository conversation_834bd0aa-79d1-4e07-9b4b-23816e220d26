﻿namespace Cartrack.Fleet.User.Domain.Permissions;

public class UserPermissions {
    
    [SettingsName("user_manageUsers")]
    public bool CanManageUsers { get; init; }
    
    [SettingsName("user_audit")]
    public bool CanAudit { get; init; }
    
    [SettingsName("user_profileSettings")]
    public bool CanViewProfileSettings { get; init; }
    
    [SettingsName("user_profileSettings_editUser")]
    public bool CanEditProfileSettings { get; init; }
 
    public static UserPermissions FullAccess() {
        var i = new UserPermissions();
        foreach (var pi in typeof(UserPermissions).GetProperties().Where(p => p.CanWrite && p.PropertyType == typeof(bool))) {
            pi.SetValue(i, true);
        }

        return i;
    }
}