﻿namespace Cartrack.Fleet.User.Domain.Permissions;

public class ListPermissions {
    [SettingsName("list")]
    public bool IsEnabled { get; init; }
    
    public VehiclePermissions VehiclePermissions { get; init; }= new();
    public DriverPermissions DriverPermissions { get; init; }= new();
    
    public static ListPermissions FullAccess() {
        return new ListPermissions() {
            VehiclePermissions = Permissions.VehiclePermissions.FullAccess(),
            DriverPermissions = Permissions.DriverPermissions.FullAccess()
        };

    }
}