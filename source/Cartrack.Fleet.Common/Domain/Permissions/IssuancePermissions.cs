﻿namespace Cartrack.Fleet.User.Domain.Permissions;

public class IssuancePermissions {
    
    [SettingsName("carpool")]
    public bool IsEnabled { get; set; }
    
    [SettingsName("carpoolAddRequestPurposeEnabled")]
    public bool CanAddRequestPurpose { get; set; }
    
    [SettingsName("carpoolAddVehicleCategoriesEnabled")]
    public bool CanAddVehicleCategory { get; set; }
    
    [SettingsName("carpoolAllowBackDateBooking")]
    public bool CanCreateBackdatedBooking { get; set; }
    
    [SettingsName("carpool_request_vehicle_by_registration")]
    public bool CanAllowVehicleSelectionDuringBooking { get; set; }
    
    [SettingsName("carpool_request_by_vehicle_type")]
    public bool CanAllowVehicleTypeDuringBooking { get; set; }
    
    [SettingsName("carpool_approve_bookings")]
    public bool CanApproveBookingRequest { get; set; }
    
    [SettingsName("carpoolAutoApprovalBooking")]
    public bool CanAutoApproveBookingRequest { get; set; }
    
    [SettingsName("carpool_enable_calendar")]
    public bool CanViewCalendar { get; set; }
    
    [SettingsName("carpool_cancel_booking")]
    public bool CanCancelBookingRequest { get; set; }
    
    [SettingsName("carpool_change_booking_to_active")]
    public bool CanChangeBookingToActive { get; set; }
    
    [SettingsName("carpool_decline_bookings")]
    public bool CanDeclineBookingRequest { get; set; }

    [SettingsName("carpoolEndBookings")] 
    public bool CanEndBookingRequest { get; set; } = true; //TODO: Andreas
    
    [SettingsName("carpoolViewOnlyMyBookings")] 
    public bool CanViewOnlyMyBookings { get; set; } = false; //TODO: Andreas

    
    [SettingsName("carpoolDeleteRequestPurposeEnabled")]
    public bool CanDeleteRequestPurpose { get; set; }
    
    [SettingsName("carpoolDeleteVehicleCategoriesEnabled")]
    public bool CanDeleteVehicleCategory { get; set; }
    
    [SettingsName("carpoolEditVehicleEnabled")]
    public bool CanEditAvailableVehicles { get; set; }
    
    [SettingsName("carpool_edit_bookings")]
    public bool CanEditBookingRequest { get; set; }
    
    [SettingsName("carpoolEditRequestPurposeEnabled")]
    public bool CanEditRequestPurpose { get; set; }
    
    [SettingsName("carpoolEditRulesEnabled")]
    public bool CanEditRules { get; set; }
    
    [SettingsName("carpoolEditVehicleCategoriesEnabled")]
    public bool CanEditVehicleCategory { get; set; }
    
    [SettingsName("carpool_force_terminate_booking")]
    public bool CanForceTerminateBooking { get; set; }
    
    [SettingsName("carpool_enable_resources")]
    public bool CanViewResources { get; set; }
    
    [SettingsName("carpool_enable_settings")]
    public bool CanViewSettings { get; set; }
    
    [SettingsName("carpoolViewTermsConditionsEnabled")]
    public bool CanViewTermsAndConditions { get; set; }

    [SettingsName("carpoolViewVehicleEnabled")]
    public bool CanViewAvailableVehicles { get; set; }
    
    [SettingsName("carpoolViewRulesEnabled")]
    public bool CanViewRules { get; set; }
    
    [SettingsName("carpoolViewVehicleCategoriesEnabled")]
    public bool CanViewVehicleCategory { get; set; }
    
    [SettingsName("carpoolViewVehicleEnabled")]
    public bool CanViewVehicle { get; set; }

    [SettingsName("carpoolViewRequestPurposeEnabled")]
    public bool CanViewRequestPurpose { get; set; } // ?

    public static IssuancePermissions FullAccess() {
        var i = new IssuancePermissions();
        foreach (var pi in typeof(IssuancePermissions).GetProperties().Where(p => p.CanWrite && p.PropertyType == typeof(bool))) {
            pi.SetValue(i, true);
        }

        i.CanViewOnlyMyBookings = false; // allow to view all bookings
        return i;
    }
}