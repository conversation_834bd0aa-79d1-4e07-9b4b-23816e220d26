﻿namespace Cartrack.Fleet.User.Domain.Permissions;

public class ReportPermissions {
    
    [SettingsName("reports")]
    public bool CanViewReports { get; init; }
    
    [SettingsName("reports_customize")]
    public bool CanCustomise { get; init; }
    
    [SettingsName("reports_favorites")]
    public bool CanViewFavorites { get; init; }
    
    [SettingsName("reports_information")]
    public bool CanViewInformation { get; init; }
    
    [SettingsName("reports_setup")]
    public bool CanViewSetup { get; init; }
    
    [SettingsName("reports_setup_downloadReport")]
    public bool CanSetupDownloadReport { get; init; }
    
    public static ReportPermissions FullAccess() {
        var i = new ReportPermissions();
        foreach (var pi in typeof(ReportPermissions).GetProperties().Where(p => p.CanWrite && p.PropertyType == typeof(bool))) {
            pi.SetValue(i, true);
        }

        return i;
    }
}