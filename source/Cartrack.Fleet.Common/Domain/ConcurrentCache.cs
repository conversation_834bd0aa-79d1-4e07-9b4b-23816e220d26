﻿using System.Collections.Concurrent;
using System.Diagnostics;

namespace Cartrack.Fleet.Common.Domain;

public class ConcurrentCache<K, V> where K : notnull {
   
    private readonly ConcurrentDictionary<K, IExpiry<V>> _cache = new();
    private readonly ConcurrentDictionary<K, SemaphoreSlim> _locks = new();
    private readonly TimeSpan _defaultExpiry = TimeSpan.FromMinutes(1);
    // ReSharper disable once NotAccessedField.Local
    private readonly Task _cleanupTask;
    private readonly PeriodicTimer? _cleanupTimer;
    public int Count => this._cache.Count;

    public ConcurrentCache(PeriodicTimer? cleanupTimer = null) : this(true, cleanupTimer) {
    }

    public ConcurrentCache(bool autoCleanup, PeriodicTimer? cleanupTimer = null) {
        
        if (autoCleanup) {
            var timer = cleanupTimer ?? new PeriodicTimer(TimeSpan.FromSeconds(5));
            this._cleanupTimer = timer;
            this._cleanupTask = this.Cleanup(timer);
        }
        else {
            this._cleanupTask = Task.CompletedTask;
        }
    }
    public ICollection<K> Keys => this._cache.Keys;

    public ICollection<V> Values => this._cache.Values.Select(t => t.Get()).ToList();

    private async Task Cleanup(PeriodicTimer timer) {
        while (await timer.WaitForNextTickAsync()) {
            try {
                foreach (var v in this._cache) {
                    if (v.Value.IsExpired()) {
                        this.TryRemove(v.Key, out _);
                    }
                }
            }
            catch (Exception ex) {
               
            }
        }
    }

    public bool TryRemove(K key, out V? value) {
        var removed = this._cache.TryRemove(key, out var v);
        this._locks.TryRemove(key, out _);
        value = removed ? v!.Get() : default;
        return removed;
    }

    public V GetOrAdd(K key, Func<K, V> valueFactory, TimeSpan? expiry = null, bool keepAlive = false) {
        expiry ??= this._defaultExpiry;
        var i = this._cache.GetOrAdd(key, k => {
            IExpiry<V> exp = keepAlive
                ? new KeepAliveIfUsed<V>(valueFactory(k), expiry.Value)
                : new FixedExpiry<V>(valueFactory(k), expiry.Value);
            return exp;
        });

        return i.Get();
    }

    public async Task<V> GetOrAddAsync(K key, Func<K, Task<V>> valueFactory, TimeSpan? expiry = null, bool keepAlive = false) {
        if (this._cache.TryGetValue(key, out var storedValue)) {
            return storedValue.Get();
        }

        if (expiry?.TotalMilliseconds == 0)
            return await valueFactory(key);
        
        expiry ??= this._defaultExpiry;

        var slim = this._locks.GetOrAdd(key, _ => new SemaphoreSlim(1));
        try {
            await slim.WaitAsync();
            if (this._cache.TryGetValue(key, out var storedValue2)) {
                return storedValue2.Get();
            }
            
            var value = await valueFactory(key);
            var i = this._cache.GetOrAdd(key, _ => {
                IExpiry<V> exp = keepAlive
                    ? new KeepAliveIfUsed<V>(value, expiry.Value)
                    : new FixedExpiry<V>(value, expiry.Value);
                return exp;
            });
            return i.Get();
        }
        finally {
            slim.Release();
        }
    }
    
    public bool TryGetValue(K key, out V? o) {
        var found = this._cache.TryGetValue(key, out var val);
        o = found ? val!.Get() : default;
        return found;
    }

    public void Clear() {
        this._cache.Clear();
    }

    public void Dispose() {
        this._cleanupTimer?.Dispose();
    }

    private interface IExpiry<T> {
        bool IsExpired();
        T Get();
    }

    private class KeepAliveIfUsed<T>(T value, TimeSpan expiry) : IExpiry<T> {
        private long _lastAccessAt = Stopwatch.GetTimestamp();

        public bool IsExpired() {
            var expired = TimeSpan.FromTicks(Stopwatch.GetTimestamp() - this._lastAccessAt) > expiry;
            return expired;
        }

        public T Get() {
            this._lastAccessAt = Stopwatch.GetTimestamp();
            return value;
        }
    }

    private class FixedExpiry<T> : IExpiry<T> {
        private readonly long _createdAt = Stopwatch.GetTimestamp();
        private readonly T _value;
        private readonly TimeSpan _expiry;

        public FixedExpiry(T value, TimeSpan expiry) {
            this._value = value;
            this._expiry = expiry;
        }

        public bool IsExpired() {
            var expired = TimeSpan.FromTicks(Stopwatch.GetTimestamp() - this._createdAt) > this._expiry;
            return expired;
        }

        public T Get() {
            return this._value;
        }
    }
}