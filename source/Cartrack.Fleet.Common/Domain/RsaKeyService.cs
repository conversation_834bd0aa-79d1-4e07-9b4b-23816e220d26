﻿using Cartrack.Fleet.Common.IO.FileSystem;
using Cartrack.Fleet.Common;
using System.Security.Cryptography;

namespace Cartrack.Fleet.Common.Domain;

public class RsaKeyService(AppSettings settings, IFileIo fileIo) : IRsaKeyService {
    private readonly string _privateKeyPath = settings.PrivateKeyPath;
    private readonly string _publicKeyPath = settings.PublicKeyPath;

    public RSA GetPrivateKey() {
        var privateKey = fileIo.ReadAllText(this._privateKeyPath);
        var rsa = RSA.Create();
        rsa.ImportFromPem(privateKey);
        return rsa;
    }

    public RSA GetPublicKey() {
        var publicKey = fileIo.ReadAllText(this._publicKeyPath);
        var rsa = RSA.Create();
        rsa.ImportFromPem(publicKey);
        return rsa;
    }
}