﻿using Microsoft.Extensions.Logging;
using Minio;
using Minio.DataModel;
using Minio.DataModel.Args;
using Minio.Exceptions;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;

namespace Cartrack.Fleet.Common;

public class MinIoHandler {
    private readonly string _bucketName; // appSettings.MinIoBucket;
    private readonly int _maxSizeInBytes; 

    private readonly string minIoTempFolder = "temp";
    private readonly IMinioClient _minioClient;
    private readonly ILogger _logger;

    public MinIoHandler(IMinioClient minioClient, AppSettings appSettings, AuthClaims claims, ILogger logger) {
        this._minioClient = minioClient;
        this._logger = logger;
        var storage = appSettings.GetStorageSettings(claims);
        this._bucketName = storage.BucketName;
        this._maxSizeInBytes = storage.MinIo.CompressImageSizeLimit;
    }

    public async Task<MinIoResponseResult> TempUploadBase64ImageAsync(string? base64String, string guidFileName, string extension) {
        if (string.IsNullOrEmpty(base64String)) {
            return new MinIoResponseResult {
                Guid = "",
                ImageUrl = "",
                Extension = "",
                ContentType = "",
                ErrorMessage = "Base64 string is empty"
            };
        }

        string filePath = Path.Combine(this.minIoTempFolder, guidFileName + "." + extension).Replace("\\", "/");
        MinIoResponseResult result;
        try {
            byte[] imageBytes = Convert.FromBase64String(base64String);
            imageBytes = this.CompressImage(imageBytes);
            using var stream = new MemoryStream(imageBytes);
            
            // Upload the image to the MinIO bucket
            await this._minioClient.PutObjectAsync(new PutObjectArgs()
                .WithBucket(this._bucketName)
                .WithObject(filePath)
                .WithStreamData(stream)
                .WithObjectSize(stream.Length)
            );

            //Console.WriteLine("temp image is uploaded successfully to server.");
            var imageUrl = $"{this._minioClient.Config.Endpoint}/{this._bucketName}/{filePath}";
            this._logger.LogInformation("image is uploaded successfully to server @ {Url}", imageUrl);
            result = new MinIoResponseResult {
                Guid = guidFileName,
                ImageUrl = imageUrl,
                Extension = extension,
                ContentType = "",
                ErrorMessage = ""
            };
        }
        catch (MinioException e) {
            this._logger.LogError(e, "MinIo Upload Failed.");
            //Console.WriteLine("MinIo Temp Upload Failed from Minio: " + e.Message);
            result = new MinIoResponseResult {
                Guid = "",
                ImageUrl = "",
                Extension = "",
                ContentType = "",
                ErrorMessage = e.Message
            };
        }

        return result;
        
    }

    public async Task<MinIoResponseResult> TempDeleteBase64Image(string guidFileName, string extension) {
        var result = new MinIoResponseResult { };

        string filePath = Path.Combine(this.minIoTempFolder, guidFileName + "." + extension).Replace("\\", "/");
        try {
            // Delete the image from /temp folder
            await this._minioClient.RemoveObjectAsync(new RemoveObjectArgs()
                .WithBucket(this._bucketName)
                .WithObject(filePath)
            );

            var imageUrl = $"{this._minioClient.Config.Endpoint}/{this._bucketName}/{filePath}";
            this._logger.LogInformation("temp image is deleted successfully from server. : {Path}", filePath);
            result = new MinIoResponseResult {
                Guid = guidFileName,
                ImageUrl = imageUrl,
                Extension = extension,
                ErrorMessage = ""
            };
        }
        catch (MinioException e) {
            Console.WriteLine("MinIo Temp File Delete Failed from Minio: " + e.Message);
            result = new MinIoResponseResult {
                Guid = guidFileName,
                ImageUrl = $"{this._minioClient.Config.Endpoint}/{this._bucketName}/{filePath}",
                Extension = extension,
                ErrorMessage = e.Message
            };
        }

        return result;
    }

    public async Task<MinIoResponseResult> DeleteImageByUrlFullPath(string urlFullPathFileName) {
        MinIoResponseResult result;

        Uri uri = new Uri(urlFullPathFileName);
        string path = uri.AbsolutePath; // "/fleet-tfms/BookingAttachment/2088/Equipment_1747823720348.jpg"

        // Step 2: Remove the bucket path (e.g., "/fleet-tfms/")
        string trimmedPath = path.TrimStart('/'); // "fleet-tfms/BookingAttachment/2088/Equipment_1747823720348.jpg"
        string[] pathParts = trimmedPath.Split('/');

        // Step 3: Skip the first segment (i.e., bucket name) )
        string filePath = string.Join('/', pathParts.Skip(1)); // "BookingAttachment/2088/Equipment_1747823720348.jpg"

        try {
            // Delete the image from filePath
            await this._minioClient.RemoveObjectAsync(new RemoveObjectArgs()
                .WithBucket(this._bucketName)
                .WithObject(filePath)
            );

            Console.WriteLine("image is deleted successfully from server.");
            result = new MinIoResponseResult {
                ImageUrl = $"{urlFullPathFileName}",
                ErrorMessage = ""
            };
        }
        catch (MinioException e) {
            Console.WriteLine("MinIo File Delete Failed from Minio: " + e.Message);
            result = new MinIoResponseResult {
                ImageUrl = $"{this._minioClient.Config.Endpoint}/{this._bucketName}/{filePath}",
                ErrorMessage = e.Message
            };
        }

        return result;
    }

    public async Task<MinIoResponseResult> CopyObjectImage(string sourceFileNameWithExtension, string toFileNameWithPathAndExtension) {
        var result = new MinIoResponseResult { };

        try {
            ObjectStat stat = await this.GetObjectStat(sourceFileNameWithExtension);
            if (stat.Size == 0) {
                result = new MinIoResponseResult {
                    ErrorMessage = $"File {sourceFileNameWithExtension} is not found in minIo. Ignore"
                };
                return result;
            }

            await this._minioClient.CopyObjectAsync(new CopyObjectArgs()
                .WithBucket(this._bucketName)
                .WithObject(toFileNameWithPathAndExtension)
                .WithCopyObjectSource(new CopySourceObjectArgs()
                    .WithBucket(this._bucketName)
                    .WithObject(sourceFileNameWithExtension))
            );

            this._logger.LogInformation("Image copied successfully to {toFileNameWithPathAndExtension}.", toFileNameWithPathAndExtension);
            var imageUrl = $"{this._minioClient.Config.Endpoint}/{this._bucketName}/{toFileNameWithPathAndExtension}";
            result = new MinIoResponseResult {
                ImageUrl = imageUrl,
                ErrorMessage = ""
            };
        }
        catch (MinioException e) {
            this._logger.LogError(e, "Copy failed from {sourceFileNameWithExtension} to {toFileNameWithPathAndExtension} in bucket {bucketName}",
                sourceFileNameWithExtension, toFileNameWithPathAndExtension, this._bucketName);
            result = new MinIoResponseResult {
                ImageUrl = "",
                ErrorMessage = e.Message
            };
        }

        return result;
    }

    public async Task<MinIoResponseResult> MoveObjectImage(string fromFileNameWithExtension,
        string toFileNameWithPathAndExtension) {
        var result = new MinIoResponseResult { };

        string fromFilePath = Path.Combine(this.minIoTempFolder, fromFileNameWithExtension).Replace("\\", "/");

        try {
            ObjectStat stat = await this.GetObjectStat(fromFilePath);
            if (stat.Size == 0) {
                result = new MinIoResponseResult {
                    ErrorMessage = "File is not found in temporary folder. Ignore"
                };
                return result;
            }

            await this._minioClient.CopyObjectAsync(new CopyObjectArgs()
                .WithBucket(this._bucketName)
                .WithObject(toFileNameWithPathAndExtension)
                .WithCopyObjectSource(new CopySourceObjectArgs()
                    .WithBucket(this._bucketName)
                    .WithObject(fromFilePath))
            );

            // Delete the image from /temp folder
            await this._minioClient.RemoveObjectAsync(new RemoveObjectArgs()
                .WithBucket(this._bucketName)
                .WithObject(fromFilePath)
            );

            this._logger.LogInformation("Image is moved successfully to {TargetPath}.", toFileNameWithPathAndExtension);
            var imageUrl = $"{this._minioClient.Config.Endpoint}/{this._bucketName}/{toFileNameWithPathAndExtension}";
            result = new MinIoResponseResult {
                ImageUrl = imageUrl,
                ErrorMessage = ""
            };
        }
        catch (MinioException e) {
            this._logger.LogError(e, "File Move {fromFilePath} -> {toFileNameWithPathAndExtension}. Bucket:{Bucket}", fromFilePath, toFileNameWithPathAndExtension, this._bucketName);
            result = new MinIoResponseResult {
                ImageUrl = "",
                ErrorMessage = e.Message
            };
        }

        return result;
    }

    public async Task<ObjectStat> GetObjectStat(string fileNamePathWithExtension) {
        return await this._minioClient.StatObjectAsync(new StatObjectArgs()
            .WithBucket(this._bucketName)
            .WithObject(fileNamePathWithExtension));
    }
    
    private byte[] CompressImage(byte[] imageBytes, int maxWidth = 1024, int maxHeight = 1024, int quality = 70)
    {
        // If image is already small enough, return it directly
        if (imageBytes.Length <= this._maxSizeInBytes)
            return imageBytes;
        
        using var inputStream = new MemoryStream(imageBytes);
        using var image = Image.Load(inputStream);

        // Resize while maintaining aspect ratio
        var resized = ResizeToFit(image, maxWidth, maxHeight);
        
        var encoder = new JpegEncoder { Quality = quality };

        using var outputStream = new MemoryStream();
        resized.Save(outputStream, encoder);

        return outputStream.ToArray();
    }

    private static Image ResizeToFit(Image image, int maxWidth, int maxHeight)
    {
        // If the image is already smaller than the max dimensions, return original
        if (image.Width <= maxWidth && image.Height <= maxHeight)
            return image;
        
        var ratioX = (double)maxWidth / image.Width;
        var ratioY = (double)maxHeight / image.Height;
        var ratio = Math.Min(ratioX, ratioY);

        var newWidth = (int)(image.Width * ratio);
        var newHeight = (int)(image.Height * ratio);

        var resized = image.Clone(ctx => ctx.Resize(newWidth, newHeight));
        return resized;
    }
}

public record MinIoResponseResult {
    public string? Guid;
    public string? ImageUrl;
    public string? Extension;
    public string? ContentType;
    public string? ErrorMessage;
}