﻿namespace Cartrack.Fleet.Common;

public class IssuanceSettingsVehicleCategoriesChange {
    public ChangeType Type { get; init; }
    public long? BookingVehicleTypeId { get; set; }
    public string BookingVehicleType { get; set; } = "";
    public Dictionary<string, string> CurrentValue { get; init; } = new Dictionary<string, string>();
    public Dictionary<string, string> PreviousValue { get; init; } = new Dictionary<string, string>();
    public string ChangedBy { get; init; } = "";
    public DateTime? CreatedTime { get; init; } = null;
}