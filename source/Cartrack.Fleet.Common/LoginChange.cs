﻿namespace Cartrack.Fleet.Common;

public class LoginChange {
    public long? ClientId { get; set; }
    public string ClientUserId { get; set; } = "";
    public string ClientUserName { get; set; } = "";
    public string ClientLoginSourceIP { get; set; } = "";
    public string ClientDetails { get; set; } = "";
    public string Reason { get; set; } = "";
    public DateTime? EventTs { get; init; } = null;
}