﻿using Cartrack.Fleet.Accessory.Features.GetAccessory.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Accessory.IO.Http;

public partial class ScdfAccessoryController {
    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult<ScdfGetAccessoryResponse>> GetAccessory(long id) {
        var resp = await this._mediator.Send(new ScdfGetAccessoryRequest(id));
        return resp.ToContentResult<ScdfGetAccessoryResponse, Accessory>(this.HttpContext);
    }
}