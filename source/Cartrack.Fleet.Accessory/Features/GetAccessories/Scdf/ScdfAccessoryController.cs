﻿using Cartrack.Fleet.Accessory.Features.GetAccessories.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Accessory.IO.Http;

public partial class ScdfAccessoryController {
    [HttpGet]
    public async Task<ActionResult<ScdfGetAccessoriesResponse>> GetAccessories() {
        var resp = await this._mediator.Send(new ScdfGetAccessoriesRequest());
        return resp.ToContentResult<ScdfGetAccessoriesResponse, Accessories>(this.HttpContext);
    }
}