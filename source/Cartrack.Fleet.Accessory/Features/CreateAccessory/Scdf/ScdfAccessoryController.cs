﻿using Cartrack.Fleet.Accessory.Features.CreateAccessory.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Accessory.IO.Http;

public partial class ScdfAccessoryController {
    [HttpPost]
    public async Task<ActionResult<ScdfCreateAccessoryResponse>> CreateBooking(
        [FromBody] ScdfCreateAccessoryRequest request) {
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfCreateAccessoryResponse, Accessory>(this.HttpContext);
    }
}