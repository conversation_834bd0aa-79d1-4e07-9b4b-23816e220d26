﻿using MediatR;

namespace Cartrack.Fleet.Accessory.Features.CreateAccessory.Scdf;

public record ScdfCreateAccessoryRequest : IRequest<ScdfCreateAccessoryResponse> {
    public string Account { get; init; } = "SCDF000001";
    public int UserId { get; init; } = 302739;
    public int AccessoryTypeId { get; init; }
    public required string Name { get; set; }
    public string? Description { get; set; }
}