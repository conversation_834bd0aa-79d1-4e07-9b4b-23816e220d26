﻿using Cartrack.AppHost;
using Cartrack.Fleet.Accessory.Domain;
using Cartrack.Fleet.Accessory.Domain.Common;
using Cartrack.Fleet.Accessory.Domain.Scdf;
using Cartrack.Fleet.Accessory.IO;
using Cartrack.Fleet.Accessory.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Accessory.Features.CreateAccessory.Scdf;

public class ScdfCreateAccessoryHandler(IAccessoryRepository repo, IHttpContextAccessor context, ILogger<ScdfCreateAccessoryHandler> logger)
    : IRequestHandler<ScdfCreateAccessoryRequest, ScdfCreateAccessoryResponse> {
    public async Task<ScdfCreateAccessoryResponse> Handle(ScdfCreateAccessoryRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            Requires.NotNullOrEmpty(request.Name, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Creating a accessory", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            AccessoryBase accessory = await CreateFromRequest(request);
            var accessoryId = await repo.CreateAccessory(request.UserId, accessory);
            var createdAccessory = await repo.GetAccessoryById(request.UserId, accessoryId);
            return new ScdfCreateAccessoryResponse(createdAccessory?.ToHttpAccessory());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error creating accessory", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfCreateAccessoryResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error creating accessory", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfCreateAccessoryResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private static async Task<AccessoryBase> CreateFromRequest(ScdfCreateAccessoryRequest request) {
        //1. Create Base Accessory
        var accessory = new AccessoryBase {
            UserId = 0,
            AccessoryTypeId = 1,
            Name = request.Name,
            Description = request.Description ?? "",
            IsDeleted = false
        };

        //2. Validate the accessory
        //await accessory.Validate();

        return accessory;
    }
}