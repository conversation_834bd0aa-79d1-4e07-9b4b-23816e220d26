﻿using Cartrack.EFCore.Models.Pool;
using Cartrack.Fleet.Accessory.Domain.Common;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Accessory.IO.Sql;

public partial class AccessoryRepository {
    public async Task<int> CreateAccessory(int userId, AccessoryBase accessory) {
        var accessoryRepo = new EFCore.Models.Pool.Accessory {
            UserId = userId,
            AccessoryTypeId = accessory.AccessoryTypeId,
            Name = accessory.Name,
            Description = accessory.Description,
            IsDeleted = false
        };
        await poolDbContext.Accessories.AddAsync(accessoryRepo);
        await poolDbContext.SaveChangesAsync();

        return accessoryRepo.Id;
    }
}