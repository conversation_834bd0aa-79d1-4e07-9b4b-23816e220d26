﻿namespace Cartrack.Fleet.Accessory.IO.Http;

public class Accessory {
    //TODO add the properties required by the client UI to display an accessory
    public int Id { get; set; }
    public int AccessoryTypeId { get; set; }
    public string? AccessoryTypeName { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
}

public record AccessoryStatus(long AccessoryId, string Status);