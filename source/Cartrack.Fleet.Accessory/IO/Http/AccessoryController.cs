﻿using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Accessory.IO.Http;

[ApiController]
[Route("accessory")]
public partial class AccessoryController : ControllerBase {
    private readonly AppSettings _appSettings;
    private readonly ILogger<AccessoryController> _logger;
    private readonly IMediator _mediator;

    public AccessoryController(ILogger<AccessoryController> logger, IMediator mediator, AppSettings appSettings) {
        this._logger = logger;
        this._mediator = mediator;
        this._appSettings = appSettings;
    }
}