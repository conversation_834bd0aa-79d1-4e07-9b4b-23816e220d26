﻿using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Accessory.IO.Http;

[ApiController]
[Route("scdf/accessory")]
public partial class ScdfAccessoryController : ControllerBase {
    private readonly AppSettings _appSettings;
    private readonly ILogger<ScdfAccessoryController> _logger;
    private readonly IMediator _mediator;

    public ScdfAccessoryController(ILogger<ScdfAccessoryController> logger, IMediator mediator,
        AppSettings appSettings) {
        this._logger = logger;
        this._mediator = mediator;
        this._appSettings = appSettings;
    }
}