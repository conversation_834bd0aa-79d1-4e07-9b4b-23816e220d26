﻿using Cartrack.Fleet.Accessory.Domain.Common;
using Cartrack.Fleet.Accessory.IO.Sql;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Accessory.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class AccessoryRepository(AppTfmsCustomDbContext tfmsCustomDbContext, AppPoolDbContext poolDbContext)
    : IAccessoryRepository {
    public async Task<AccessoryBase?> GetAccessoryById(int userId, long id) {
        var accessory = await poolDbContext.Accessories
            .Include(c => c.AccessoryType)
            .FirstOrDefaultAsync(c => c.UserId.Equals(userId) && c.Id == id);

        if (accessory is null) {
            return null;
        }

        var accessoryRepo = new AccessoryBase() {
            Id = accessory.Id,
            UserId = userId,
            AccessoryTypeId = accessory.AccessoryTypeId,
            AccessoryTypeName = accessory.AccessoryType?.Name,
            Description = accessory.Description ?? "",
            Name = accessory.Name ?? ""
        };

        return accessoryRepo;
    }

    public async Task<List<AccessoryBase>> GetAccessories(int userId) {
        var accessories = await poolDbContext.Accessories.Where(c => c.UserId == userId && c.IsDeleted == false)
            .Include(c => c.AccessoryType)
            .ToListAsync();

        List<AccessoryBase> accessoriesList = [];
        accessoriesList.AddRange(accessories
            .Select(accessory => new AccessoryBase() {
                Id = accessory.Id,
                UserId = userId,
                AccessoryTypeId = accessory.AccessoryTypeId,
                AccessoryTypeName = accessory.AccessoryType?.Name,
                Description = accessory.Description ?? "",
                Name = accessory.Name ?? ""
            })
        );

        return accessoriesList;
    }

    public async Task<int> GetTotal(int userId) {
        return await poolDbContext.Accessories.Where(c => c.UserId == userId && c.IsDeleted == false).CountAsync();
    }
}