﻿using Cartrack.Fleet.Accessory.Domain.Common;
using Db = Cartrack.EFCore.Models.Pool;
using Api = Cartrack.Fleet.Accessory.IO.Http;

namespace Cartrack.Fleet.Accessory.IO;

public static class AccessorySqlExtensions {
    public static AccessoryBase ToBooking(this Db.Booking dbBooking) {
        throw new NotImplementedException("TODO");
    }

    public static Api.Accessory? ToHttpAccessory(this AccessoryBase? entity) {
        if (entity == null) return null;

        return new Api.Accessory {
            Id = entity.Id,
            AccessoryTypeId = entity.AccessoryTypeId ?? 0,
            AccessoryTypeName = entity.AccessoryTypeName ?? "",
            Name = entity.Name,
            Description = entity.Description
        };
    }

    public static Api.Accessories ToHttpAccessories(this List<AccessoryBase>? entities, int totalCount) {
        if (entities == null) return null;

        List<Api.Accessory> accessories = [];
        accessories.AddRange(entities
            .Select(entity => new Api.Accessory {
                Id = entity.Id,
                AccessoryTypeId = entity.AccessoryTypeId ?? 0,
                AccessoryTypeName = entity.AccessoryTypeName,
                Name = entity.Name,
                Description = entity.Description
            })
        );

        return new Api.Accessories {
            Data = accessories ?? null, TotalCount = totalCount,
            //Message = ""
        };
    }

    public static Db.Accessory FromBooking(this AccessoryBase booking) {
        throw new NotImplementedException("TODO");
    }
}