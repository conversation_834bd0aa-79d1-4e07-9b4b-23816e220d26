﻿using Cartrack.Fleet.Accessory.IO.Http;
using Cartrack.Fleet.Accessory.IO.Sql;
using Cartrack.Fleet.Common;
using Microsoft.Extensions.DependencyInjection;

namespace Cartrack.Fleet.Accessory;

public static class AccessoryStartup {
    public static void Register(IServiceCollection builderServices, AppSettings appSetting) {
        //builderServices.AddScoped<IAuthRepository, AuthRepository>();
        builderServices.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<AccessoryController>());
        builderServices.AddControllers().AddApplicationPart(typeof(AccessoryController).Assembly);
        builderServices.AddScoped<IAccessoryRepository, AccessoryRepository>();
    }
}