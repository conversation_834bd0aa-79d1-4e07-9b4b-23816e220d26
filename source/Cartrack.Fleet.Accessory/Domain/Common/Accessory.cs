﻿namespace Cartrack.Fleet.Accessory.Domain.Common;

public abstract class Accessory : IAccessory {
    public int Id { get; set; }
    public int UserId { get; set; }
    public int? AccessoryTypeId { get; set; }
    public string? AccessoryTypeName { get; init; } = "";
    public required string Name { get; set; }
    public string Description { get; set; } = "";
    public bool IsDeleted { get; set; }
    public abstract Task Validate();
}