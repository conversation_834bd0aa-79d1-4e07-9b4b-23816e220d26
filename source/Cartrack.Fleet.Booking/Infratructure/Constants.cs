﻿namespace Cartrack.Fleet.Booking.Domain.Common;

public static class Constants {
    public const string Approved = "Approved";
    public const string Rejected = "Rejected";
    public const string Cancelled = "Cancelled";
    public const string Active = "Active";
    public const string ActivateLate = "ActiveLate";
    public const string Returned = "Returned";
    public const string ReturnedLate = "ReturnedLate";
    public const string ForceTerminated = "ForceTerminated";
    public const string Requested = "Requested";
    public const string ExpiringApproval = "ExpiringApproval";
    public const string Unknown = "Unknown";

    // Booking Status - Translation Key 
    public const string BookingStatusTranslationKeyApproved = "BOOKING_STATUS_APPROVED";
    public const string BookingStatusTranslationKeyRejected = "BOOKING_STATUS_DECLINED";
    public const string BookingStatusTranslationKeyCancelled = "BOOKING_STATUS_CANCELLED";
    public const string BookingStatusTranslationKeyActive = "BOOKING_STATUS_ACTIVE";
    public const string BookingStatusTranslationKeyActivateLate = "BOOKING_STATUS_ACTIVE_LATE";
    public const string BookingStatusTranslationKeyReturned = "BOOKING_STATUS_RETURNED";
    public const string BookingStatusTranslationKeyReturnedLate = "BOOKING_STATUS_RETURNED_LATE";
    public const string BookingStatusTranslationKeyForceTerminated = "BOOKING_STATUS_FORCE_TERMINATED";
    public const string BookingStatusTranslationKeyRequested = "BOOKING_STATUS_REQUESTED";
    public const string BookingStatusTranslationKeyExpiringApproval = "BOOKING_STATUS_EXPIRING_APPROVAL";
    public const string BookingStatusTranslationKeyUnknown = "BOOKING_STATUS_UNKNOWN";
    public const string BookingContext = "BOOKING_HTTPCONTEXT";
    
    // Booking Rules
    public const long BookingRuleCodeIsAutoApprovalEnabled = 100;
    public const long BookingRuleCodeBookInAdvanceBy = 101;
    public const long BookingRuleCodeMaximumBookingTime = 102;
    public const long BookingRuleCodeCheckDriverLicenseClass = 103;
    public const long BookingRuleCodeCheckDriverSpecialLicense = 104;
    public const long BookingRuleCodeIsKeyCollectionEnabled = 114;
    public const long BookingRuleCodeActivationType = 115;
    public const long BookingRuleCodeIsMultiLevelApprovalEnabled = 118;
    public const long BookingRuleCodeIsDriverRequired = 119;
    public const long BookingRuleCodePreDriveChecklistAvailableBy = 120;
    
}