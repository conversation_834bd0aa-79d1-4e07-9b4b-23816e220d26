using Cartrack.Fleet.Booking.Domain.Common;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.IO.Http;

public class Booking {
    public long Id { get; set; }
    public long? StatusId { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string? Description { get; set; }
    public string? Purpose { get; set; }
    public long? VehicleId { get; set; }

    public string? VehicleRegistration { get; set; }

    //public DateTime CreatedDate { get; set; }
    //public DateTime? UpdatedDate { get; set; }
    public DateTime? PickupTime { get; set; }
    public DateTime? DropoffTime { get; set; }
    public string Type { get; set; } = string.Empty;
    public string? RequestClientUserId { get; set; }
    public string? RequestedBy { get; set; }
    public DateTime? RequestedDate { get; set; }
    public Journey[]? Journeys { get; set; } = Array.Empty<Journey>();
    public long? PickupLocationId { get; set; }
    public string? PickupLocationName { get; set; }
    public string? RequestClientDriverId { get; set; }
    public string? DriverName { get; set; }
    public string? DriverEmail { get; set; }
    public string? Remarks { get; set; }
    public long? BookingPurposeId { get; set; }
    public string? BookingPurposeTitle { get; set; }
    public string? BookingPurposeDescription { get; set; }

    public long? VehicleCategoryId { get; set; }
    public string? VehicleCategoryName { get; set; }

    //public string? BookingReference { get; set; }
    public DateTime? KeyReturnTs { get; set; }
    public DateTime? KeyCollectionTs { get; set; }
    [JsonIgnore] public bool IsDetailView { get; set; } = false;
    public BookingAccessory[] Accessories { get; set; }

    public DisplayUser[]? ApprovedManagers { get; set; }
    public DisplayUser[]? RejectedManagers { get; set; }
    public string? CommanderClientUserId { get; set; }
    public string? CommanderUsername { get; set; }

    public string? RequestedForClientUserId { get; set; }
    public int? LocationType { get; set; }
    public int? DriverType { get; set; }
    public int VehicleCommanderType { get; set; }
    public int? EquipmentType { get; set; }
    public int? NumberOfPassengers { get; set; }

    public int BookingType { get; set; } = (int)Domain.Common.BookingType.Standard;

    // Only serialize accessories when viewing booking details
    // (list views no need to get these info to improve perf)
    public bool ShouldSerializeAccessories() => IsDetailView;
    public bool ShouldSerializeRequestedForClientUserId() => IsDetailView;
    public bool ShouldSerializeDriverType() => IsDetailView;
    public bool ShouldSerializeVehicleCommanderType() => IsDetailView;
    public bool ShouldSerializeEquipmentType() => IsDetailView;
    public bool ShouldSerializeNumberOfPassengers() => IsDetailView;
}

public class Journey {
    public int Id { get; set; }
    public string Location { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public int? Order { get; set; }
    public string? JourneyLocationType { get; set; }
    public string? JourneyLocationId { get; set; }
}

public class DisplayUser {
    public string? ClientUserId { get; set; }
    public string Username { get; set; }
}

public class BookingAccessory {
    public int Id { get; set; }
    public int? TypeId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public record BookingStatus(long BookingId, string Status);

public record TempUploadImageStatus(string Guid, string Extension, string Status, string ErrorMessage);

public record TempDeleteImageStatus(string Guid, string Extension, string Status);