﻿using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Domain.Permissions;
using Cartrack.Fleet.User.Features.GetPermissions;
using MediatR;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using Constants = Cartrack.Fleet.Booking.Domain.Common.Constants;

namespace Cartrack.Fleet.Booking.IO.Http.Filters;

public class BookingContextAttribute : ActionFilterAttribute
{
    public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next) {
        var httpContext = context.HttpContext;
        var mediator = context.HttpContext.RequestServices.GetService<IMediator>();
        var rule = context.HttpContext.RequestServices.GetService<IBookingRuleRepository>();
        var request = new GetPermissionsRequest();
        var authClaims = AuthClaims.From(httpContext.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = authClaims.ClientUserId;
        var permissionResponse = await mediator!.Send(request);
        
        var (_, bookingSettings) = await rule!.GetBookingSettingRules(authClaims.UserId);
        var bookingContext = new BookingContext(authClaims, bookingSettings,
            permissionResponse.IsOk ? permissionResponse.Value! : new SetOfPermissions());

        httpContext.Items[Constants.BookingContext] = bookingContext;
        
        
        await base.OnActionExecutionAsync(context, next);
        
    }
}