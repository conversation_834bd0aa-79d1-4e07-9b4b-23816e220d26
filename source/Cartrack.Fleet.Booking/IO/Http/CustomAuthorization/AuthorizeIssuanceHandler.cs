﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.GetPermissions;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.IO.Http.CustomAuthorization;

public class AuthorizeIssuanceHandler(IMediator mediator, IHttpContextAccessor httpContextAccessor, ILogger<AuthorizeIssuanceHandler> logger)
    : AuthorizationHandler<AuthorizeIssuanceAttribute> {
    // Check whether a given minimum age requirement is satisfied.
    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, AuthorizeIssuanceAttribute requirement) {
        var authClaims = AuthClaims.From(context.User);
        logger.LogInformation(
            "[{TraceId}] Evaluating authorization requirement for {Check}. Account: {Account}, Client user id: {ClientUserId}",
            httpContextAccessor.HttpContext?.TraceIdentifier ?? "",
            requirement.Rule,
            authClaims.Account,
            authClaims.ClientUserId);

        var request = new GetPermissionsRequest { Account = authClaims.Account, UserId = authClaims.UserId, ClientUserId = authClaims.ClientUserId };
        var perm = await mediator.Send(request);
        if (!perm.IsOk) {
            logger.LogWarning("[{TraceId}] Unable to find permissions for Client user id {ClientUserUd}. {Err}", 
                httpContextAccessor.HttpContext?.TraceIdentifier ?? "", 
                authClaims.ClientUserId,
                perm.Error);
            return;
        }

        switch (requirement.Rule) {
            case AuthorizeIssuanceCheck.CanRejectBooking when perm.Value!.IssuancePermissions.CanDeclineBookingRequest:
                context.Succeed(requirement);
                break;
            case AuthorizeIssuanceCheck.CanRejectBooking:
                logger.LogWarning(
                    "[{TraceId}] User does not have permission to reject the booking. Account: {Account}, Client user id: {ClientUserId}",
                    httpContextAccessor.HttpContext?.TraceIdentifier ?? "",
                    authClaims.Account,
                    authClaims.ClientUserId);
                break;
            case AuthorizeIssuanceCheck.CanApproveBooking when perm.Value!.IssuancePermissions.CanApproveBookingRequest:
                context.Succeed(requirement);
                break;
            case AuthorizeIssuanceCheck.CanApproveBooking:
                logger.LogWarning(
                    "[{TraceId}] User does not have permission to approve the booking. Account: {Account}, Client user id: {ClientUserId}",
                    httpContextAccessor.HttpContext?.TraceIdentifier ?? "",
                    authClaims.Account,
                    authClaims.ClientUserId);
                break;
            case AuthorizeIssuanceCheck.CanTerminateBooking when perm.Value!.IssuancePermissions.CanForceTerminateBooking:
                context.Succeed(requirement);
                break;
            case AuthorizeIssuanceCheck.CanTerminateBooking:
                logger.LogWarning(
                    "[{TraceId}] User does not have permission to terminate the booking. Account: {Account}, Client user id: {ClientUserId}",
                    httpContextAccessor.HttpContext?.TraceIdentifier ?? "",
                    authClaims.Account,
                    authClaims.ClientUserId);
                break;
            case AuthorizeIssuanceCheck.CanEndBooking when perm.Value!.IssuancePermissions.CanEndBookingRequest:
                context.Succeed(requirement);
                break;
            case AuthorizeIssuanceCheck.CanEndBooking:
                logger.LogWarning(
                    "[{TraceId}] User does not have permission to end the booking. Account: {Account}, Client user id: {ClientUserId}",
                    httpContextAccessor.HttpContext?.TraceIdentifier ?? "",
                    authClaims.Account,
                    authClaims.ClientUserId);
                break;
            case AuthorizeIssuanceCheck.CanActivateBooking when perm.Value!.IssuancePermissions.CanChangeBookingToActive:
                context.Succeed(requirement);
                break;
            case AuthorizeIssuanceCheck.CanActivateBooking:
                logger.LogWarning(
                    "[{TraceId}] User does not have permission to activate the booking. Account: {Account}, Client user id: {ClientUserId}",
                    httpContextAccessor.HttpContext?.TraceIdentifier ?? "",
                    authClaims.Account,
                    authClaims.ClientUserId);
                break;
            case AuthorizeIssuanceCheck.CanCancelBooking when perm.Value!.IssuancePermissions.CanCancelBookingRequest:
                context.Succeed(requirement);
                break;
            case AuthorizeIssuanceCheck.CanCancelBooking:
                logger.LogWarning(
                    "[{TraceId}] User does not have permission to cancel the booking. Account: {Account}, Client user id: {ClientUserId}",
                    httpContextAccessor.HttpContext?.TraceIdentifier ?? "",
                    authClaims.Account,
                    authClaims.ClientUserId);
                break;
        }
    }
}