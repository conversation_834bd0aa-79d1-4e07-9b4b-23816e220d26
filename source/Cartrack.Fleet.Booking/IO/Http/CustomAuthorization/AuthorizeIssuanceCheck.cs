﻿namespace Cartrack.Fleet.Booking.IO.Http.CustomAuthorization;

public static class AuthorizeIssuanceCheck {
    public const string CanRejectBooking = "CanRejectBooking";
    public const string CanApproveBooking = "CanApproveBooking";
    public const string CanTerminateBooking = "CanTerminateBooking";
    public const string CanActivateBooking = "CanActivateBooking";
    public const string CanEndBooking = "CanEndBooking";
    public const string CanCancelBooking = "CanCancelBooking";
}