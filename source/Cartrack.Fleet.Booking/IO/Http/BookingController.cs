﻿using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.IO.Http;

[ApiController]
[Route("booking")]
public partial class BookingController : ControllerBase {
    private readonly AppSettings _appSettings;
    private readonly ILogger<BookingController> _logger;
    private readonly IMediator _mediator;

    public BookingController(ILogger<BookingController> logger, IMediator mediator, AppSettings appSettings) {
        this._logger = logger;
        this._mediator = mediator;
        this._appSettings = appSettings;
    }
}