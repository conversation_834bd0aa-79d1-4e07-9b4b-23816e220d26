﻿using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.IO.Http;

[ApiController]
[Route("spf/booking")]
public partial class SpfBookingController : ControllerBase {
    private readonly AppSettings _appSettings;
    private readonly ILogger<SpfBookingController> _logger;
    private readonly IMediator _mediator;
    
    public SpfBookingController(ILogger<SpfBookingController> logger, IMediator mediator, AppSettings appSettings) {
        this._logger = logger;
        this._mediator = mediator;
        this._appSettings = appSettings;
    }
}