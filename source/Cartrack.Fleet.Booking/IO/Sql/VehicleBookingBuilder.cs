﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.States;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.IO.Http.Filters;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.Domain;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Driver.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
//using VehicleBooking = Cartrack.Fleet.Booking.Domain.VehicleBooking;
//using VehicleBooking = Cartrack.Fleet.Booking.Domain.VehicleBooking;

namespace Cartrack.Fleet.Booking.IO.Sql;

public abstract class VehicleBookingBuilder : IVehicleBookingBuilder {
    protected record ClientUserRecord(string ClientUserId, string UserName);
    private static readonly ConcurrentCache<string, ClientUserRecord?> CachedClientUsers = new();
    protected record DriverRecord(string DriverName, string DriverSurname, string Email, string ClientDriverId);
    private static readonly ConcurrentCache<string, DriverRecord?> CachedDrivers = new();

    private readonly AppSettings _appSettings;
    protected readonly IMediator _mediator;
    private readonly IDriverRepository _driverRepo;
    private readonly AppCtDbContext _ctDbContext;
    private readonly AppFleetDbContext _fleetDbContext;
    private readonly AppPoolDbContext _poolDbContext;
    protected readonly AppTfmsCustomDbContext _tfmsCustomDbContext;
    protected EFCore.Models.Pool.Booking? _booking;
    //protected List<ClientUserRecord> _clientUsers = [];

    private List<EFCore.Models.Pool.BookingApproval> _approvals = [];

    public VehicleBookingBuilder(AppSettings appSettings, IMediator mediator, IDriverRepository driverRepo, AppCtDbContext ctDbContext, AppFleetDbContext fleetDbContext,
        AppPoolDbContext poolDbContext, AppTfmsCustomDbContext tfmsCustomDbContext, IHttpContextAccessor contextAccessor) {
        this._appSettings = appSettings;
        this._mediator = mediator;
        this._driverRepo = driverRepo;
        this._ctDbContext = ctDbContext;
        this._fleetDbContext = fleetDbContext;
        this._poolDbContext = poolDbContext;
        this._tfmsCustomDbContext = tfmsCustomDbContext;
        this.BookingContext = contextAccessor.GetBookingContext();
    }

    public Func<string, AppFleetDbContext> CreateAppFleetDb { get; init; } = conString => new AppFleetDbContext(conString);
    public BookingContext? BookingContext { get; private set; }

    public abstract Task<VehicleBookingBase?> Build(long id); 
    public VehicleBookingBase? VehicleBooking { get; private set; }

    public abstract VehicleBookingBase Create();
    public virtual async Task Start(long bookingId) {
        var vehicleBooking = this.Create();
        this.VehicleBooking = vehicleBooking;
         
        if (bookingId <= 0)
            return;

        var bookings = await _poolDbContext.Bookings.Where(b => b.BookingId == bookingId).ToArrayAsync();
        var booking = bookings.FirstOrDefault();
        
        Requires.NotNull(booking, nameof(booking)); 
        
        this._booking = booking!;
        vehicleBooking.UserId = this._booking.UserId;
        vehicleBooking.BookingId = this._booking.BookingId;
        vehicleBooking.StartDate = this._booking.StartTs;
        vehicleBooking.EndDate = this._booking.EndTs;
        vehicleBooking.Description = this._booking.BookingPurposeDescription;
        vehicleBooking.CreatedDate = this._booking.RequestTs;
        vehicleBooking.UpdatedDate = this._booking.DecisionTs ?? this._booking.RequestTs;
        vehicleBooking.Type = this._booking.BookingType.HasValue ? (BookingType)this._booking.BookingType.Value : BookingType.Standard;
        vehicleBooking.RequestedDate = this._booking.RequestTs;
        vehicleBooking.BookingReference = this._booking.BookingReference;
        vehicleBooking.KeyReturnTs = this._booking.KeyReturnTs;
        vehicleBooking.PickupTime = this._booking.PickupIgnitionTs;
        vehicleBooking.DropoffTime = this._booking.ReturnedIgnitionTs;
        vehicleBooking.Remarks = this._booking.Remarks;
        vehicleBooking.Status = BaseBookingState.GetState(this._booking.BookingStatusId, vehicleBooking);
        vehicleBooking.BookingAuditReference = Guid.NewGuid().ToString("N") + "_" +DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        
      
        // Get booking approvals
        this._approvals = await this._poolDbContext.BookingApprovals
            .Where(ba => ba.BookingId == this._booking.BookingId)
            .ToListAsync();

    }

    public async Task IncludePurpose() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
            
        if (this._booking!.BookingPurposeId.HasValue && this._booking.BookingPurposeId.Value > 0) {
            await this.IncludePurpose(this._booking.BookingPurposeId.Value);
        }
    }
    
    public async Task IncludePurpose(long bookingPurposeId) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        Requires.IsTrue(() => bookingPurposeId > 0, () => "Booking purpose id must be greater than zero.");
        
        var purpose = (await this._poolDbContext.BookingPurposes
            .Where(bp => bp.BookingPurposeId == bookingPurposeId)
            .Select(bp => bp.BookingPurpose1).ToArrayAsync())
            .FirstOrDefault();

        this.VehicleBooking!.Purpose = new RequestPurpose {
            Id = bookingPurposeId,
            Title = purpose ?? string.Empty,
            Description = this._booking?.BookingPurposeDescription
        };

        this.VehicleBooking.BookingPurposeId = bookingPurposeId;
        
    }

    public async Task IncludePickupLocation() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        if (this._booking!.PickupSiteLocationId > 0)
            await this.IncludePickupLocation(this._booking.PickupSiteLocationId);
    }


    public async Task IncludePickupLocation(long pickupSiteLocationId) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        Requires.IsTrue(() => pickupSiteLocationId > 0, () => "Pickup site location id must be greater than zero.");
     
        // Get pickup location
        var pickupLocation = (await this._fleetDbContext.SiteLocations
            .Where(sl => sl.SiteLocationId == pickupSiteLocationId)
            .Select(sl => new { sl.SiteLocationId, sl.SiteLocationName })
            .ToArrayAsync())
            .FirstOrDefault();
        
        this.VehicleBooking!.PickupLocation = new RequestLocation { Id = pickupSiteLocationId, Name = pickupLocation?.SiteLocationName ?? string.Empty };
        this.VehicleBooking.PickupSiteLocationId = pickupSiteLocationId;
    }

    public async Task IncludeVehicle() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        if (this._booking!.VehicleId.HasValue && this._booking.VehicleId.Value > 0) {
            await this.IncludeVehicle(this._booking.VehicleId.Value);
        }
    }

    public async Task IncludeVehicle(long vehicleId) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        Requires.IsTrue(() => vehicleId > 0, () => "VehicleId must be greater than zero");
        
        var vehicle = (await this._ctDbContext.Vehicles
            .Where(v => v.VehicleId == vehicleId)
            .Select(v => new { v.VehicleId, v.Registration }).ToArrayAsync())
            .FirstOrDefault();

        Requires.IsTrue(() => vehicle is not null, () => $"VehicleId {vehicleId} mot found");
        
        var vehicleAdditionalInfo = (await this._fleetDbContext.VehicleAdditionalInfos
            .Where(v => v.VehicleId == vehicleId)
            .Select(v => new {v.CommonPool, v.IsPoolActive, v.DefaultSiteLocationId}).ToArrayAsync())
            .FirstOrDefault();
            
        if (vehicle != null || vehicleAdditionalInfo != null) {
            this.VehicleBooking!.Target = new VehicleTarget {
                Vehicle = new Domain.Common.Vehicle {
                    VehicleId = vehicleId, 
                    Registration = vehicle!.Registration, 
                    CommonPool = vehicleAdditionalInfo?.CommonPool ?? false,
                    CarpoolEnabled = vehicleAdditionalInfo?.IsPoolActive ?? false,
                    DefaultSiteLocationId = vehicleAdditionalInfo?.DefaultSiteLocationId ?? 0
                }
            };

            this.VehicleBooking.VehicleId = vehicleId;
        }

    }

    public async Task IncludeCategory() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        if (this._booking!.BookingVehicleTypeId.HasValue) {
            await this.IncludeCategory(this._booking.BookingVehicleTypeId.Value);
        }
    }

    public async Task IncludeCategory(long vehicleTypeId) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        if (vehicleTypeId > 0) {
            var vehicleType = (await this._poolDbContext.BookingVehicleTypes
                .Where(vt => vt.BookingVehicleTypeId == vehicleTypeId)
                .Select(vt => vt.BookingVehicleType1).ToArrayAsync())
                .FirstOrDefault();

            this.VehicleBooking!.VehicleCategory = new RequestVehicleType {
                Id = vehicleTypeId, Name = vehicleType ?? string.Empty
            };

            this.VehicleBooking.BookingVehicleTypeId = vehicleTypeId;
        }
    }

    
    public async Task IncludeDriver() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Get driver details
        if (!string.IsNullOrEmpty(this._booking!.RequestClientDriverId)) {
            await this.IncludeDriver(this._booking.RequestClientDriverId);
        }
    }

    public async Task IncludeDriverByClientUserId(string clientUserId) {
        clientUserId = clientUserId.Trim();
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        Requires.NotNullOrEmpty(clientUserId, nameof(clientUserId));

        var d = (await this._fleetDbContext.ClientDrivers.Where(d => d.ClientUserId == clientUserId).ToArrayAsync()).FirstOrDefault();
        if (d is not null)
            await this.IncludeDriver(d.ClientDriverId);
    }

    protected async Task<DriverRecord?> GetDriver(string clientDriverId) {
        return await CachedDrivers.GetOrAddAsync(clientDriverId, async (key) => {
                await using var dbContext = this.CreateAppFleetDb(this._appSettings.ConnectionString);
                var d = (await dbContext.ClientDrivers.Where(c => c.ClientDriverId == clientDriverId).ToArrayAsync()).FirstOrDefault();
                if (d is null)
                    return null;

                return new DriverRecord(d.DriverName, d.DriverSurname ?? string.Empty, d.EMail ?? "", d.ClientDriverId);
            },
            expiry: this._appSettings.CacheExpiry,
            keepAlive: true);
    }

    public async Task IncludeDriver(string requestClientDriverId) {
        requestClientDriverId = requestClientDriverId.Trim();
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
       // Requires.NotNullOrEmpty(requestClientDriverId, nameof(requestClientDriverId));
        
        // Get driver details
        var driver = await this.GetDriver(requestClientDriverId);
        if (driver != null) {
            this.VehicleBooking!.Driver = new Domain.Common.Driver {
                DriverId = requestClientDriverId, 
                DriverName = driver.DriverName,
                DriverSurname = driver.DriverSurname,
                DriverDepartments = await _driverRepo.GetDepartmentsByDriverId(this.VehicleBooking!.UserId, requestClientDriverId),
                DriverQdlLicense = await _driverRepo.GetQdlLicensesById(requestClientDriverId),
                DriverPdpLicense = await _driverRepo.GetPdpLicensesById(requestClientDriverId),
                Email = driver.Email ?? ""
            };
            
            var isSelfDrive = this.VehicleBooking.RequestClientDriverId == driver.ClientDriverId;
            this.VehicleBooking.DriverType = isSelfDrive ? DriverType.SelfDrive : DriverType.Specific;
            this.VehicleBooking.RequestClientDriverId = requestClientDriverId;
        }
    }

    public async Task IncludeJourneyType(JourneyType journeyType) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        await Task.Yield();
        this.VehicleBooking!.JourneyType = journeyType;
    }
    
    public async Task IncludeJourneys() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Get journeys for this booking
        var journeys = await this._poolDbContext.BookingJourneys
            .Where(j => j.BookingId == this._booking!.BookingId)
            .Select(j => new Journey {
                Id = j.Id,
                Start = j.StartTs,
                End = j.EndTs,
                Location = j.Location ?? string.Empty,
                Order = j.Order,
                LocationReference = new JourneyLocation() { Type = j.JourneyLocationType ?? string.Empty, Value = j.JourneyLocationId ?? string.Empty, }
            })
            .ToListAsync();

        await this.IncludeJourneys(journeys);
    }

    public Task IncludeJourneys(IList<Journey> journeys) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        this.VehicleBooking!.Journeys = journeys;
        return Task.CompletedTask;
    }

    public async Task IncludeAccessories() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Get accessories for this booking
        var bookingAccessories = await this._poolDbContext.BookingAccessories
            .Where(ba => ba.BookingId == this._booking!.BookingId && !ba.IsDeleted)
            .Join(
                this._poolDbContext.Accessories,
                ba => ba.AccessoryId,
                a => a.Id,
                (ba, a) => new {
                    BookingAccessory = ba, Accessory = a
                }
            )
            .ToListAsync();

        foreach (var item in bookingAccessories) {
            this.VehicleBooking!.DetailedAccessories.Add(new BookingAccessoryDetail {
                Id = item.Accessory.Id,
                TypeId = item.Accessory.AccessoryTypeId,
                Name = item.Accessory.Name ?? string.Empty,
                Description = item.Accessory.Description ?? string.Empty,
            });
        }
    }

    protected async Task<ClientUserRecord?> GetClientUser(string clientUserId) {
        return await CachedClientUsers.GetOrAddAsync(clientUserId, async _ => {
            await using var db =  this.CreateAppFleetDb(this._appSettings.ConnectionString);
            var cu = (await db.ClientUsers.Where(t => t.ClientUserId == clientUserId).ToArrayAsync()).FirstOrDefault();
            if (cu is null)
                return null;
            
            return  new ClientUserRecord(cu.ClientUserId, cu.UserName);
        },
        expiry: this._appSettings.CacheExpiry,
        keepAlive: true);
         
    }
    
    public async Task IncludeRequestedBy() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Set requested by user
        // Database auto add empty space of RequestClientUserId so we will need to trim it
        if (!string.IsNullOrWhiteSpace(this._booking!.RequestClientUserId)) {
            await this.IncludeRequestedBy(this._booking.RequestClientUserId);
        }
    }

    public async Task IncludeRequestedBy(string clientUserId) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Set requested by user
        // Database auto add empty space of RequestClientUserId so we will need to trim it
        if (!string.IsNullOrWhiteSpace(clientUserId)) {
            this.VehicleBooking!.RequestedBy = new ClientUser {
                ClientUserId = clientUserId,
                Username = (await this.GetClientUser(clientUserId))?.UserName ?? ""
            };
            this.VehicleBooking.RequestClientUserId = clientUserId;
        }
        else {
            this.VehicleBooking!.RequestedBy = new ClientUser {
                ClientUserId = string.Empty, Username = this._booking!.Requestor ?? "",
            };
        }

        this.VehicleBooking.RequestClientUserId = clientUserId;
    }

   
    public async Task IncludeRequestedFor(string clientUserId) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Set requested by user
        // Database auto add empty space of RequestClientUserId so we will need to trim it
        if (!string.IsNullOrWhiteSpace(clientUserId)) {
            this.VehicleBooking!.RequestedFor = new ClientUser {
                ClientUserId = clientUserId,
                Username = (await this.GetClientUser(clientUserId))?.UserName ?? this._booking!.Requestor ?? ""
            };
        }
        else {
            this.VehicleBooking!.RequestedFor = new ClientUser {
                ClientUserId = string.Empty, Username = this._booking!.Requestor ?? "",
            };
        }

        this.VehicleBooking.RequestedForClientUserId = clientUserId;
    }
    
    
    public async Task IncludeApprovedBy() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Set approved managers
        var approvedManagers = new List<ClientUser>();

        var approvedManagerIds = this._approvals
            .Where(ba => ba.IsApproved == true)
            .Select(ba => ba.UnitManagerId)
            .ToList();

        foreach (var managerId in approvedManagerIds) {
            approvedManagers.Add(new ClientUser {
                ClientUserId = managerId,
                Username = (await this.GetClientUser(managerId))?.UserName ?? ""
            });
        }

        // If no approvals found but booking is approved, consider decision_client_user_id
        if (!approvedManagers.Any() && this._booking!.IsApproved == true) {
            if (!string.IsNullOrEmpty(this._booking.DecisionClientUserId)) {
                approvedManagers.Add(new ClientUser {
                    ClientUserId = this._booking.DecisionClientUserId,
                    Username =  (await this.GetClientUser(this._booking.DecisionClientUserId))?.UserName ?? ""
                });
            }
            else {
                // System approval case
                approvedManagers.Add(new ClientUser { ClientUserId = "", Username = "System" });
            }
        }

        this.VehicleBooking!.ApprovedBy = approvedManagers;
    }

    public async Task IncludeRejectedBy() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Set rejected managers
        var rejectedManagers = new List<ClientUser>();

        var declinedManagerIds = this._approvals
            .Where(ba => ba.IsApproved == false)
            .Select(ba => ba.UnitManagerId)
            .ToList();

        foreach (var managerId in declinedManagerIds) {
            rejectedManagers.Add(new ClientUser {
                ClientUserId = managerId, 
                Username = (await this.GetClientUser(managerId))?.UserName ?? ""
            });
        }

        // If no declined approvals found but booking is declined, consider decision_client_user_id
        if (!rejectedManagers.Any() && (this._booking!.IsApproved == false || this._booking.BookingStatusId == 4)) {
            if (!string.IsNullOrEmpty(this._booking.DecisionClientUserId)) {
                rejectedManagers.Add(new ClientUser {
                    ClientUserId = this._booking.DecisionClientUserId,
                    Username = (await this.GetClientUser(this._booking.DecisionClientUserId))?.UserName ?? ""
                });
            }
            else {
                // System decline case
                rejectedManagers.Add(new ClientUser {
                    ClientUserId = "", Username = "System"
                });
            }
        }

        this.VehicleBooking!.RejectedBy = rejectedManagers;
    }

    public async Task IncludeRejectedBy(string clientUserId) {
        if (!string.IsNullOrWhiteSpace(clientUserId)) {
            var c = new ClientUser() { 
                ClientUserId = clientUserId,
                Username = (await this.GetClientUser(clientUserId))?.UserName };

            this.VehicleBooking!.RejectedBy = [c];
        }
    }
    
    public async Task IncludeCancellation() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        
        if (!string.IsNullOrWhiteSpace(this._booking!.CanceledClientUserId)) {
            this.VehicleBooking!.CancellationDate = this._booking.CanceledTs;
            this.VehicleBooking.CancellationNotes = this._booking.BookingCancelNotes;
            this.VehicleBooking.CancellationReasonId = this._booking.BookingCancelReasonId;
            await this.IncludeCancelledBy();
        }
    }
    
    public async Task IncludeCancelledBy() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Set requested by user
        // Database auto add empty space of RequestClientUserId so we will need to trim it
        if (!string.IsNullOrWhiteSpace(this._booking!.CanceledClientUserId)) {
            await this.IncludeCancelledBy(this._booking.CanceledClientUserId);
        }
    }
    
    public async Task IncludeCancelledBy(string clientUserId) {
        if (!string.IsNullOrWhiteSpace(clientUserId)) {
            var c = new ClientUser() { 
                ClientUserId = clientUserId,
                Username = (await this.GetClientUser(clientUserId))?.UserName };

            this.VehicleBooking!.CancelledBy = c;
        }
    }
}