﻿using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Minio;
using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("Cartrack.Fleet.Booking.Tests")]
namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class ScdfBookingRepository : BookingRepository {
    private readonly AppTfmsCustomDbContext _tfmsCustomDbContext;

    public ScdfBookingRepository(Common.AppSettings appSettings, 
        AppTfmsCustomDbContext tfmsCustomDbContext, 
        AppFleetDbContext fleetDbContext, 
        AppCtDbContext ctDbContext, 
        AppPoolDbContext poolDbContext,
        IMinioClient minioClient, 
        IBookingFilterService filterService, 
        IHttpContextAccessor context,
        Func<string, IVehicleBookingBuilder> bookingBuilder,
        ILogger<ScdfBookingRepository> logger) : base(appSettings, tfmsCustomDbContext, fleetDbContext, ctDbContext, poolDbContext, minioClient, filterService, context, bookingBuilder, logger) {
        this._tfmsCustomDbContext = tfmsCustomDbContext;
    }

    internal ScdfBookingRepository() : base() {
        
    }
}