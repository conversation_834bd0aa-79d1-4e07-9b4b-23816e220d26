using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Carpool;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Booking.IO.Http.Filters;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Vehicle.Domain.Common;

namespace Cartrack.Fleet.Booking.IO.Sql;

public interface IBookingRepository {
    Task<VehicleBookingBase?> GetBooking(string agency, long id);
    Task<VehicleBookingBase?> GetBooking(IVehicleBookingBuilder builder, long id);
    Task<BookingsResult> GetBookings(string clientUserId, ServerRequestModel serverRequest, BookingContext bookingContext);

    Task<long> CreateBooking(VehicleBookingBase booking);
    Task AddOtherScdfBookingInfo(VehicleBookingBase booking, long bookingId);
    Task<long> UpdateBooking(VehicleBookingBase booking);
    Task<long> ApproveBooking(VehicleBookingBase booking);
    Task<long> RejectBooking(VehicleBookingBase booking);
    Task<long> ActivateBooking(VehicleBookingBase booking);
    Task ForceTerminateBooking(List<long> bookingIds, string forceTerminateClientUserId, int forceTerminateReasonId, string bookingForceTerminateNotes);
    Task UpdateStatus(long bookingId, BookingStatusCode bookingStatusId);
    Task<List<BookingAccessory>?> GetBookingAccessory(long bookingId);
    Task<List<AdditionalLocation>> GetAdditionalLocations(long userId);
    Task<List<RequestPurpose>> GetRequestPurposes(long userId, long bookingPurposeId);
    Task<List<RejectBookingReason>> GetRejectBookingReasons(long userId, long rejectBookingReasonId);
    Task<List<CancelBookingReason>> GetCancelBookingReasons(long userId, long cancelBookingReasonId);
    Task<List<ForceTerminateBookingReason>> GetForceTerminateBookingReasons(long userId, long forceTerminateBookingReasonId);
    Task<List<RequestPurposeVehicleCategoryMap>> GetRequestPurposeVehicleCategoriesMaps(long userId, long bookingPurposeId, int page, int pageSize);
    Task<int> GetRequestPurposeVehicleCategoriesMapsTotal(int userId);
    Task AddOrUpdateBookingJourneys(long userId, long bookingId, string bookingAuditReference, IList<Journey> bookingJourney);
    Task AddOrUpdateBookingAccessory(long bookingId, string bookingAuditReference, List<int>? bookingAccessory);
    Task<bool> IsBookingPurposeOthersRequired(VehicleBookingBase booking);
    Task<bool> IsBookingTimeConflicting(VehicleBookingBase booking);
    Task<bool> IsVehicleCommonPoolRuleAllowed(bool commonPool, List<DriverDepartment> clientDriverDepartments, List<VehicleDepartment> vehicleDepartments);
    Task<ClientUser> GetClientUserById(string? clientUserId);
    Task<List<long>> GetBookingsIdByUserId(long userId);
    Task<BookingAttachments> TempUploadSingleImage(string? base64Input, string? extension);
    Task<BookingAttachments> DeleteTempUploadSingleImage(string? guid, string? extension);
    Task<List<BookingAttachments>> GetBookingAttachments(long bookingId);
    Task<BookingCancelReasonBase?> GetBookingCancelReason(string agency, long id);
    Task CancelBooking(long bookingId, string bookingAuditReference, string canceledClientUserId, long bookingCancelReasonId, string bookingCancelNotes);
    Task<Domain.Common.Driver> GetClientDriverById(string id);
    Task UpdateBookingApproval(VehicleBookingBase booking);
    Task<List<CarpoolBooking>> GetBooingByVehicleId(long vehicleId);
    Task<List<CarpoolBooking>> GetBooingByDriverId(string clientDriverId);
}