﻿using Cartrack.EFCore.Models.Pool;
using Cartrack.Fleet.Booking.Domain.Carpool;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.User.Domain.Common;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Minio;
using Npgsql;
using System.Runtime.CompilerServices;
using AppSettings = Cartrack.Fleet.Common.AppSettings;
using BookingAccessory = Cartrack.Fleet.Booking.Domain.Common.BookingAccessory;

[assembly: InternalsVisibleTo("Cartrack.Fleet.Booking.Tests")]
namespace Cartrack.Fleet.Booking.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class BookingRepository : IBookingRepository {
    private readonly AppSettings _appSettings;
    protected readonly AppTfmsCustomDbContext _tfmsCustomDbContext;
    protected readonly AppFleetDbContext _fleetDbContext;
    protected readonly AppCtDbContext _ctDbContext;
    protected readonly AppPoolDbContext _poolDbContext;
    protected readonly IMinioClient _minioClient;
    protected readonly IBookingFilterService _filterService;
    protected readonly Func<string, IVehicleBookingBuilder> _bookingBuilder;
    protected readonly AuthClaims _user;
    private readonly ILogger<BookingRepository> _logger;
    

    public BookingRepository(
        Common.AppSettings appSettings,
        AppTfmsCustomDbContext tfmsCustomDbContext,
        AppFleetDbContext fleetDbContext,
        AppCtDbContext ctDbContext,
        AppPoolDbContext poolDbContext,
        IMinioClient minioClient,
        IBookingFilterService filterService,
        IHttpContextAccessor context,
        Func<string, IVehicleBookingBuilder> bookingBuilder, 
        ILogger<BookingRepository> logger) {
        this._appSettings = appSettings;
        this._tfmsCustomDbContext = tfmsCustomDbContext;
        this._fleetDbContext = fleetDbContext;
        this._ctDbContext = ctDbContext;
        this._poolDbContext = poolDbContext;
        this._minioClient = minioClient;
        this._filterService = filterService;
        this._bookingBuilder = bookingBuilder;
        this._logger = logger;
        if(context.HttpContext is not null)
            this._user = AuthClaims.From(context.HttpContext.User);
    }

    protected internal BookingRepository(){}
    public async Task UpdateStatus(long bookingId, BookingStatusCode bookingStatusId) {
        var booking = await this._poolDbContext.Bookings.FirstOrDefaultAsync(b => b.BookingId == bookingId);

        if (booking is not null) {
            var stat = await this._poolDbContext.BookingStatuses.FirstOrDefaultAsync(s =>
                s.BookingStatusId == (long)bookingStatusId);
            if (stat is not null) {
                booking.BookingStatusId = stat.BookingStatusId;
                await this._poolDbContext.SaveChangesAsync();
            }
        }
    }

    public async Task<List<BookingAccessory>?> GetBookingAccessory(long bookingId) {
        var bookingAccessory = await this._poolDbContext.BookingAccessories
            .Where(b => b.BookingId == bookingId)
            .Select(c => new BookingAccessory {
                AccessoryId = c.AccessoryId,
                IsDeleted = c.IsDeleted
            })
            .ToListAsync();

        return bookingAccessory ?? null;
    }

    public async Task AddOrUpdateBookingJourneys(long userId, long bookingId, string bookingAuditReference, IList<Journey> bookingJourney) {
        // delete current journey
        var bookingJ = await this._poolDbContext.BookingJourneys.Where(b => b.BookingId == bookingId).ToListAsync();
        this._poolDbContext.BookingJourneys.RemoveRange(bookingJ);

        // add new journeys
        if (bookingJourney.Count > 0) {
            foreach (var bj in bookingJourney) {
                string journeyLocationType = bj.LocationReference.Type.ToLower();
                string locationText = "";
                string locationId = bj.LocationReference.Value;

                if (journeyLocationType == "freetext") {
                    locationText = bj.LocationReference.Value;
                    var pois = new EFCore.Models.Pool.AdditionalLocation {
                        LocationName = locationText,
                        Description = locationText,
                        //UserId = userId, --> need to be added too. do not remove this.
                    };

                    // check if it has same name in AdditionalLocation table
                    var singlePoi = await this._poolDbContext.AdditionalLocations
                        .Where(x => x.LocationName!.ToLower().Equals(locationText.ToLower()))
                        .FirstOrDefaultAsync();

                    journeyLocationType = "additionallocation";
                    if (singlePoi == null) {
                        await this._poolDbContext.AdditionalLocations.AddAsync(pois);
                        await this._poolDbContext.SaveChangesAsync();
                        locationId = pois.Id.ToString();
                    }
                    else {
                        locationId = singlePoi.Id.ToString();
                    }
                }

                var bookingJourneyMap = new EFCore.Models.Pool.BookingJourney {
                    UserId = userId,
                    BookingId = bookingId,
                    StartTs = null,
                    EndTs = null,
                    Order = bj.Order,
                    JourneyLocationType = journeyLocationType,
                    JourneyLocationId = locationId,
                    BookingAuditReference = bookingAuditReference
                };

                this._poolDbContext.BookingJourneys.Add(bookingJourneyMap);
            }
        }

        await this._poolDbContext.SaveChangesAsync();
    }

    public async Task AddOrUpdateBookingAccessory(long bookingId, string bookingAuditReference, List<int>? bookingAccessory) {
        if (bookingAccessory is null) {
            return;
        }

        // update IsDeleted = true
        var existingBookingAccessories = await this._poolDbContext.BookingAccessories
            .Where(a => a.BookingId == bookingId && !a.IsDeleted)
            .ToListAsync();

        foreach (var b in existingBookingAccessories) {
            b.IsDeleted = true;
        }

        // insert if new data, update if existing
        foreach (var b in bookingAccessory) {
            var bookingAccessoryByBookingId = await this._poolDbContext.BookingAccessories
                .Where(a => a.BookingId == bookingId && a.AccessoryId == b)
                .FirstOrDefaultAsync();

            if (bookingAccessoryByBookingId == null) {
                var bookingAccessoryMap = new EFCore.Models.Pool.BookingAccessory {
                    BookingId = bookingId,
                    AccessoryId = b,
                    IsDeleted = false,
                };
                this._poolDbContext.BookingAccessories.Add(bookingAccessoryMap);
            }
            else if (bookingAccessoryByBookingId.IsDeleted) {
                bookingAccessoryByBookingId.IsDeleted = false;
            }
        }
        await this._poolDbContext.SaveChangesAsync();
        
        // Update BookingAuditReference
        await this._poolDbContext.BookingAccessories
            .Where(x => x.BookingId == bookingId)
            .ExecuteUpdateAsync(setters => setters
                .SetProperty(x => x.BookingAuditReference, bookingAuditReference));
    }

    public async Task UpdateBookingEquipmentAttachmentIds(long bookingId, string bookingAuditReference, List<int> bookingAttachmentIds) {
        MinIoHandler minIo = new MinIoHandler(this._minioClient, this._appSettings, this._user, this._logger);

        // Get the attachments to delete from the DB
        var attachmentsToDelete = await this._poolDbContext.BookingAttachments
            .Where(a => !bookingAttachmentIds.Contains(a.Id) && a.BookingId == bookingId)
            .ToListAsync();

        // Delete files from MinIO
        foreach (var attachment in attachmentsToDelete) {
            if (attachment.UrlAttachment != null) {
                await minIo.DeleteImageByUrlFullPath(attachment.UrlAttachment);
            }
        }

        // Remove from DB
        this._poolDbContext.BookingAttachments.RemoveRange(attachmentsToDelete);
        await this._poolDbContext.SaveChangesAsync();
        
        // Update BookingAuditReference
        await this._poolDbContext.BookingAttachments
            .Where(x => x.BookingId == bookingId)
            .ExecuteUpdateAsync(setters => setters
                .SetProperty(x => x.BookingAuditReference, bookingAuditReference));
    }

    // Duplicate (copy) the given attachment ids with new minIo objects
    // public async Task AddBookingEquipmentAttachmentIds(long bookingId, string bookingAuditReference, List<int> bookingAttachmentIds) {
    //     MinIoHandler minIo = new MinIoHandler(this._minioClient,this._appSettings, this._logger);
    //
    //     // Get the existing attachments from the DB
    //     var existingAttachments = await this._poolDbContext.BookingAttachments
    //         .Where(a => bookingAttachmentIds.Contains(a.Id))
    //         .ToListAsync();
    //
    //     // copy the attachments in minIo
    //     foreach (var attachment in existingAttachments) {
    //         if (attachment.UrlAttachment == null) {
    //             continue;
    //         }
    //
    //         string url = attachment.UrlAttachment;
    //         int startIndex = url.IndexOf("/BookingAttachment", StringComparison.Ordinal);
    //
    //         if (startIndex == -1) {
    //             continue;
    //         }
    //
    //         var sourceFileName = url.Substring(startIndex);
    //
    //         string newFileName =
    //             $"BookingAttachment/{bookingId}/Equipment_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}_{attachment.Filename}";
    //         // copy the minIo with given source file name
    //         MinIoResponseResult minIoImageResult = await minIo.CopyObjectImage(sourceFileName, newFileName);
    //         if (string.IsNullOrEmpty(minIoImageResult.ImageUrl)) {
    //             continue;
    //         }
    //
    //         // add the new attachment to DB
    //         await this._poolDbContext.BookingAttachments.AddAsync(new BookingAttachment {
    //             BookingId = bookingId,
    //             UrlAttachment = minIoImageResult.ImageUrl,
    //             Filename = attachment.Filename,
    //             BookingAuditReference = bookingAuditReference
    //         });
    //         await this._poolDbContext.SaveChangesAsync();
    //     }
    // }

    private async Task AddBookingEquipmentAttachments(long bookingId, string bookingAuditReference, List<InputEquipmentAttachment>? bookingAttachments) {
        MinIoHandler minIo = new MinIoHandler(this._minioClient, this._appSettings, this._user, this._logger);

        if (bookingAttachments != null) {
            foreach (var ba in bookingAttachments) {
                string guidFileName = ba.Guid + "." + ba.Extension;
                string realFileName = $"booking/{bookingId}/Attachment/Equipment_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}_{ba.RealFilename}";
                var minIoImageResult = await minIo.MoveObjectImage(guidFileName, realFileName);
                if (string.IsNullOrEmpty(minIoImageResult.ImageUrl)) {
                    continue;
                }

                await this._poolDbContext.BookingAttachments.AddAsync(new BookingAttachment {
                    BookingId = bookingId,
                    UrlAttachment = minIoImageResult.ImageUrl,
                    Filename = ba.RealFilename,
                    BookingAuditReference = bookingAuditReference
                });
                await this._poolDbContext.SaveChangesAsync();
            }
        }
    }

    public async Task<bool> IsBookingPurposeOthersRequired(VehicleBookingBase booking) {
        var exist = (await this._poolDbContext.BookingPurposes
            .Where(x => x.BookingPurposeId == booking.BookingPurposeId && !x.IsDeleted).ToArrayAsync())
            .FirstOrDefault();

        return exist != null && exist.BookingPurpose1.Equals("others", StringComparison.CurrentCultureIgnoreCase);
    }

    public async Task<bool> IsBookingTimeConflicting(VehicleBookingBase booking) {
        bool driverExist = false;
        bool vehicleExist = false;
        bool vehicleCommanderExist = false;

        var query = this._poolDbContext.Bookings
            .Where(b => b.UserId == booking.UserId)
            .Where(b => (b.StartTs <= booking.EndDate && booking.StartDate <= b.EndTs))
            .Where(b => (b.BookingStatusId == (long)BookingStatusCode.Active
                        || b.BookingStatusId == (long)BookingStatusCode.Requested
                        || b.BookingStatusId == (long)BookingStatusCode.Approved
                        || b.BookingStatusId == (long)BookingStatusCode.ExpiringApproval
                        || b.BookingStatusId == (long)BookingStatusCode.ActiveLate));

        if (booking?.BookingId != null) {
            query = query.Where(b => b.BookingId != booking.BookingId);
        }

        var bookings = await query.ToListAsync();

        // Check SCDF Table Custom Table
        List<long> bookingIds = bookings
            .Select(b => b.BookingId)
            .ToList();

        if (!string.IsNullOrWhiteSpace(booking?.VehicleCommanderClientUserId)) {
            vehicleCommanderExist = await this._tfmsCustomDbContext.ScdfBookingAdditionalInfos
                .AnyAsync(x => bookingIds.Contains((long)x.BookingId)
                               && x.VehicleCommanderClientUserId == booking.VehicleCommanderClientUserId);
        }

        if (!string.IsNullOrWhiteSpace(booking?.RequestClientDriverId)) {
            driverExist = bookings.Any(x => x.RequestClientDriverId == booking.RequestClientDriverId);
        }

        if (booking?.VehicleId is > 0) {
            vehicleExist = bookings.Any(x => x.VehicleId == booking.VehicleId);
        }

        return driverExist || vehicleCommanderExist || vehicleExist;
    }

    public async Task<bool> IsVehicleCommonPoolRuleAllowed(bool commonPool, List<DriverDepartment> clientDriverDepartments,
        List<VehicleDepartment> vehicleDepartments) {
        if (commonPool == false) {
            // must check Driver Department and Vehicle Department match.
            var matchFound = clientDriverDepartments
                .Any(driver => vehicleDepartments
                    .Any(vehicle => driver.Id == vehicle.Id));

            return matchFound;
        }

        return true;
    }

    public async Task UpdateClientDriverIdToTerminalTripBasedOnBooking(VehicleBookingBase booking) {
        var bookingStartDate = booking.StartDate;
        var bookingEndDate = booking.EndDate;
        
        var vehicleId = (booking.Target as VehicleTarget)?.Vehicle?.VehicleId;
        if (vehicleId == null) { return;}
        
        var getDriver = await this._fleetDbContext.ClientDrivers
            .Where(a => a.ClientDriverId.Equals(booking.RequestClientDriverId)).FirstOrDefaultAsync();
        
        var terminalTrips = await this._fleetDbContext.TerminalTrips
            .Where(a => a.VehicleId == vehicleId)
            .Where(a => a.StartTimestamp >= bookingStartDate && a.EndTimestamp <= bookingEndDate)
            .ToListAsync();
        
        foreach (var terminalTrip in terminalTrips) {
            terminalTrip.ClientDriverId = booking.RequestClientDriverId;
            terminalTrip.DriverName = getDriver?.DriverName ?? "-";
            terminalTrip.DriverSurname = getDriver?.DriverSurname ?? "-";
        }
        await this._fleetDbContext.SaveChangesAsync();
    }
    
    public async Task CreateBookingMetric(VehicleBookingBase booking) {
        var bookingMetric = new EFCore.Models.Pool.BookingMetric() {
            BookingId = booking.BookingId,
        };
        await this._poolDbContext.BookingMetrics.AddAsync(bookingMetric);
        await this._poolDbContext.SaveChangesAsync();
    }

    public async Task DeleteBookingApproval(VehicleBookingBase booking) {
        if (booking.BookingId == 0) { return; }

        const string sqlQuery = @"DELETE FROM pool.booking_approval WHERE booking_id = @BookingId";
        object[] parameters = [
            new NpgsqlParameter("@BookingId", booking.BookingId)
        ];

        await this._poolDbContext.Database.ExecuteSqlRawAsync(sqlQuery, parameters);
    }

    public async Task CreateBookingApproval(VehicleBookingBase booking) {
        var clientDriverId = booking.RequestClientDriverId;
        if (booking.IsAutoApproveEnabled || string.IsNullOrEmpty(clientDriverId)) {
            return;
        }

        var driverDepartmentIds = await this._fleetDbContext.ClientDriverDepartments
            .Where(x => x.ClientDriverId == clientDriverId && x.IsActive == true)
            .Select(x => x.DepartmentId)
            .ToListAsync();

        var bookingApproveList = await this._fleetDbContext.ClientUserDepartments
            .Where(x => driverDepartmentIds.Contains(x.DepartmentId) && x.IsActive == true)
            .Select(x => new Domain.Common.BookingApproval {
                BookingId = booking.BookingId,
                UserId = booking.UserId,
                DepartmentId = x.DepartmentId,
                ClientUserId = x.ClientUserId,
                IsNotified = false,
                IsApproved = false
            })
            .ToListAsync();

        foreach (var bookingApproval in bookingApproveList) {
            if (string.IsNullOrEmpty(bookingApproval.ClientUserId)) continue;

            const string sqlQuery = @"INSERT INTO pool.booking_approval(                    
                                booking_id,
                                user_id, 
                                department_id,
                                unit_manager_id,
                                is_notified,
                                is_approved
                    ) VALUES (
                    @BookingId,
                    @UserId, 
                    @DepartmentId,
                    @UnitManagerId,
                    @IsNotified,
                    @IsApproved
                    )";

            object[] parameters = [
                new NpgsqlParameter("@BookingId", bookingApproval.BookingId),
                new NpgsqlParameter("@UserId", bookingApproval.UserId),
                new NpgsqlParameter("@DepartmentId", bookingApproval.DepartmentId),
                new NpgsqlParameter("@UnitManagerId", bookingApproval.ClientUserId),
                new NpgsqlParameter("@IsNotified", bookingApproval.IsNotified),
                new NpgsqlParameter("@IsApproved", bookingApproval.IsApproved)
            ];

            await this._poolDbContext.Database.ExecuteSqlRawAsync(sqlQuery, parameters);
        }
    }

    public async Task<Domain.Common.Driver> GetClientDriverById(string id) {
        var clientUserDepartment = await this._fleetDbContext.ClientDriverDepartments
            .Where(c => c.ClientDriverId == id && c.IsActive == true)
            .Select(x => new DriverDepartment {
                Id = x.DepartmentId ?? 0
            })
            .ToListAsync();

        var qdlLicense = await this._fleetDbContext.ClientDriverLicenses
            .Where(c => c.ClientDriverId == id)
            .Select(x => new DriverQdlLicense() {
                LicenseTypeId = x.DriverLicenseTypeId
            })
            .ToListAsync();

        var pdpLicense = await this._fleetDbContext.ClientDriverSpecialLicenses
            .Where(c => c.ClientDriverId == id)
            .Select(x => new DriverPdpLicense() {
                LicenseTypeId = x.DriverSpecialLicenseTypeId
            })
            .ToListAsync();

        var result = await this._fleetDbContext.ClientDrivers
            .Where(c => c.ClientDriverId == id)
            .Select(x => new Domain.Common.Driver {
                DriverId = x.ClientDriverId,
                DriverName = x.DriverName,
                DriverSurname = x.DriverSurname ?? "",
                DriverDepartments = clientUserDepartment,
                DriverQdlLicense = null,
                DriverPdpLicense = null
            })
            .FirstOrDefaultAsync();

        return result ?? new Domain.Common.Driver();
    }

    public async Task<ClientUser> GetClientUserById(string id) {
        var emptyDefault = new ClientUser {
            ClientUserId = "",
            Username = "",
        };

        // Department
        var clientUserDepartment = await this._fleetDbContext.ClientUserDepartments
            .Where(c => c.ClientUserId == id && c.IsActive == true)
            .Select(x => new UserDepartment {
                Id = x.DepartmentId ?? 0
            }).ToListAsync();

        var result = await this._fleetDbContext.ClientUsers
            .Where(c => c.ClientUserId == id)
            .Select(x => new ClientUser {
                ClientUserId = x.ClientUserId,
                Username = x.UserName,
                CreatedDate = x.Cts,
                Email = x.EMail,
                IsDeleted = x.IsDeleted,
                PasswordHash = x.PasswordHash,
                Departments = clientUserDepartment
            })
            .FirstOrDefaultAsync();

        return result ?? emptyDefault;
    }

    public async Task UpdateBookingApproval(VehicleBookingBase booking) {
        var bookingId = booking.BookingId;
        var rejectedClientUserIds = booking?.RejectedBy.Select(r => r.ClientUserId).ToList();
        if (rejectedClientUserIds != null) {
            foreach (var unitManagerId in rejectedClientUserIds) {
                const string sql = """
                                                      UPDATE pool.booking_approval
                                                      SET is_approved = @p0,
                                                      action_ts = @p1
                                                      WHERE booking_id = @p2 AND unit_manager_id = @p3 AND is_approved IS NULL
                                   """;

                var parameters = new object[] {
                    false, DateTime.UtcNow, // @p0
                    bookingId, // @p1
                    unitManagerId // @p2
                };

                await this._poolDbContext.Database.ExecuteSqlRawAsync(sql, parameters);
            }
        }
    }

    public async Task<List<long>> GetBookingsIdByUserId(long userId) {
        var bookingIds = await this._poolDbContext.Bookings.Where(b => b.UserId == userId).Select(a => a.BookingId).ToListAsync();
        return bookingIds;
    }

    public async Task<List<CarpoolBooking>> GetBooingByVehicleId(long vehicleId) {
        var bookings = await this._poolDbContext.Bookings
            .Where(c => c.VehicleId == vehicleId)
            .ToListAsync();

        List<CarpoolBooking> bookingsBase = new List<CarpoolBooking>();
        foreach (var booking in bookings) {
            CarpoolBooking carpoolBooking = new CarpoolBooking() {
                UserId = booking.UserId,
                BookingId = booking.BookingId,
                BookingStatusId = (BookingStatusCode)booking.BookingStatusId,
                StartDate = booking.StartTs,
                EndDate = booking.EndTs,
                VehicleId = booking.VehicleId
            };
            bookingsBase.Add(carpoolBooking);
        }
        return bookingsBase;
    }

    public async Task<List<CarpoolBooking>> GetBooingByDriverId(string clientDriverId) {
        var bookings = await this._poolDbContext.Bookings
            .Where(c => c.RequestClientDriverId == clientDriverId)
            .OrderBy(x=>x.BookingId)
            .ToListAsync();

        List<CarpoolBooking> bookingsBase = new List<CarpoolBooking>();
        foreach (var booking in bookings) {
            CarpoolBooking carpoolBooking = new CarpoolBooking() {
                UserId = booking.UserId,
                BookingId = booking.BookingId,
                BookingStatusId = (BookingStatusCode)booking.BookingStatusId,
                StartDate = booking.StartTs,
                EndDate = booking.EndTs,
                VehicleId = booking.VehicleId,
                RequestClientDriverId = booking.RequestClientDriverId
            };
            bookingsBase.Add(carpoolBooking);
        }
        return bookingsBase;
    }
}