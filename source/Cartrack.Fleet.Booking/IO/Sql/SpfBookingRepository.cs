﻿using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Minio;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class SpfBookingRepository(
    Common.AppSettings appSettings,
    AppTfmsCustomDbContext tfmsCustomDbContext, 
    AppFleetDbContext fleetDbContext, 
    AppCtDbContext ctDbContext,
    AppPoolDbContext poolDbContext, 
    IMinioClient minioClient, 
    IBookingFilterService filterService,
    IHttpContextAccessor context,
    Func<string, IVehicleBookingBuilder> bookingBuilder,
    ILogger<SpfBookingRepository> logger) 
    : BookingRepository(appSettings, tfmsCustomDbContext, fleetDbContext, ctDbContext, poolDbContext, minioClient, filterService, context, bookingBuilder, logger) {
    
}