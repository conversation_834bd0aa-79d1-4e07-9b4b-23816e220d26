﻿using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.Domain;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using Constants = Cartrack.Fleet.Booking.Domain.Common.Constants;

namespace Cartrack.Fleet.Booking.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class BookingRuleRepository(AppSettings settings, AppPoolDbContext poolDbContext)
    : IBookingRuleRepository {
    private const string BookingActivationCodeKeyCollection = "keyCollection";
    private const string BookingActivationCodeGeofence = "geofence";
    private const string BookingActivationCodeChecklist = "checklist";

    private record BookingRulesAndSettings(List<UserBookingRuleDto> BookingRules, BookingSettings BookingSettings);
    private static readonly ConcurrentCache<long, BookingRulesAndSettings?> CachedPermissions = new();

    public async Task<(List<UserBookingRuleDto>, BookingSettings)> GetBookingSettingRules(long userId) {
        var ruleAndSettings = await CachedPermissions.GetOrAddAsync(userId, 
            async _ => await GetBookingSettingRulesInternal(),
            expiry: settings.CacheExpiry,
            keepAlive: true);
        return (ruleAndSettings!.BookingRules, ruleAndSettings.BookingSettings);

        async Task<BookingRulesAndSettings> GetBookingSettingRulesInternal() {
            var bookingRules = await poolDbContext.UserBookingRules
                .Where(b => b.UserId == userId && b.Status == true)
                .Select(b => new UserBookingRuleDto(
                    b.BookingRuleId,
                    b.Value ?? "",
                    b.Unit ?? "",
                    b.Status ?? false
                )).ToListAsync();

            BookingSettings bookingSettings = new BookingSettings {
                IsAutoApprovalEnabled = null,
                BookInAdvanceBy = default,
                MaximumBookingTime = default,
                CheckDriverSpecialLicense = null,
                CheckDriverLicenseClass = null,
                IsKeyCollectionEnabled = null,
                ActivationType = null,
                IsMultiLevelApprovalEnabled = null,
                IsDriverRequired = null,
                PreDriveChecklistAvailableBy = default
            };

            foreach (var rule in bookingRules) {
                TimeSpan timeSpan = TimeSpan.MinValue;
                switch (rule.BookingRuleId) {
                    case Constants.BookingRuleCodeBookInAdvanceBy:
                    case Constants.BookingRuleCodeMaximumBookingTime:
                    case Constants.BookingRuleCodePreDriveChecklistAvailableBy:
                        if (rule.Unit == "hours") {
                            timeSpan = TimeSpan.FromHours(double.Parse(rule.Value));
                        }
                        else if (rule.Unit == "days") {
                            timeSpan = TimeSpan.FromDays(double.Parse(rule.Value));
                        }
                        else if (rule.Unit == "minutes") {
                            timeSpan = TimeSpan.FromMinutes(double.Parse(rule.Value));
                        }
                        else if (rule.Unit == "months") {
                            //timeSpan = TimeSpan.FromDays(double.Parse(rule.Value));
                        }

                        break;
                }

                switch (rule.BookingRuleId) {
                    case Constants.BookingRuleCodeIsAutoApprovalEnabled:
                        bookingSettings.IsAutoApprovalEnabled = rule.Status; break;
                    case Constants.BookingRuleCodeBookInAdvanceBy: bookingSettings.BookInAdvanceBy = timeSpan; break;
                    case Constants.BookingRuleCodeMaximumBookingTime: bookingSettings.MaximumBookingTime = timeSpan; break;
                    case Constants.BookingRuleCodeCheckDriverSpecialLicense:
                        bookingSettings.CheckDriverSpecialLicense = rule.Status; break;
                    case Constants.BookingRuleCodeCheckDriverLicenseClass:
                        bookingSettings.CheckDriverLicenseClass = rule.Status; break;
                    case Constants.BookingRuleCodeIsKeyCollectionEnabled:
                        bookingSettings.IsKeyCollectionEnabled = rule.Status; break;
                    case Constants.BookingRuleCodeActivationType:
                        bookingSettings.ActivationType = rule.Value.ToLower(); break;
                    case Constants.BookingRuleCodeIsMultiLevelApprovalEnabled:
                        bookingSettings.IsMultiLevelApprovalEnabled = rule.Status; break;
                    case Constants.BookingRuleCodeIsDriverRequired: bookingSettings.IsDriverRequired = rule.Status; break;
                    case Constants.BookingRuleCodePreDriveChecklistAvailableBy:
                        bookingSettings.PreDriveChecklistAvailableBy = timeSpan;
                        break;
                }
            }

            return new BookingRulesAndSettings(bookingRules, bookingSettings);
        }
    }
}

public record UserBookingRuleDto(long BookingRuleId, string Value, string Unit, bool Status);