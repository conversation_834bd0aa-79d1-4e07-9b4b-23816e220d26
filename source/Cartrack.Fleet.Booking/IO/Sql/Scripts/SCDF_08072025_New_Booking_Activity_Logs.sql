
CREATE TABLE IF NOT EXISTS tfms_custom.booking_activity_logs (
    id serial8 PRIMARY KEY,
    user_id BIGINT,
    booking_id BIGINT,
    booking_status_id_from BIGINT,
    booking_status_id_to BIGINT,
    audit_id_reference_from BIGINT,
    audit_id_reference_to BIGINT,
    audit_username VARCHAR,
    audit_ts timestamptz,
    created_ts timestamptz DEFAULT NOW() NOT NULL,
    processed_ts timestamptz DEFAULT NOW() NOT NULL,
    jsondata TEXT,
    CONSTRAINT unique_booking_activity_logs_combination_idx 
        UNIQUE (user_id,booking_id,booking_status_id_from,booking_status_id_to, audit_id_reference_from,audit_id_reference_to)
);


