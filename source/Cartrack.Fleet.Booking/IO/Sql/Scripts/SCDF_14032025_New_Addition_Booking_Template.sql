CREATE TABLE IF NOT EXISTS pool.booking_journey (
    id serial4 NOT NULL,
    user_id int8 NULL,
    booking_id int8 NULL,
    booking_journey_type_varcode varchar(255) NULL,
    "location" varchar NULL,
    start_ts timestamptz NULL,
    end_ts timestamptz NULL,
    "order" int4 NULL,
    journey_location_type varchar NULL,
    journey_location_id varchar NULL, -- depends on journey_location_type. poi --> poi_id, location --> site_location_id. freetext, --> save into poi table
    booking_audit_reference varchar NULL, -- this is only to refer to pool.booking for audit purpose in au$
	"au$counter" int4 DEFAULT 1 NULL,
    CONSTRAINT booking_journey_pkey PRIMARY KEY (id)
);
COMMENT ON COLUMN pool.booking_journey.journey_location_id IS 'depends on journey_location_type. poi --> poi_id, location --> site_location_id. freetext --> save into poi table';
COMMENT ON COLUMN pool.booking_journey.booking_audit_reference IS 'this is only to refer to pool.booking for audit purpose in au$';
        
CREATE TABLE IF NOT EXISTS pool.booking_journey_type (
    id serial4 primary key,
    user_id INT,
    variable_code varchar(255) not NULL,
    name VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN default false not NULL
);


CREATE TABLE IF NOT EXISTS pool.accessory (
    id serial4 primary key,
    user_id INT,
    accessory_type_id INT,
    name VARCHAR(255),
    description TEXT,
    "au$counter" int4 DEFAULT 1 NULL,
    is_deleted BOOLEAN default false not NULL,
    CONSTRAINT accessory_pkey PRIMARY KEY (id),
    CONSTRAINT accessory_accessory_type_id_fkey FOREIGN KEY (accessory_type_id) REFERENCES pool.accessory_type(id) ON DELETE SET NULL
);


CREATE TABLE IF NOT EXISTS pool.additional_locations (
    id bigserial primary key,
    location_name VARCHAR,
    description TEXT,
    is_deleted BOOLEAN default false not NULL,
    created_ts timestamptz DEFAULT NOW() NOT NULL,
    updated_ts timestamptz DEFAULT NOW() NOT NULL,
    "au$counter" int4 DEFAULT 1 NULL,
	CONSTRAINT additional_locations_pkey PRIMARY KEY (id)
);


CREATE TABLE IF NOT EXISTS pool.accessory_type (
    id serial4 primary key,
    user_id INT,
    name VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN default false not NULL,
    CONSTRAINT accessory_type_pkey PRIMARY KEY (id)
);


CREATE TABLE IF NOT EXISTS pool.booking_accessories (
    id serial4 PRIMARY KEY,
    booking_id BIGINT,
    accessory_id INT,
    is_deleted BOOLEAN default false not null,
    booking_audit_reference varchar NULL, -- this is only to refer to pool.booking for audit purpose in au$
    "au$counter" int4 DEFAULT 1 NULL,
	CONSTRAINT booking_accessories_pkey PRIMARY KEY (id),
    CONSTRAINT booking_accessories_accessory_id_fkey FOREIGN KEY (accessory_id) REFERENCES pool.accessory(id) ON DELETE CASCADE ON UPDATE CASCADE
);
COMMENT ON COLUMN pool.booking_accessories.booking_audit_reference IS 'this is only to refer to pool.booking for audit purpose in au$';

CREATE TABLE IF NOT EXISTS pool.booking_journey (
    id serial4 PRIMARY KEY,
    user_id INT,
    booking_id INT,
    booking_journey_type_varcode VARCHAR(255),
    location VARCHAR,
    start_ts timestamptz NOT NULL,
    end_ts timestamptz NOT NULL
    booking_audit_reference varchar NULL, -- this is only to refer to pool.booking for audit purpose in au$
    "au$counter" int4 DEFAULT 1 NULL,
	CONSTRAINT booking_journey_pkey PRIMARY KEY (id)
);
COMMENT ON COLUMN pool.booking_journey.booking_audit_reference IS 'this is only to refer to pool.booking for audit purpose in au$';

CREATE TABLE IF NOT EXISTS pool.booking_silent_hours (
    id serial4 PRIMARY KEY,
    user_id INT,
    start_ts timestamptz,
    end_ts timestamptz,
    every_mon BOOLEAN default FALSE NOT NULL,
    every_tue BOOLEAN default FALSE NOT NULL,
    every_wed BOOLEAN default FALSE NOT NULL,
    every_thu BOOLEAN default FALSE NOT NULL,
    every_fri BOOLEAN default FALSE NOT NULL,
    every_sat BOOLEAN default FALSE NOT NULL,
    every_sun BOOLEAN default FALSE NOT NULL,
    non_working_day timestamptz,
    "au$counter" int4 DEFAULT 1 NULL,
	CONSTRAINT booking_silent_hours_pkey PRIMARY KEY (id)
);


CREATE TABLE IF NOT EXISTS pool.booking_attachments (
    id serial4 PRIMARY KEY,
    booking_id BIGINT,
    url_attachment TEXT,
    content_type VARCHAR,
    filename VARCHAR NULL,
    booking_audit_reference varchar NULL, -- this is only to refer to pool.booking for audit purpose in au$
    "au$counter" int4 DEFAULT 1 NULL,
	CONSTRAINT booking_attachments_pkey PRIMARY KEY (id)
);
COMMENT ON COLUMN pool.booking_attachments.filename IS 'only filename text';
COMMENT ON COLUMN pool.booking_attachments.booking_audit_reference IS 'this is only to refer to pool.booking for audit purpose in au$';

        
CREATE TABLE IF NOT EXISTS pool.booking_force_terminate_reason (
    booking_force_terminate_reason_id int4 DEFAULT nextval('pool.booking_force_terminate_reaso_booking_force_terminate_reaso_seq'::regclass) NOT NULL,
    user_id int8 NOT NULL,
    booking_force_terminate_reason VARCHAR(256) NULL,
    is_deleted bool DEFAULT false NOT NULL,
    "au$counter" int4 DEFAULT 1 NULL,
    CONSTRAINT booking_force_terminate_reason_pkey PRIMARY KEY (booking_force_terminate_reason_id)
);
CREATE INDEX booking_force_terminate_reason_user_id_idx ON pool.booking_force_terminate_reason USING btree (user_id);


CREATE TABLE IF NOT EXISTS pool.booking_reject_reason (
    booking_reject_reason_id serial4 NOT NULL,
    user_id int8 NOT NULL,
    booking_reject_reason varchar(256) NULL,
    is_deleted bool DEFAULT false NOT NULL,
    "au$counter" int4 DEFAULT 1 NULL,
    CONSTRAINT booking_reject_reason_pkey PRIMARY KEY (booking_reject_reason_id)
);
CREATE INDEX booking_reject_reason_user_id_idx ON pool.booking_reject_reason USING btree (user_id);


ALTER TABLE pool.booking ADD COLUMN remarks VARCHAR;
ALTER TABLE pool.booking ADD COLUMN updated_ts timestamptz NULL;
ALTER TABLE pool.booking ADD COLUMN booking_reject_reason_id int8 NULL;
ALTER TABLE pool.booking ADD COLUMN booking_reject_notes VARCHAR NULL;
ALTER TABLE pool.booking ADD COLUMN booking_force_terminate_reason_id int8 NULL;
ALTER TABLE pool.booking ADD COLUMN booking_force_terminate_notes VARCHAR NULL;
ALTER TABLE pool.au$booking ADD COLUMN updated_ts timestamptz NULL;
ALTER TABLE pool.au$booking ADD COLUMN booking_reject_reason_id int8 NULL;
ALTER TABLE pool.au$booking ADD COLUMN booking_reject_notes VARCHAR NULL;
ALTER TABLE pool.au$booking ADD COLUMN booking_force_terminate_reason_id int8 NULL;
ALTER TABLE pool.au$booking ADD COLUMN booking_force_terminate_notes VARCHAR NULL;
ALTER TABLE pool.au$booking ADD COLUMN remarks VARCHAR;

-- 14 July 2025 -- adding extra column in Booking
ALTER table pool.booking ADD activated_ts timestamptz null;
ALTER table pool.booking ADD completed_ts timestamptz null;
ALTER table pool.booking ADD activated_client_user_id fleet."ct_uuid" null;
ALTER table pool.booking ADD completed_client_user_id fleet."ct_uuid" null;
ALTER table pool.booking ADD booking_audit_reference varchar null;
ALTER table pool.au$booking ADD activated_ts timestamptz null;
ALTER table pool.au$booking ADD completed_ts timestamptz null;
ALTER table pool.au$booking ADD activated_client_user_id fleet."ct_uuid" null;
ALTER table pool.au$booking ADD completed_client_user_id fleet."ct_uuid" null;
ALTER table pool.au$booking ADD booking_audit_reference varchar null;
ALTER TABLE pool."booking_cancel_reason" ADD internal_description varchar NULL;
ALTER TABLE pool."au$booking_cancel_reason" ADD internal_description varchar NULL;


-----------------------------------
-- This must run after pool.booking update to apply rule into partition table
-- when running this, it will auto apply the rule with newest column in pool.bookings
-----------------------------------
DO $$
DECLARE
v_current_ts timestamptz := '2022-07-01 00:00:00+08';
    v_end_ts timestamptz := '2040-01-01 00:00:00+08';
    v_year_month text;
BEGIN
    -- Loop through each month from v_current_ts to v_end_ts
    WHILE v_current_ts <= v_end_ts LOOP
        -- Format the year and month as YYYYMM
        v_year_month := to_char(v_current_ts, 'YYYYMM');

        -- Construct and execute the CREATE RULE statement
EXECUTE format(
        'CREATE OR REPLACE RULE rule_booking_%s AS
        ON INSERT TO pool.booking
        WHERE (new.start_ts >= %L AND new.start_ts < %L)
        DO INSTEAD INSERT INTO pool.booking_%s SELECT new.*;',
        v_year_month,
        v_current_ts,
        v_current_ts + INTERVAL '1 month',
        v_year_month
        );

-- Log the applied partition
RAISE NOTICE 'Created/Updated rule to partition: pool.booking_%', v_year_month;

        -- Move to the next month
        v_current_ts := v_current_ts + INTERVAL '1 month';
END LOOP;
END $$;

