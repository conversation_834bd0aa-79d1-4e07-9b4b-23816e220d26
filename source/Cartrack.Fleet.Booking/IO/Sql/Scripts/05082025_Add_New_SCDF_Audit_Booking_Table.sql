CREATE TABLE pool."au$accessory" (
	"au$audit_id" fleet."key_id" DEFAULT nextval('fleet.unique_audit_id'::regclass) NOT NULL,
	id int4 NULL,
	user_id int4 NULL,
	accessory_type_id int4 NULL,
	"name" varchar(255) NULL,
	description text NULL,
	is_deleted bool NULL,
	"au$action_type" bpchar(1) NULL,
	"au$ts" timestamptz DEFAULT now() NULL,
	"au$username" varchar(128) NULL,
	"au$application" varchar(50) NULL,
	"au$counter" int4 NULL,
	CONSTRAINT "pk_au$accessory" PRIMARY KEY ("au$audit_id")
);

CREATE TABLE pool."au$additional_locations" (
	"au$audit_id" fleet."key_id" DEFAULT nextval('fleet.unique_audit_id'::regclass) NOT NULL,
	id int8 NULL,
	location_name varchar NULL,
	description text NULL,
	is_deleted bool NULL,
	created_ts timestamptz NULL,
	updated_ts timestamptz NULL,
	"au$action_type" bpchar(1) NULL,
	"au$ts" timestamptz DEFAULT now() NULL,
	"au$username" varchar(128) NULL,
	"au$application" varchar(50) NULL,
	"au$counter" int4 NULL,
	CONSTRAINT "pk_au$additional_locations" PRIMARY KEY ("au$audit_id")
);

CREATE TABLE pool."au$booking_accessories" (
	"au$audit_id" fleet."key_id" DEFAULT nextval('fleet.unique_audit_id'::regclass) NOT NULL,
	id int4 NULL,
	booking_id int8 NULL,
	accessory_id int4 NULL,
	is_deleted bool NULL,
	booking_audit_reference varchar NULL,
	"au$action_type" bpchar(1) NULL,
	"au$ts" timestamptz DEFAULT now() NULL,
	"au$username" varchar(128) NULL,
	"au$application" varchar(50) NULL,
	"au$counter" int4 NULL,
	CONSTRAINT "pk_au$booking_accessories" PRIMARY KEY ("au$audit_id")
);

CREATE TABLE pool."au$booking_attachments" (
	"au$audit_id" fleet."key_id" DEFAULT nextval('fleet.unique_audit_id'::regclass) NOT NULL,
	id int4 NULL,
	booking_id int8 NULL,
	url_attachment text NULL,
	content_type varchar NULL,
	filename varchar NULL,
	booking_audit_reference varchar NULL,
	"au$action_type" bpchar(1) NULL,
	"au$ts" timestamptz DEFAULT now() NULL,
	"au$username" varchar(128) NULL,
	"au$application" varchar(50) NULL,
	"au$counter" int4 NULL,
	CONSTRAINT "pk_au$booking_attachments" PRIMARY KEY ("au$audit_id")
);

CREATE TABLE pool."au$booking_cancel_reason" (
	"au$audit_id" fleet."key_id" DEFAULT nextval('fleet.unique_audit_id'::regclass) NOT NULL,
	booking_cancel_reason_id int8 NULL,
	user_id int8 NULL,
	booking_cancel_reason varchar(256) NULL,
	is_deleted bool NULL,
	"au$action_type" bpchar(1) NULL,
	"au$ts" timestamptz DEFAULT now() NULL,
	"au$username" varchar(128) NULL,
	"au$application" varchar(50) NULL,
	"au$counter" int4 NULL,
	internal_description varchar NULL,
	CONSTRAINT "pk_au$booking_cancel_reason" PRIMARY KEY ("au$audit_id")
);

CREATE TABLE pool."au$booking_force_terminate_reason" (
	"au$audit_id" fleet."key_id" DEFAULT nextval('fleet.unique_audit_id'::regclass) NOT NULL,
	booking_force_terminate_reason_id int4 NULL,
	user_id int8 NULL,
	booking_force_terminate_reason varchar(256) NULL,
	is_deleted bool NULL,
	"au$action_type" bpchar(1) NULL,
	"au$ts" timestamptz DEFAULT now() NULL,
	"au$username" varchar(128) NULL,
	"au$application" varchar(50) NULL,
	"au$counter" int4 NULL,
	CONSTRAINT "pk_au$booking_force_terminate_reason" PRIMARY KEY ("au$audit_id")
);

CREATE TABLE pool."au$booking_journey" (
	"au$audit_id" fleet."key_id" DEFAULT nextval('fleet.unique_audit_id'::regclass) NOT NULL,
	id int4 NULL,
	user_id int8 NULL,
	booking_id int8 NULL,
	booking_journey_type_varcode varchar(255) NULL,
	"location" varchar NULL,
	start_ts timestamptz NULL,
	end_ts timestamptz NULL,
	"order" int4 NULL,
	journey_location_type varchar NULL,
	journey_location_id varchar NULL,
	booking_audit_reference varchar NULL,
	"au$action_type" bpchar(1) NULL,
	"au$ts" timestamptz DEFAULT now() NULL,
	"au$username" varchar(128) NULL,
	"au$application" varchar(50) NULL,
	"au$counter" int4 NULL,
	CONSTRAINT "pk_au$booking_journey" PRIMARY KEY ("au$audit_id")
);

CREATE TABLE pool."au$booking_reject_reason" (
	"au$audit_id" fleet."key_id" DEFAULT nextval('fleet.unique_audit_id'::regclass) NOT NULL,
	booking_reject_reason_id int4 NULL,
	user_id int8 NULL,
	booking_reject_reason varchar(256) NULL,
	is_deleted bool NULL,
	"au$action_type" bpchar(1) NULL,
	"au$ts" timestamptz DEFAULT now() NULL,
	"au$username" varchar(128) NULL,
	"au$application" varchar(50) NULL,
	"au$counter" int4 NULL,
	CONSTRAINT "pk_au$booking_reject_reason" PRIMARY KEY ("au$audit_id")
);

CREATE TABLE pool."au$booking_silent_hours" (
	"au$audit_id" fleet."key_id" DEFAULT nextval('fleet.unique_audit_id'::regclass) NOT NULL,
	id int4 NULL,
	user_id int4 NULL,
	start_ts timestamptz NULL,
	end_ts timestamptz NULL,
	every_mon bool NULL,
	every_tue bool NULL,
	every_wed bool NULL,
	every_thu bool NULL,
	every_fri bool NULL,
	every_sat bool NULL,
	every_sun bool NULL,
	non_working_day timestamptz NULL,
	"au$action_type" bpchar(1) NULL,
	"au$ts" timestamptz DEFAULT now() NULL,
	"au$username" varchar(128) NULL,
	"au$application" varchar(50) NULL,
	"au$counter" int4 NULL,
	CONSTRAINT "pk_au$booking_silent_hours" PRIMARY KEY ("au$audit_id")
);