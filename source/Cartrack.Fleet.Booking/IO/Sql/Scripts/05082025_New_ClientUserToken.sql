CREATE TABLE IF NOT EXISTS fleet.client_user_token (
    id int8 GENERATED ALWAYS AS IDENTITY( INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE) NOT NULL,
    "token" varchar NOT NULL,
    expires_ts timestamptz NOT NULL,
    created_ts timestamptz NOT NULL,
    revoked_ts timestamptz NULL,
    replaced_by_token varchar NULL,
    client_user_id fleet."uuid" NULL,
    user_id int8 NOT NULL,
    CONSTRAINT client_user_token_pk PRIMARY KEY (id),
    CONSTRAINT client_user_token_client_user_fk FOREIGN KEY (client_user_id) REFERENCES fleet.client_user(client_user_id)
);