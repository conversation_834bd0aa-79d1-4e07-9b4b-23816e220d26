CREATE TABLE tfms_custom.booking_activity_logs (
    id bigserial NOT NULL,
    user_id int8 NULL,
    booking_id int8 NULL,
    booking_status_id_from int8 NULL,
    booking_status_id_to int8 NULL,
    audit_id_reference_from int8 NULL,
    audit_id_reference_to int8 NULL,
    audit_username varchar NULL,
    audit_ts timestamptz NULL,
    created_ts timestamptz DEFAULT now() NOT NULL,
    processed_ts timestamptz DEFAULT now() NOT NULL,
    jsondata text NULL,
    CONSTRAINT booking_activity_logs_pkey PRIMARY KEY (id),
    CONSTRAINT unique_booking_activity_logs_combination_idx UNIQUE (user_id, booking_id, booking_status_id_from, booking_status_id_to, audit_id_reference_from, audit_id_reference_to)
);

-- 14 july 2025 -- extra info client driver info
CREATE TABLE IF NOT EXISTS tfms_custom.client_driver_license_additional_info (
    id serial4 NOT NULL,
    client_driver_id fleet."uuid" NOT NULL,
    driver_license_type_id int8 NOT NULL,
    suspension_start_date date NULL,
    suspension_end_date date NULL,
    disqualification_start_date date NULL,
    disqualification_end_date date NULL,
    revocation_start_date date NULL,
    revocation_end_date date NULL,
    is_deleted bool DEFAULT false NOT NULL,
    CONSTRAINT client_driver_license_additional_info_pkey PRIMARY KEY (id),
    CONSTRAINT client_driver_license_additional_info_client_driver_id_fkey FOREIGN KEY (client_driver_id) REFERENCES fleet.client_driver(client_driver_id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT client_driver_license_additional_info_driver_license_type_id_fk FOREIGN KEY (driver_license_type_id) REFERENCES fleet.driver_license_type(driver_license_type_id) ON DELETE CASCADE ON UPDATE CASCADE
);

-- 14 july -- create scdf booking additional info
CREATE TABLE IF NOT EXISTS tfms_custom.scdf_booking_additional_info (
    id serial4 NOT NULL,
    booking_id int8 NULL,
    passenger_count int4 NULL,
    vehicle_commander_client_user_id fleet."ct_uuid" NULL,
    requested_for_client_user_id fleet."ct_uuid" NULL,
    location_type int4 NULL,
    driver_type int4 NULL,
    vehicle_commander_type int4 NULL,
    equipment_type int4 NULL,
    returned_vehicle_commander_client_user_id fleet."ct_uuid" NULL,
    "au$counter" int4 DEFAULT 1 NULL,
    booking_audit_reference varchar NULL, -- this is only to refer to pool.booking for audit purpose in au$
    CONSTRAINT scdf_booking_additional_info_pkey PRIMARY KEY (id)
);
COMMENT ON COLUMN tfms_custom.scdf_booking_additional_info.booking_audit_reference IS 'this is only to refer to pool.booking for audit purpose in au$';
