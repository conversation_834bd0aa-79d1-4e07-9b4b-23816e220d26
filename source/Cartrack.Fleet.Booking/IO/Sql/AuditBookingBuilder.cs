﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.States;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;
using AdditionalLocation = Cartrack.EFCore.Models.Pool.AdditionalLocation;
using BookingApproval = Cartrack.EFCore.Models.Pool.BookingApproval;
using VehicleBooking = Cartrack.Fleet.Booking.Domain.VehicleBooking;

namespace Cartrack.Fleet.Booking.IO.Sql;

public class AuditBookingActivityBuilder(
    AppPoolDbContext poolDbContext,
    AppTfmsCustomDbContext tfmsCustomDbContext,
    AppFleetDbContext fleetDbContext,
    AppCtDbContext ctDbContext,
    AuditDataSetup auditDataSetup,
    ScdfAuBookings auPreviousBooking,
    ScdfAuBookings auCurrentBooking) {
    private record ClientUserRecord(string ClientUserId, string UserName);
    
    private AuditDataSetup _auditDataSetup = auditDataSetup;
    private ScdfAuBookings _auCurrentBooking = auCurrentBooking;
    private ScdfAuBookings _prevAuBooking = auPreviousBooking;
    private readonly AppPoolDbContext _poolDbContext = poolDbContext;
    private readonly AppTfmsCustomDbContext _tfmsCustomDbContext = tfmsCustomDbContext;
    private readonly AppFleetDbContext _fleetDbContext = fleetDbContext;
    
    private readonly string editModeText = "EDIT";
    private readonly Dictionary<string, string> editModeColumnCompareBookingsTable = new Dictionary<string, string> {
        {"VehicleId", "Registration"},
        {"BookingStatusId", "Status"},
        {"BookingVehicleTypeId", "Vehicle Category"},
        {"BookingPurposeId", "Booking Purpose"},
        {"BookingPurposeDescription", "Purpose Description"},
        {"BookingCancelReasonId","Cancel Reason"},
        {"BookingCancelNotes","Cancel Notes"},
        {"PickupSiteLocationId", "Pickup Location"},
        {"ReturnSiteLocationId", "Return Location"},
        {"StartTs","Start Date"},
        {"EndTs","End Date"},
        {"RequestClientDriverId","Driver"},
        {"RequestClientUserId","Requestor Account"},
        {"DecisionClientUserId", "Decision made by"},
        {"CanceledClientDriverId","Canceled by Driver"},
        {"PickupIgnitionTs","Actual Pickup Time"},
        {"PickupClientDriverId","Pickup Driver by"},
        {"ReturnedIgnitionTs","Returned Time"},
        {"ReturnedClientDriverId","Returned Driver by"},
        {"ReturnedClientUserId","Change Return status by"},
        {"BookingRejectReasonId","Reject Reason"},
        {"BookingRejectNotes","Reject Notes"},
        {"BookingForceTerminateReasonId","Force Terminate Reason"},
        {"BookingForceTerminateNotes","Force Terminate Notes"},
        {"ActivatedTs","Activated Time"},
        {"CompletedTs","End Time"},
        {"ActivatedClientUserId","Change Activated status by"},
        {"CompletedClientUserId","Change End Status by"},
        {"Remarks","Remarks"}
    };

    private readonly Dictionary<string, string> editModeColumnCompareScdfAdditionalBookingInfoTable = new Dictionary<string, string> {
        {"PassengerCount", "Number of Passenger"}, 
        {"VehicleCommanderClientUserId", "Vehicle Commander"}, 
        {"RequestedForClientUserId", "Requested For"}, 
        {"LocationType", "Location Type"}, 
        {"DriverType", "Driver Type"},
        {"VehicleCommanderType", "Vehicle Commander Type"}, 
        {"EquipmentType", "Equipment Type"}
    };

    private ScdfBookingAdditionalInfo? _additionalInfo;
    
    public ActivityLogEntry? ActivityLogEntry { get; private set; }

    public async Task Start() {
        var actionLog = new ActivityLogAction { StatusId = "N/A" };
        this.ActivityLogEntry = new ActivityLogEntry {
            
        };
        
        var driverFullName = this.IncludeDriverName(this._auCurrentBooking.AuBooking.RequestClientDriverId);
        var vehicleRegistration =
            this._auditDataSetup.Vehicles?.Find(a => a.VehicleId == this._auCurrentBooking.AuBooking.VehicleId)
                ?.Registration ?? string.Empty;
        var vehicleCommanderWogId = this._auditDataSetup.Users?.Find(a =>
                a.ClientUserId.Equals(this._auCurrentBooking.AuScdfBookingAdditionalInfo?.VehicleCommanderClientUserId))
            ?.UserName ?? string.Empty;
            
        switch ( (BookingStatusCode) this._auCurrentBooking.AuBooking.BookingStatusId!) {
            case BookingStatusCode.Requested:
                actionLog = new ActivityLogAction { StatusId = ((int)BookingStatusCode.Requested).ToString(), StatusName = nameof(BookingStatusCode.Requested), };
                break;
            case BookingStatusCode.Approved:
                var auCurrentTime = this._auCurrentBooking.AuBooking.AuTs;

                actionLog = new ActivityLogAction {
                    StatusId = ((int)BookingStatusCode.Approved).ToString(), 
                    StatusName = nameof(BookingStatusCode.Approved), 
                    AssignedDriver = this._auCurrentBooking.AuBooking.RequestClientDriverId, 
                    AssignedVehicleCommander = vehicleCommanderWogId,
                    Changes = await this.CompareAuditBookingData(this._prevAuBooking, this._auCurrentBooking)
                };
                break;
            case BookingStatusCode.Rejected:
                var bookingRejectReasonName = this._auditDataSetup.RejectReason?.Find(a => a.BookingRejectReasonId == this._auCurrentBooking.AuBooking.BookingRejectReasonId)?.BookingRejectReason1 ?? string.Empty;
                var rejectReasonNote = this._auCurrentBooking.AuBooking.BookingRejectNotes;

                actionLog = new ActivityLogAction {
                    StatusId = ((int)BookingStatusCode.Rejected).ToString() , 
                    StatusName = nameof(BookingStatusCode.Rejected), 
                    Reason = bookingRejectReasonName,
                    Remarks = rejectReasonNote,
                };
                break;
            case BookingStatusCode.Cancelled:
                var bookingCancelReasonName = this._auditDataSetup.CancelReason?.Find(a => a.BookingCancelReasonId == this._auCurrentBooking.AuBooking.BookingCancelReasonId)?.BookingCancelReason1 ?? string.Empty;
                var cancelReasonNote = this._auCurrentBooking.AuBooking.BookingCancelNotes;;

                actionLog = new ActivityLogAction {
                    StatusId = ((int)BookingStatusCode.Cancelled).ToString(), 
                    StatusName = nameof(BookingStatusCode.Cancelled), 
                    Reason = bookingCancelReasonName,
                    Remarks = cancelReasonNote
                };
                break;
            case BookingStatusCode.Active:
                
                actionLog = new ActivityLogAction {
                    StatusId = ((int)BookingStatusCode.Active).ToString() ,
                    StatusName = nameof(BookingStatusCode.Active), 
                    PickupDateTime = this._auCurrentBooking.AuBooking.StartTs,
                    ActualPickupDateTime = this._auCurrentBooking.AuBooking.PickupIgnitionTs,
                    VehicleRegistration = vehicleRegistration,
                    AssignedDriver = driverFullName,
                    AssignedVehicleCommander = vehicleCommanderWogId,
                    Changes = await this.CompareAuditBookingData(this._prevAuBooking, this._auCurrentBooking)
                };
                break;
            case BookingStatusCode.ActiveLate:
                actionLog = new ActivityLogAction { 
                    StatusId = ((int)BookingStatusCode.ActiveLate).ToString(), 
                    StatusName = nameof(BookingStatusCode.ActiveLate), 
                    ActualPickupDateTime = this._auCurrentBooking.AuBooking.AuTs,
                };
                break;
            case BookingStatusCode.Returned:
                actionLog = new ActivityLogAction {
                    StatusId = ((int)BookingStatusCode.Returned).ToString(), 
                    StatusName = nameof(BookingStatusCode.Returned), 
                    ActualDropoffDateTime = this._auCurrentBooking.AuBooking.ReturnedIgnitionTs,
                    VehicleRegistration = vehicleRegistration,
                    AssignedDriver = driverFullName,
                    AssignedVehicleCommander = vehicleCommanderWogId,
                    Changes = await this.CompareAuditBookingData(this._prevAuBooking, this._auCurrentBooking)
                };
                break;
            case BookingStatusCode.ReturnedLate:
                actionLog = new ActivityLogAction {
                    StatusId = ((int)BookingStatusCode.ReturnedLate).ToString(), 
                    StatusName = nameof(BookingStatusCode.ReturnedLate), 
                    DropOffDateTime  = this._auCurrentBooking.AuBooking.EndTs,
                    ActualDropoffDateTime = this._auCurrentBooking.AuBooking.ReturnedIgnitionTs,
                    VehicleRegistration = vehicleRegistration,
                    AssignedDriver = driverFullName,
                    AssignedVehicleCommander = vehicleCommanderWogId,
                    Changes = await this.CompareAuditBookingData(this._prevAuBooking, this._auCurrentBooking)
                };
                break;
            case BookingStatusCode.ForceTerminated:
                var bookingForceTerminateReasonName = this._auditDataSetup.ForceTerminateReason?.Find(a => a.BookingForceTerminateReasonId == this._auCurrentBooking.AuBooking.BookingForceTerminateReasonId)?.BookingForceTerminateReason1 ?? string.Empty;
                var forceTerminateReasonNote = this._auCurrentBooking.AuBooking.BookingForceTerminateNotes;

                actionLog = new ActivityLogAction { 
                    StatusId = ((int)BookingStatusCode.ForceTerminated).ToString() , 
                    StatusName = nameof(BookingStatusCode.ForceTerminated), 
                    Reason = bookingForceTerminateReasonName,
                    Remarks = forceTerminateReasonNote
                };
                break;
        }

        if (this._prevAuBooking.AuBooking.BookingStatusId == this._auCurrentBooking.AuBooking.BookingStatusId) {
            actionLog = new ActivityLogAction {
                StatusId = this.editModeText, StatusName = this.editModeText, Changes = await this.CompareAuditBookingData(this._prevAuBooking, this._auCurrentBooking)
            };
        }

        var clientUserDto = new ClientUser {
            ClientUserId = "--Blank--", 
            Username = this._auCurrentBooking.AuBooking.AuUsername
        };
        
        this.ActivityLogEntry!.AuClientUser = clientUserDto;
        this.ActivityLogEntry!.AuAction = actionLog;
        this.ActivityLogEntry!.AuTimestamp = this._auCurrentBooking.AuBooking.AuTs;

    }

    private async Task<List<ActivityLogDataField>> CompareAuditBookingData(ScdfAuBookings auPreviousBooking, ScdfAuBookings auCurrentBooking) {
        var changes = new List<ActivityLogDataField>();
        foreach (var item in editModeColumnCompareBookingsTable) {
            var previousValue = auPreviousBooking.AuBooking
                .GetType()
                .GetProperty(item.Key)?
                .GetValue(auPreviousBooking.AuBooking)?.ToString();

            var updatedValue = auCurrentBooking.AuBooking
                .GetType()
                .GetProperty(item.Key)?
                .GetValue(auCurrentBooking.AuBooking)?.ToString();

            if (item.Key.Equals("BookingStatusId")) {
                int.TryParse(previousValue, out int resultPreviousValue);
                int.TryParse(updatedValue, out int resultUpdatedValue);
                previousValue = ((BookingStatusCode)resultPreviousValue).ToString();
                updatedValue = ((BookingStatusCode)resultUpdatedValue).ToString();
            }
            
            var pValue = this.TranslateIdToName(item.Key, previousValue ?? "");
            var uValue = this.TranslateIdToName(item.Key, updatedValue ?? "");
                
            if (pValue.Equals(uValue)) continue;

            var data = new ActivityLogDataField {
                Field = item.Value, 
                PreviousValue = pValue, 
                UpdatedValue = uValue,
            };
            changes.Add(data);
        }
            
        // SCDF Booking Additional Info
        // If currentBooking does not have data, it means user never updates this table for particular booking
        foreach (var item in editModeColumnCompareScdfAdditionalBookingInfoTable) {
            var previousValue = (auPreviousBooking?.AuScdfBookingAdditionalInfo)?.GetType()
                .GetProperty(item.Key)?
                .GetValue(auPreviousBooking.AuScdfBookingAdditionalInfo);
                
            var updatedValue = auCurrentBooking.AuScdfBookingAdditionalInfo?.GetType()
                .GetProperty(item.Key)?
                .GetValue(auCurrentBooking.AuScdfBookingAdditionalInfo);
                
            var pValue = this.TranslateIdToName(item.Key, previousValue?.ToString() ?? "");
            var uValue = this.TranslateIdToName(item.Key, updatedValue?.ToString() ?? "");

            if (pValue.Equals(uValue)) continue;

            var data = new ActivityLogDataField { 
                Field = item.Value, PreviousValue = pValue, UpdatedValue = uValue,
            };
            changes.Add(data);
        }

        // SCDF Booking Journey
        if (true) {
            string? pValue = auPreviousBooking?.AuBookingJourneys;
            string? uValue = auCurrentBooking?.AuBookingJourneys;
                
            if (!pValue.Equals(uValue)) {
                var data = new ActivityLogDataField {
                    Field = "Journeys", PreviousValue = pValue, UpdatedValue = uValue,
                };
                changes.Add(data);
            }
        }
            
        // SCDF Booking Attachment
        if (true) {
            string? pValue = auPreviousBooking?.AuBookingAttachments;
            string? uValue = auCurrentBooking?.AuBookingAttachments;
                
            if (!pValue.Equals(uValue)) {
                var data = new ActivityLogDataField {
                    Field = "Attachment", PreviousValue = pValue, UpdatedValue = uValue,
                };
                changes.Add(data);
            }
        }
            
        // SCDF Booking Accessories
        if (true) {
            string? pValue = auPreviousBooking?.AuBookingAccessories;
            string? uValue = auCurrentBooking?.AuBookingAccessories;
                
            if (!pValue.Equals(uValue)) {
                var data = new ActivityLogDataField { 
                    Field = "Accessories", PreviousValue = pValue, UpdatedValue = uValue,
                };
                changes.Add(data);
            }
        }
        return changes;
    }

    private string TranslateIdToName(string key, string value) {
        if (string.IsNullOrEmpty(value)) return value;
       
        switch (key) {
            case "VehicleId":
                return this._auditDataSetup.Vehicles?.Find(a => a.VehicleId == long.Parse(value))?.Registration ?? string.Empty;
                break;
            case "BookingVehicleTypeId":
                return this._auditDataSetup.BookingVehicleTypes?.Find(a => a.BookingVehicleTypeId == long.Parse(value))?.BookingVehicleType1 ?? string.Empty;
                break;
            case "BookingPurposeId":
                return this._auditDataSetup.BookingPurpose?.Find(a => a.BookingPurposeId == long.Parse(value))?.BookingPurpose1 ?? string.Empty;
                break;
            case "BookingCancelReasonId":
                return this._auditDataSetup.CancelReason?.Find(a => a.BookingCancelReasonId == long.Parse(value))?.BookingCancelReason1 ?? string.Empty;
                break;
            case "BookingRejectReasonId":
                return this._auditDataSetup.RejectReason?.Find(a => a.BookingRejectReasonId == long.Parse(value))?.BookingRejectReason1 ?? string.Empty;
                break;
            case "BookingForceTerminateReasonId":
                return this._auditDataSetup.ForceTerminateReason?.Find(a => a.BookingForceTerminateReasonId == long.Parse(value))?.BookingForceTerminateReason1 ?? string.Empty;
                break;
            case "PickupSiteLocationId":
            case "ReturnSiteLocationId":
                return this._auditDataSetup.SiteLocations?.Find(a => a.SiteLocationId == long.Parse(value))?.SiteLocationName ?? string.Empty;
                break;
            case "RequestClientDriverId":
            case "CanceledClientDriverId":
            case "PickupClientDriverId":
            case "ReturnedClientDriverId":
                return this.IncludeDriverName(value);
                break;
            case "RequestClientUserId":
            case "DecisionClientUserId":
            case "ReturnedClientUserId":
            case "ActivatedClientUserId":
            case "CompletedClientUserId":
            case "VehicleCommanderClientUserId":
            case "RequestedForClientUserId":    
                return this._auditDataSetup.Users?.Find(a => a.ClientUserId.Equals(value))?.UserName ?? string.Empty;
                break;
            case "DriverType":
                var driverTypeName = Regex.Replace(Enum.GetName(typeof(DriverType), long.Parse(value)) ?? "", "(\\B[A-Z])", " $1");
                return driverTypeName;
                break;
            case "VehicleCommanderType":
                string vehicleCommmanderTypeName  = Regex.Replace(Enum.GetName(typeof(VehicleCommanderType), long.Parse(value)) ?? "", "(\\B[A-Z])", " $1");
                return vehicleCommmanderTypeName;
                break;
            case "EquipmentType":
                string equipmentTypeName  = Regex.Replace(Enum.GetName(typeof(EquipmentType), long.Parse(value)) ?? "", "(\\B[A-Z])", " $1");
                return equipmentTypeName;
                break;
            case "LocationType" :
                string locationTypeName  = Regex.Replace(Enum.GetName(typeof(JourneyType), long.Parse(value)) ?? "", "(\\B[A-Z])", " $1");
                return locationTypeName;
                break;
            case "StartTs":
            case "EndTs":
                var dateTime = DateTime.Parse(value);
                return dateTime.ToString("yyyy-MM-ddTHH:mm:ssZ");
            break;
        }
        return value;
    }

    private string IncludeDriverName(string? clientDriverId) {
        if(clientDriverId == null) return string.Empty;
        var driverFirstName = this._auditDataSetup.Drivers?.Find(a => a.ClientDriverId.Equals(this._auCurrentBooking.AuBooking.RequestClientDriverId))?.DriverName;
        var driverSurname = this._auditDataSetup.Drivers?.Find(a => a.ClientDriverId.Equals(this._auCurrentBooking.AuBooking.RequestClientDriverId))?.DriverSurname;
        return driverFirstName + " " + driverSurname;
    }
    
        
    
}