﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Cartrack.AppHost" Version="2.5.7" />
        <PackageReference Include="Cartrack.EFCore.Models.Pool" Version="1.1.0" />
        <PackageReference Include="Cartrack.EFCore.Models.TfmsCustom" Version="1.2.1" />
        <PackageReference Include="Minio.AspNetCore" Version="6.0.1" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Cartrack.Fleet.Common\Cartrack.Fleet.Common.csproj" />
        <ProjectReference Include="..\Cartrack.Fleet.Driver\Cartrack.Fleet.Driver.csproj" />
        <ProjectReference Include="..\Cartrack.Fleet.License\Cartrack.Fleet.License.csproj" />
        <ProjectReference Include="..\Cartrack.Fleet.User\Cartrack.Fleet.User.csproj" />
        <ProjectReference Include="..\Cartrack.Fleet.Vehicle\Cartrack.Fleet.Vehicle.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Features\ApproveBooking\Scdf\" />
        <Folder Include="Features\GetBooking\Scdf\" />
    </ItemGroup>

</Project>
