using Cartrack.EFCore.Models.CT;
using Cartrack.Fleet.Booking.Features.GetBooking.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult<ScdfGetBookingResponse>> GetBooking(long id) {
        var request = new ScdfGetBookingRequest(id);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = authClaims.ClientUserId;
        
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfGetBookingResponse, Booking>(this.HttpContext);
    }
}