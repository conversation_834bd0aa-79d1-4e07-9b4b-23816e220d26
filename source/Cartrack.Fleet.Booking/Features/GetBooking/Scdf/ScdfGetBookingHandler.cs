﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Http.Filters;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.GetBooking.Scdf;

public class ScdfGetBookingHandler(ScdfBookingRepository repo, IHttpContextAccessor contextAccessor, ILogger<ScdfGetBookingHandler> logger)
    : IRequestHandler<ScdfGetBookingRequest, ScdfGetBookingResponse> {
    public async Task<ScdfGetBookingResponse> Handle(ScdfGetBookingRequest request, CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.Id > 0, "Booking ID must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{RequestId}] [{Agency}] Retrieving the booking with Id={BookingId}", contextAccessor.HttpContext?.TraceIdentifier ?? "", request.Account, request.Id);

            var booking = await repo.GetBooking(request.Account, request.Id);
            var apiBooking = booking?.ToHttpBooking();
            if (apiBooking is null) {
                return new ScdfGetBookingResponse(apiBooking, NotFoundException.Create($"Booking with Id={request.Id} not found"))
                    { IsServerError = false };
            }

            apiBooking.IsDetailView = true;
            return new ScdfGetBookingResponse(apiBooking);

        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{RequestId}] Invalid request value. Error retrieving booking", contextAccessor.HttpContext?.TraceIdentifier ?? "");
            return new ScdfGetBookingResponse(null, ex) { IsServerError = false };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{RequestId}] Error retrieving booking", contextAccessor.HttpContext?.TraceIdentifier ?? "");
            return new ScdfGetBookingResponse(null, ex) { IsServerError = true };
        }
    }
}