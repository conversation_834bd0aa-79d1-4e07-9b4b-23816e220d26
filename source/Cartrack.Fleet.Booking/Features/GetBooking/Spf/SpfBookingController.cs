﻿using Cartrack.Fleet.Booking.Features.GetBooking.Spf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class SpfBookingController {
    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult<SpfGetBookingResponse>> GetBooking(long id) {
        var resp = await this._mediator.Send(new SpfGetBookingRequest(id));
        return resp.ToContentResult<SpfGetBookingResponse, Booking>(this.HttpContext);
    }
}