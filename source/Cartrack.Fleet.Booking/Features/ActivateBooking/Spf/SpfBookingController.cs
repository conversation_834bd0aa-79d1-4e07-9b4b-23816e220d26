﻿using Cartrack.Fleet.Booking.Features.ActivateBooking.Spf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class SpfBookingController {
    [HttpPost]
    [Route("{id}/activate")]
    public async Task<ActionResult<SpfActivateBookingResponse>> ActivateBooking(long id,
        [FromBody] SpfActivateBookingRequest request) {
        request.BookingId = id;
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<SpfActivateBookingResponse, BookingStatus>(this.HttpContext);
    }
}