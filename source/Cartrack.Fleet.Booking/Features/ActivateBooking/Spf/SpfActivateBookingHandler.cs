﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.ActivateBooking.Spf;

public class SpfActivateBookingHandler(IBookingRepository repo, IHttpContextAccessor context, ILogger<SpfActivateBookingHandler> logger)
    : IRequestHandler<SpfActivateBookingRequest, SpfActivateBookingResponse> {
    public async Task<SpfActivateBookingResponse> Handle(SpfActivateBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Activateing a booking", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var booking = await repo.GetBooking(request.Account, request.BookingId);
            await booking?.Status.Approve()!;
            await repo.UpdateStatus(booking.BookingId, BookingStatusCode.Approved);
            var statusHttp = new BookingStatus(booking.BookingId, Constants.Approved);
            return new SpfActivateBookingResponse(statusHttp);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error Activate booking", context.HttpContext?.TraceIdentifier ?? "");
            return new SpfActivateBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error Activate booking", context.HttpContext?.TraceIdentifier ?? "");
            return new SpfActivateBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private SpfVehicleBooking ApproveFromRequest(SpfActivateBookingRequest request) {
        throw new NotImplementedException();
    }
}