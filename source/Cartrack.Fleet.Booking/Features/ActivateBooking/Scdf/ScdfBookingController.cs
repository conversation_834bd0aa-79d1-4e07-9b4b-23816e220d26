﻿using Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf;
using Cartrack.Fleet.Booking.IO.Http.CustomAuthorization;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Cartrack.Fleet.Booking.IO.Http;

// ReSharper disable once IdentifierTypo
public partial class ScdfBookingController {
    [HttpPatch]
    [Route("activate")]
    [AuthorizeIssuance(AuthorizeIssuanceCheck.CanActivateBooking)]
    public async Task<ActionResult<ScdfActivateBookingResponse>> ActivateBooking([FromBody] ScdfActivateBookingRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ActivatedClientUserId = authClaims.ClientUserId;


        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfActivateBookingResponse, Booking>(this.HttpContext);
    }
}