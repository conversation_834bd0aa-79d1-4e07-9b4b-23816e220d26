﻿

using Microsoft.EntityFrameworkCore;
// ReSharper disable once CheckNamespace
using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class ScdfBookingRepository {
    public override async Task<long> ActivateBooking(VehicleBookingBase booking) {
        var id = await base.ActivateBooking(booking);
        var scdfBookingInfo = await this._tfmsCustomDbContext.ScdfBookingAdditionalInfos.Where(x => x.BookingId == booking.BookingId).FirstOrDefaultAsync();
        if (scdfBookingInfo != null) {
            scdfBookingInfo.VehicleCommanderClientUserId = booking.VehicleCommanderClientUserId;
            scdfBookingInfo.BookingAuditReference = booking.BookingAuditReference;
            
            await this._tfmsCustomDbContext.SaveChangesAsync();
        }

        return id;
    }
}