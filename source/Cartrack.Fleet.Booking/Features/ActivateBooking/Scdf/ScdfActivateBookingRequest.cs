﻿using Cartrack.Fleet.Booking.Domain.Common;
using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf;

// ReSharper disable once IdentifierTypo
public record ScdfActivateBookingRequest() : IRequest<ScdfActivateBookingResponse> {
    [JsonIgnore] public string Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; }
    public long BookingId { get; set; }
    public DateTime PickUpTime { get; set; }
    public DateTime ActualPickUpTime { get; set; }
    public int VehicleId { get; set; }
    public required string ClientDriverId { get; set; }
    public required string VehicleCommanderClientUserId { get; set; }
    public required string ActivatedClientUserId { get; set; }
}