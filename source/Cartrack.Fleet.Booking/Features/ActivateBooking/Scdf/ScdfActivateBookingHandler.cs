﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.License.Features.GetPdpLicense;
using Cartrack.Fleet.License.Features.GetQdlLicense;
using Cartrack.Fleet.Vehicle.IO.Http.Filters;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Constants = Cartrack.Fleet.Booking.Domain.Common.Constants;

namespace Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf;


/// <summary>
/// Handles the activation of SCDF (Singapore Civil Defence Force) bookings.
/// </summary>
/// <remarks>
/// This class is responsible for processing requests to activate bookings in the SCDF system.
/// It validates the request, retrieves the booking, updates its details, and activates it.
/// </remarks>
public class ScdfActivateBookingHandler(
    ScdfBookingRepository repo,
    ScdfVehicleBookingBuilder bookingBuilder,
    IBookingRuleRepository rule,
    IDriverRepository driverRepo,
    IVehicleRepository vehicleRepo,
    IMediator mediator,
    IHttpContextAccessor contextAccessor,
    ILogger<ScdfActivateBookingHandler> logger)
    : IRequestHandler<ScdfActivateBookingRequest, ScdfActivateBookingResponse> {
    
    
    /// <summary>
    /// Handles the activation of a booking based on the provided request.
    /// </summary>
    /// <param name="request">The activation request containing booking details.</param>
    /// <param name="cancellationToken">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A response indicating the result of the activation attempt.</returns>
    /// <remarks>
    /// This method performs the following steps:
    /// 1. Validates the request parameters.
    /// 2. Retrieves the booking from the repository.
    /// 3. Fetches and updates driver license information.
    /// 4. Updates booking details.
    /// 5. Validates the updated booking.
    /// 6. Activates the booking status.
    /// 7. Persists the changes to the repository.
    /// 8. Returns the updated booking information.
    /// 
    /// If any errors occur during this process, appropriate error responses are returned.
    /// </remarks>
    public async Task<ScdfActivateBookingResponse> Handle(ScdfActivateBookingRequest request, CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.BookingId != 0, "Booking Id is Required.");
            Requires.IsTrue(() => request.PickUpTime is { } time, "Pickup Time is Required.");
            logger.LogInformation("[{RequestId}] [{Agency}] Activating a booking", contextAccessor.HttpContext?.TraceIdentifier ?? "", request.Account);
            
            var booking = await repo.GetBooking(bookingBuilder, request.BookingId);
            if (booking is null) {
                return new ScdfActivateBookingResponse(null, NotFoundException.Create($"Booking {request.BookingId} not found")) { IsServerError = false };
            }

            try {
                //Fetch the license online and wait a bit for it to get updated to the DB
                var qdlReq = new GetQdlLicenseRequest(booking.RequestClientDriverId!);
                await mediator.Send(qdlReq, cancellationToken);
                var pdpReq = new GetPdpLicenseRequest(booking.RequestClientDriverId!);
                await mediator.Send(pdpReq, cancellationToken);
                await Task.Delay(500, cancellationToken);
            }
            catch (Exception exc) {
                logger.LogWarning("[{TraceId}] Unable to fetch data from license service.\n{Err}", contextAccessor.HttpContext?.TraceIdentifier ?? "", exc);
            }

            await bookingBuilder.UpdateBookingDetails(request, repo, rule, driverRepo, vehicleRepo);
            await bookingBuilder.VehicleBooking!.Validate();
            await booking.Status.Activate();
            var bookingId = await repo.ActivateBooking(booking);
            var updatedBooking = await repo.GetBooking(request.Account, bookingId);
            return new ScdfActivateBookingResponse(updatedBooking?.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{RequestId}] Invalid request value. Error Activate booking", contextAccessor.HttpContext?.TraceIdentifier ?? "");
            return new ScdfActivateBookingResponse(null, ex) { IsServerError = false };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{RequestId}] Error Activate booking", contextAccessor.HttpContext?.TraceIdentifier ?? "");
            return new ScdfActivateBookingResponse(null, ex) { IsServerError = true };
        }
    }
}