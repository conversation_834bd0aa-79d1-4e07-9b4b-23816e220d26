﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.GetRejectBookingReasons;

public class
    GetRejectBookingReasonsHandler(IBookingRepository repo, IHttpContextAccessor context, ILogger<GetRejectBookingReasonsHandler> logger)
    : IRequestHandler<GetRejectBookingReasonsRequest, GetRejectBookingReasonsResponse> {
    public async Task<GetRejectBookingReasonsResponse> Handle(GetRejectBookingReasonsRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.Offset >= 0, "Offset must be greater than or equal to zero");
            //Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving reject booking reason lists", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var result = await repo.GetRejectBookingReasons(request.UserId, request.RejectBookingReasonId);
            var apiRejectReasons = result?.ToHttpRejectBookingReasons();
            return new GetRejectBookingReasonsResponse(apiRejectReasons);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving reject booking reason lists",context.HttpContext?.TraceIdentifier ?? "");
            return new GetRejectBookingReasonsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving reject booking reason lists", context.HttpContext?.TraceIdentifier ?? "");
            return new GetRejectBookingReasonsResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}