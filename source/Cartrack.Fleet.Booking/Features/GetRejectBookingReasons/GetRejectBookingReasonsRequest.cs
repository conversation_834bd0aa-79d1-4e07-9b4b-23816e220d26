﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.GetRejectBookingReasons;

public record GetRejectBookingReasonsRequest(long RejectBookingReasonId = 0) : IRequest<GetRejectBookingReasonsResponse> {
    [JsonIgnore] public string? Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; }
    [JsonIgnore] public string ClientUserId { get; set; } = "";
}