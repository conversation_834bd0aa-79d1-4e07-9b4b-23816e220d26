﻿using Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Cartrack.Fleet.Booking.IO.Http;

// ReSharper disable once IdentifierTypo
public partial class ScdfBookingController {
    [HttpPost]
    [Route("process-activity-logs")]
    public async Task<ActionResult<ScdfProcessBookingActivityLogsResponse>> ScdfProcessBookingActivityLogs([FromBody] ScdfProcessBookingActivityLogsRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = authClaims.ClientUserId;

        
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfProcessBookingActivityLogsResponse, BookingRunServiceStatus>(this.HttpContext);
    }
}