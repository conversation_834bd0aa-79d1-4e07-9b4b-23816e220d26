﻿using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Common;

// ReSharper disable once IdentifierTypo
namespace Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf;

// ReSharper disable once IdentifierTypo
public record ScdfProcessBookingActivityLogsResponse(IO.Http.BookingRunServiceStatus? Value, Exception? Error = null) : BaseResponse<IO.Http.BookingRunServiceStatus>(Value, Error) {
}