﻿using MediatR;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf;

// ReSharper disable once IdentifierTypo
public record ScdfProcessBookingActivityLogsRequest : IRequest<ScdfProcessBookingActivityLogsResponse> {
    [JsonIgnore] public string? Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; } = 0;
    public long BookingId { get; set; }
    [JsonIgnore] public string ClientUserId { get; set; } = "";
}