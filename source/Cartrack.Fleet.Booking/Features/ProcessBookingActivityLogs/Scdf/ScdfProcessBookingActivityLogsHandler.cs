﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

// ReSharper disable once IdentifierTypo
namespace Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf;

// ReSharper disable once IdentifierTypo
public class ScdfProcessBookingActivityLogsHandler( 
    IBookingRepository repo, 
    IBookingActivityLogsRepository activityLogsRepo, 
    IHttpContextAccessor context,
    ILogger<ScdfProcessBookingActivityLogsHandler> logger) : IRequestHandler<ScdfProcessBookingActivityLogsRequest, ScdfProcessBookingActivityLogsResponse> {
    public async Task<ScdfProcessBookingActivityLogsResponse> Handle(ScdfProcessBookingActivityLogsRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Process Audit Booking Activity Logs", context.HttpContext?.TraceIdentifier ?? "", request.Account);
            
            var isProcessRun = await activityLogsRepo.RunProcessActivityLogs(request.UserId, request.BookingId);
            
            var serviceStatus = new BookingRunServiceStatus {
                Status = isProcessRun
            };
            return new ScdfProcessBookingActivityLogsResponse(serviceStatus?.ToHttpBookingServiceProcess());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error processing booking activity logs", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfProcessBookingActivityLogsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error processing booking activity logs", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfProcessBookingActivityLogsResponse(null, ex) {
                IsServerError = true
            };
        }
    }

}