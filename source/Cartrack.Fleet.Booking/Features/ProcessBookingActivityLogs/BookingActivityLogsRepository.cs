﻿using CtDb = Cartrack.EFCore.Models.CT;
using FleetDb = Cartrack.EFCore.Models.Fleet;
using PoolDb = Cartrack.EFCore.Models.Pool;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingActivityLogsRepository {
    private List<PoolDb.Accessory> _accessories = [];
    private List<PoolDb.AdditionalLocation> _additionalLocations = [];
    private List<FleetDb.SiteLocation> _siteLocations = [];
    private List<CtDb.Vehicle> _vehicles = [];
    private List<FleetDb.ClientDriver> _drivers = [];
    private List<FleetDb.ClientUser> _clientUsers = [];
    private List<PoolDb.BookingVehicleType> _bookingVehicleTypes = [];
    private List<PoolDb.BookingPurpose> _bookingPurposes = [];
    private List<PoolDb.BookingCancelReason> _bookingCancelReasons = [];
    private List<PoolDb.BookingRejectReason> _bookingRejectReasons = [];
    private List<PoolDb.BookingForceTerminateReason> _bookingForceTerminateReasons = [];
    
    public async Task<bool> RunProcessActivityLogs(long userId, long bookingId = 0) {
        List<long> bookingIds = [bookingId];
        if (bookingId == 0) {
            bookingIds = await poolDbContext.Bookings
                .Where(a => a.UserId == userId)
                .Select(a => a.BookingId).ToListAsync();
        }
        
        
        //---- Get Main Table of Accessory and Journeys and SiteLocations for Text Info
        this.SetAccessories(userId);
        this.SetAdditionalLocations(userId);
        this.SetSiteLocations(userId);
        
        var auditDataSetup = new AuditDataSetup {
            Vehicles = await this.GetVehicles(userId),
            Drivers = await this.GetDrivers(userId),
            Users = await this.GetClientUsers(userId),
            BookingVehicleTypes = await this.GetBookingVehicleType(userId),
            BookingPurpose = await this.GetBookingPurpose(userId),
            CancelReason = await this.GetBookingCancelReasons(userId),
            RejectReason =  await this.GetBookingRejectReasons(userId),
            ForceTerminateReason = await this.GetBookingForceTerminateReasons(userId),
            SiteLocations = this._siteLocations
        };
        
        // -------------------
        foreach (var bId in bookingIds) {
            
            var auditPoolBookingExist = await this.CheckLastTimestampBookingActivityLogs(userId, bId);
            if (auditPoolBookingExist == null) {
                // if no new audit, no need to process
                continue;
            }

            //----------------------------------------------------
            var booking = await poolDbContext.AuBookings.Where(b => b.BookingId == bId)
                .OrderBy(b => b.AuAuditId)
                .ToListAsync();

            if (booking.Count > 0) {
                var bookingStats = await poolDbContext.BookingStatuses.ToDictionaryAsync(a => a.BookingStatusId,
                    b => b.BookingStatus1);

                var previousAuBooking = new ScdfAuBookings {
                    AuBooking = new PoolDb.AuBooking { AuAuditId = 0, BookingStatusId = 0 },
                    AuScdfBookingAdditionalInfo = new AuScdfBookingAdditionalInfo(),
                    AuBookingJourneys = string.Empty,
                    AuBookingAccessories = string.Empty,
                    AuBookingAttachments = string.Empty,
                };

                foreach (var (thisAuBookingItem, index) in booking.Select((value, i) => (value, i))) {
                    
                    var currentAuBooking = new ScdfAuBookings {
                        AuBooking = thisAuBookingItem,
                        AuScdfBookingAdditionalInfo = await this.ProcessBookingForScdfBookingAdditionalInfo(thisAuBookingItem),
                        AuBookingJourneys = await this.ProcessBookingForBookingJourneys(thisAuBookingItem),
                        AuBookingAccessories = await this.ProcessBookingForBookingAccessories(thisAuBookingItem),
                        AuBookingAttachments = await this.ProcessBookingForBookingAttachments(thisAuBookingItem)
                    };

                    var builder = new AuditBookingActivityBuilder(
                        poolDbContext, tfmsCustomDbContext, fleetDbContext, ctDbContext,
                        auditDataSetup, previousAuBooking, currentAuBooking);

                    await builder.Start();
                    var jsonData = builder.ActivityLogEntry!.AuAction;

                    var newBookingActivityLogTbl = new EFCore.Models.TfmsCustom.BookingActivityLog {
                        UserId = userId,
                        BookingId = bId,
                        BookingStatusIdFrom = previousAuBooking.AuBooking.BookingStatusId,
                        BookingStatusIdTo = thisAuBookingItem.BookingStatusId,
                        AuditIdReferenceFrom = previousAuBooking.AuBooking.AuAuditId,
                        AuditIdReferenceTo = currentAuBooking.AuBooking.AuAuditId,
                        AuditUsername = currentAuBooking.AuBooking.AuUsername,
                        AuditTs = currentAuBooking.AuBooking.AuTs,
                        Jsondata = JsonSerializer.Serialize(jsonData)
                    };

                    bool exists = await tfmsCustomDbContext.BookingActivityLogs.AnyAsync(log =>
                        log.AuditIdReferenceFrom == previousAuBooking.AuBooking.AuAuditId &&
                        log.AuditIdReferenceTo == currentAuBooking.AuBooking.AuAuditId);

                    if (!exists) {
                        await tfmsCustomDbContext.BookingActivityLogs.AddAsync(newBookingActivityLogTbl);
                    }

                    // currentAuBooking become previousBooking for next iteration.
                    previousAuBooking = currentAuBooking;

                }
                await tfmsCustomDbContext.SaveChangesAsync();
            }
        }

        return true;
    }

    private async Task<PoolDb.AuBooking?> CheckLastTimestampBookingActivityLogs(long userId, long bookingId) {
        // Check if we have a missing activity logs that is not caught yet. 
        var bookingActivityLogLatestAuditTimestamp = await tfmsCustomDbContext.BookingActivityLogs
            .Where(b => b.BookingId == bookingId)
            .Where(b => b.UserId == userId)
            .OrderByDescending(b => b.AuditTs)
            .Select(b => b.AuditTs)
            .FirstOrDefaultAsync();
        
        var query = poolDbContext.AuBookings
            .Where(b => b.BookingId == bookingId)
            .Where(b => b.UserId == userId);
        
        if (bookingActivityLogLatestAuditTimestamp != null) {
            query = query.Where(b => b.AuTs > bookingActivityLogLatestAuditTimestamp);
        }

        var existBookingActivityLogs = await query.FirstOrDefaultAsync();
        
        return existBookingActivityLogs;
    }

    private async Task<AuScdfBookingAdditionalInfo?> ProcessBookingForScdfBookingAdditionalInfo(PoolDb.AuBooking? auBooking) {
        
        var query = tfmsCustomDbContext.AuScdfBookingAdditionalInfos
            .Where(b => b.BookingId == auBooking.BookingId);
        
        // if no booking audit reference, we will refer it to nearest time at audit booking.
        if (!string.IsNullOrEmpty(auBooking.BookingAuditReference)) {
            query = query.Where(b => b.BookingAuditReference.Equals(auBooking.BookingAuditReference));
        }
        else {
            var auTsBufferTime = auBooking!.AuTs.Value.AddSeconds(5);
            query = query.Where(b => b.AuTs <= auTsBufferTime);
        }

        var thisAuScdfBookingAdditionalInfo = await query.OrderBy(b => b.AuAuditId)
            .FirstOrDefaultAsync();
        
        return thisAuScdfBookingAdditionalInfo;
    }
    
    private async Task<string> ProcessBookingForBookingAccessories(PoolDb.AuBooking? auBooking) {
        
        var query = poolDbContext.AuBookingAccessories
            .Where(b => b.BookingId == auBooking!.BookingId)
            .Where(b => b.IsDeleted == false)
            .Where(b => (b.AuActionType == 'I' || b.AuActionType == 'U'));

        if (!string.IsNullOrEmpty(auBooking!.BookingAuditReference)) {
            query = query.Where(b => b.BookingAuditReference.Equals(auBooking.BookingAuditReference));
        }
        else {
            var auTsBufferTime = auBooking!.AuTs.Value.AddSeconds(5);
            query = query.Where(b => auBooking.AuTs <= b.AuTs && b.AuTs <= auTsBufferTime);
        }

        var thisAuBookingAccessories = await query.OrderBy(b => b.AuAuditId)
            .Select(a => a.AccessoryId)
            .ToListAsync();
        
        string value = string.Join(" ; ", thisAuBookingAccessories
                .Select(a => this._accessories.FirstOrDefault(x => x.Id == a)?.Name).ToList());
        
        return value;
    }
    
    private async Task<string> ProcessBookingForBookingAttachments(PoolDb.AuBooking? auBooking) {
        
        var query = poolDbContext.AuBookingAttachments
            .Where(b => b.BookingId == auBooking.BookingId)
            .Where(b => b.AuActionType == 'I');

        if (!string.IsNullOrEmpty(auBooking.BookingAuditReference)) {
            query = query.Where(b => b.BookingAuditReference.Equals(auBooking.BookingAuditReference));
        }
        else {
            var auTsBufferTime = auBooking!.AuTs.Value.AddSeconds(5);
            query = query.Where(b => auBooking.AuTs <= b.AuTs && b.AuTs <= auTsBufferTime);
        }

        var thisAuBookingAttachments = await query.OrderBy(b => b.AuAuditId)
            .Select(a => a.Filename)
            .ToListAsync();
        
        string value = string.Join(" ; ", thisAuBookingAttachments);
        
        return value;
    }
    
    private async Task<string> ProcessBookingForBookingJourneys(PoolDb.AuBooking? auBooking) {

        var query = poolDbContext.AuBookingJourneys
            .Where(b => b.BookingId == auBooking.BookingId)
            .Where(b => b.AuActionType == 'I')
            ;
        
        if (!string.IsNullOrEmpty(auBooking.BookingAuditReference)) {
            query = query.Where(b => b.BookingAuditReference.Equals(auBooking.BookingAuditReference));
        }
        else {
            var auTsBufferTime = auBooking!.AuTs.Value.AddSeconds(5);
            query = query.Where(b => auBooking.AuTs <= b.AuTs && b.AuTs <= auTsBufferTime);
        }

        var thisAuBookingJourneys = await query.OrderBy(b => b.AuAuditId)
            .Select(a => new { a.JourneyLocationType, a.JourneyLocationId, a.AuActionType })
            .ToListAsync();
        
        var journeys = new List<string>();
        foreach (var journey in thisAuBookingJourneys) {
            var locationName = "";
            switch (journey?.JourneyLocationType?.ToLower()) {
                case "additionallocation":
                    locationName = this._additionalLocations
                        .FirstOrDefault(x => x.Id == long.Parse(journey.JourneyLocationId ?? string.Empty))
                        ?.LocationName;
                    break;
                case "location":
                    locationName = this._siteLocations
                        .FirstOrDefault(x => x.SiteLocationId == long.Parse(journey.JourneyLocationId ?? string.Empty))
                        ?.SiteLocationName;
                    break;
            }
            if (!string.IsNullOrEmpty(locationName)) { journeys.Add(locationName);}
        }

        string value = string.Join(" --> ", journeys);
        
        return value;
    }
    private async Task<List<CtDb.Vehicle>> GetVehicles(long userId) {
        return await ctDbContext.Vehicles.Where(b => b.UserId == userId).ToListAsync();
    }
    private async Task<List<FleetDb.ClientDriver>> GetDrivers(long userId) {
        return await fleetDbContext.ClientDrivers.Where(b => b.UserId == userId) .ToListAsync();
    }
    private async Task<List<FleetDb.ClientUser>> GetClientUsers(long userId) {
        return await fleetDbContext.ClientUsers.Where(b => b.UserId == userId).ToListAsync();
    }
    private async Task<List<PoolDb.BookingVehicleType>> GetBookingVehicleType(long userId) {
        return await poolDbContext.BookingVehicleTypes.Where(b => b.UserId == userId).ToListAsync();
    }
    private async Task<List<PoolDb.BookingPurpose>> GetBookingPurpose(long userId) {
        return await poolDbContext.BookingPurposes.Where(b => b.UserId == userId).ToListAsync();
    }
    private async Task<List<PoolDb.BookingCancelReason>> GetBookingCancelReasons(long userId) {
        return await poolDbContext.BookingCancelReasons.Where(b => b.UserId == userId).ToListAsync();
    }
    private async Task<List<PoolDb.BookingRejectReason>> GetBookingRejectReasons(long userId) {
        return await poolDbContext.BookingRejectReasons.Where(b => b.UserId == userId).ToListAsync();
    }
    private async Task<List<PoolDb.BookingForceTerminateReason>> GetBookingForceTerminateReasons(long userId) {
        return await poolDbContext.BookingForceTerminateReasons.Where(b => b.UserId == userId).ToListAsync();
    }
    private void SetAccessories(long userId) {
        this._accessories = poolDbContext.Accessories.Where(b => b.UserId == userId && !b.IsDeleted).ToList();
    }
    private void SetAdditionalLocations(long userId) {
        this._additionalLocations = poolDbContext.AdditionalLocations.Where(b => !b.IsDeleted).ToList();
    }
    private void SetSiteLocations(long userId) {
        this._siteLocations = fleetDbContext.SiteLocations.Where(b => b.UserId == userId).ToList();
    }
}