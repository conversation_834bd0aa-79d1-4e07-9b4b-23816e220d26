﻿using Cartrack.Fleet.Booking.Domain.Common;
using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf;

// ReSharper disable once IdentifierTypo
public record ScdfApproveBookingRequest(long BookingId) : IRequest<ScdfApproveBookingResponse> {
    [JsonIgnore] public string? Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; }
    [JsonIgnore] public long BookingId { get; set; } = BookingId;
    public string? ApproveClientUserId { get; set; }
    public long BookingPurposeId { get; set; }
    public string? BookingPurposeDescription { get; set; }
    public long VehicleId { get; set; }

    public bool IsBookingForOtherParty { get; set; } = false;

    //public string? RequestClientUserId { get; set; }
    public string? RequestedForClientUserId { get; set; } // Requested For
    public DateTime StartTs { get; set; }
    public DateTime EndTs { get; set; }

    public long? BookingVehicleTypeId { get; set; }

    //public DriverType? DriverType { get; set; }
    public required string RequestClientDriverId { get; set; }
    public EquipmentType? EquipmentType { get; set; }
    public List<int>? Accessories { get; set; }
    public string? Remarks { get; set; }
    public int PickupSiteLocationId { get; set; }
    public JourneyType JourneyType { get; set; }

    public required List<JourneyLocation> Journeys { get; set; }

    //tfmsCustom.Scdf_booking_additional_info
    //public VehicleCommanderType? VehicleCommanderType { get; set; }
    public string? VehicleCommanderClientUserId { get; set; }
    public int NumberOfPassengers { get; set; } = 0;
    public List<int>? EquipmentAttachmentIds { get; set; } // for existing attachment link to the booking
    public List<InputEquipmentAttachment>? EquipmentAttachments { get; set; } // for new attachments 
}