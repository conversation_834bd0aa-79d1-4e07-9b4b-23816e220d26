﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.CT;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Common.States;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.License.Features.GetPdpLicense;
using Cartrack.Fleet.License.Features.GetQdlLicense;
using Cartrack.Fleet.User.IO.Sql;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Journey = Cartrack.Fleet.Booking.Domain.Common.Journey;
using Vehicle = Cartrack.Fleet.Booking.Domain.Common.Vehicle;

namespace Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf;

/// <summary>
/// Handles the approval of SCDF (Singapore Civil Defence Force) bookings.
/// </summary>
/// <remarks>
/// This class is responsible for processing requests to approve bookings in the SCDF system.
/// It validates the request, retrieves the booking, updates its details, and approves it.
/// </remarks>
public class ScdfApproveBookingHandler(
    ScdfBookingRepository repo,
    IBookingRuleRepository ruleRepo,
    ScdfVehicleBookingBuilder bookingBuilder,
    IVehicleRepository vehicleRepo,
    IDriverRepository driverRepo,
    IUserRepository userRepo, 
    IUserAppSettingsRepository userAppSettingsRepo,
    IHttpContextAccessor contextAccessor,
    IMediator mediator,
    ILogger<ScdfApproveBookingHandler> logger)
    : IRequestHandler<ScdfApproveBookingRequest, ScdfApproveBookingResponse> {
    
    
    /// <summary>
    /// Handles the approval of a booking based on the provided request.
    /// </summary>
    /// <param name="request">The approval request containing booking details.</param>
    /// <param name="cancellationToken">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A response indicating the result of the approval attempt.</returns>
    /// <remarks>
    /// This method performs the following steps:
    /// 1. Validates the request parameters.
    /// 2. Retrieves the booking from the repository.
    /// 3. Updates booking details and validates them.
    /// 4. Approves the booking status.
    /// 5. Persists the changes to the repository.
    /// 6. Returns the updated booking information.
    /// 7. Triggers a background update of the driver's license information.
    ///
    /// If any errors occur during this process, appropriate error responses are returned.
    /// </remarks>
    public async Task<ScdfApproveBookingResponse> Handle(ScdfApproveBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            Requires.IsTrue(() => request.BookingId != 0, "Booking Id is Required.");
            Requires.NotNullOrEmpty(request.RequestClientDriverId ?? "", nameof(request.RequestClientDriverId));
            Requires.IsTrue(() => request.BookingId != 0, "Booking Id is Required.");
            Requires.IsTrue(() => request.VehicleId > 0, "Vehicle is Required.");
            logger.LogInformation("[{RequestId}] [{Agency}] Approving a booking {Id}", contextAccessor.HttpContext?.TraceIdentifier ?? "", request.Account, request.BookingId);

            var booking = await repo.GetBooking(bookingBuilder, request.BookingId);
            if (booking is null) {
                return new ScdfApproveBookingResponse(null, new NotFoundException($"Booking {request.BookingId} not found")) { IsServerError = false };
            }

            //1. Update the existing booking with new information from the request and validate the rules
            await bookingBuilder.UpdateBookingDetails(request, repo, ruleRepo, driverRepo, vehicleRepo);
            await bookingBuilder.VehicleBooking!.Validate();
            
            //2. Validate then Approve the booking
            await booking.Status.Approve();
            
            //3. Save the updated booking
            await repo.UpdateBooking(booking);

            //4. return the updated booking
            var updatedBooking = await repo.GetBooking(request.Account, booking.BookingId);
            
            //5. Trigger a background update of the license from Elites
            _ = Task.Run(async () => {
                if (updatedBooking != null) {
                    var qdlReq = new GetQdlLicenseRequest(updatedBooking.RequestClientDriverId!);
                    var qdlResp = await mediator.Send(qdlReq, cancellationToken);
                    
                    var pdpReq = new GetPdpLicenseRequest(updatedBooking.RequestClientDriverId!);
                    var pdpResp = await mediator.Send(pdpReq, cancellationToken);
                }
            }, cancellationToken);
            
            return new ScdfApproveBookingResponse(updatedBooking?.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{RequestId}] Invalid request value. Error approve booking", contextAccessor.HttpContext?.TraceIdentifier ?? "");
            return new ScdfApproveBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{RequestId}] Error approve booking", contextAccessor.HttpContext?.TraceIdentifier ?? "");
            return new ScdfApproveBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}