﻿using Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf;
using Cartrack.Fleet.Booking.IO.Http.CustomAuthorization;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Cartrack.Fleet.Booking.IO.Http;

// ReSharper disable once IdentifierTypo
public partial class ScdfBookingController {
    [HttpPatch]
    [Route("{bookingId:long}/approve")]
    [AuthorizeIssuance(AuthorizeIssuanceCheck.CanApproveBooking)]
    public async Task<ActionResult<ScdfApproveBookingResponse>> ApproveBooking(long bookingId, [FromBody] ScdfApproveBookingRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ApproveClientUserId = authClaims.ClientUserId;

        request.BookingId = bookingId;
        //request.ApproveClientUserId = this.User.FindFirst(ClaimTypes.NameIdentifier)!.Value;
        //request.Account = this.User.FindFirst("Account")!.Value;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfApproveBookingResponse, Booking>(this.HttpContext);
    }
}