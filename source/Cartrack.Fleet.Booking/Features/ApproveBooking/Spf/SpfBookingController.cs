﻿using Cartrack.Fleet.Booking.Features.ApproveBooking.Spf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class SpfBookingController {
    [HttpPost]
    [Route("{id}/approve")]
    public async Task<ActionResult<SpfApproveBookingResponse>> ApproveBooking(long id,
        [FromBody] SpfApproveBookingRequest request) {
        request.BookingId = id;
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<SpfApproveBookingResponse, BookingStatus>(this.HttpContext);
    }
}