﻿using Cartrack.EFCore.Models.Pool;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Microsoft.EntityFrameworkCore;
using Npgsql;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository{
    public async Task<long> ApproveBooking(VehicleBookingBase booking) {
        long bookingId = booking.BookingId;
        var myBooking = await this._poolDbContext.Bookings.FindAsync(bookingId);
        if (myBooking == null) {
            return -1;
        }

        myBooking.BookingStatusId = (long)booking.BookingStatusId!;
        myBooking.VehicleId = booking.VehicleId;
        myBooking.Requestor = booking.RequestedBy.Username;
        myBooking.BookingVehicleTypeId = booking.BookingVehicleTypeId;
        myBooking.BookingPurposeId = booking.BookingPurposeId;
        myBooking.BookingPurposeDescription = booking.BookingPurposeDescription;
        myBooking.PickupSiteLocationId = booking.PickupSiteLocationId ?? 0;
        if (booking.JourneyType == JourneyType.Return) {
            myBooking.ReturnSiteLocationId = booking.PickupSiteLocationId ?? 0;
        }

        myBooking.StartTs = booking.StartDate;
        myBooking.EndTs = booking.EndDate;
        myBooking.RequestClientDriverId = booking.RequestClientDriverId;
        myBooking.RequestClientUserId = booking.RequestClientUserId;
        myBooking.Remarks = booking.Remarks;

        myBooking.UpdatedTs = DateTime.UtcNow;
        myBooking.DecisionTs = booking?.UpdatedDate;
        myBooking.IsApproved = true;

        var approvedClientUserId = booking?.ApprovedBy.Select(r => r.ClientUserId).FirstOrDefault();
        myBooking.DecisionClientUserId = approvedClientUserId;
        myBooking.BookingAuditReference = booking?.BookingAuditReference;

        await this._poolDbContext.SaveChangesAsync();

        // Edit into SCDF Custom Booking Additional Info
        var b = await this._tfmsCustomDbContext.ScdfBookingAdditionalInfos.FirstOrDefaultAsync(b => b.BookingId == bookingId);

        b.PassengerCount = booking.NumberOfPassengers;
        b.VehicleCommanderClientUserId = booking.VehicleCommanderClientUserId;
        b.RequestedForClientUserId = booking.RequestedForClientUserId;
        b.LocationType = (int)booking.JourneyType;
        b.BookingAuditReference = booking.BookingAuditReference;

        if (booking.DriverType != null) { b.DriverType = (int)booking.DriverType; }

        if (booking.VehicleCommanderType != null) { b.VehicleCommanderType = (int)booking.VehicleCommanderType; }

        ;
        if (booking.EquipmentType1 != null) { b.EquipmentType = (int)booking.EquipmentType1; }

        await this._tfmsCustomDbContext.SaveChangesAsync();

        // Journey Related
        await this.AddOrUpdateBookingJourneys(myBooking.UserId, bookingId, booking.BookingAuditReference, booking.Journeys);

        // Accessories Related
        if (booking.AccessoryIds is { Count: > 0 }) {
            await this.AddOrUpdateBookingAccessory(bookingId, booking.BookingAuditReference, booking.AccessoryIds);
        }

        // Booking Attachment
        // Existing Booking Attachment Ids
        if (booking.EquipmentAttachmentIds != null) {
            await this.UpdateBookingEquipmentAttachmentIds(bookingId, booking.BookingAuditReference, booking.EquipmentAttachmentIds);
        }

        // New Booking Attachments
        if (booking.EquipmentAttachments is { Count: > 0 }) {
            await this.AddBookingEquipmentAttachments(bookingId, booking.BookingAuditReference, booking.EquipmentAttachments);
        }

        return bookingId;
    }
}