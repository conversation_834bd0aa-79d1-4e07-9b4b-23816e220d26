﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Features.GetRequestPurposeVehicleCategoriesMap;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<List<RequestPurposeVehicleCategoryMap>>
        GetRequestPurposeVehicleCategoriesMaps(long userId, long bookingPurposeId, int page, int pageSize) {
        
        var query = this._poolDbContext.BookingPurposes
            .Where(x => x.UserId == userId && !x.IsDeleted);

        if (bookingPurposeId != 0) {
            query = query.Where(x => x.BookingPurposeId == bookingPurposeId);
        }
        
        var purposes = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync() ?? null;
        
        if (purposes is null) {
            return new List<RequestPurposeVehicleCategoryMap>();
        }

        var maps1 = await this._poolDbContext.BookingPurposeVehicleTypes
            .Where(x => purposes.Select(y => y.BookingPurposeId).Contains(x.BookingPurposeId))
            .ToListAsync();

        var vehicleCategories = await this._poolDbContext.BookingVehicleTypes
            .Where(x => x.UserId == userId && !x.IsDeleted)
            .ToListAsync();
        
        List<RequestPurposeVehicleCategoryMap> output = new List<RequestPurposeVehicleCategoryMap>();
        foreach (var p in purposes) {
            
            List<RequestVehicleType> vehicleTypes = [];
            foreach (var m in maps1.Where(x => x.BookingPurposeId == p.BookingPurposeId).ToList()) {
                vehicleTypes.Add(new RequestVehicleType {
                    Id = m.BookingVehicleTypeId,
                    Name = vehicleCategories.Where(x => x.BookingVehicleTypeId == m.BookingVehicleTypeId).Select(x => x.BookingVehicleType1).FirstOrDefault()!
                });
            }
            
            var tmp = new RequestPurposeVehicleCategoryMap {
                Id = p.BookingPurposeId,
                Title = p.BookingPurpose1,
                Description = p.BookingPurpose1,
                VehicleTypes = vehicleTypes
            };
            
            output.Add(tmp);
        }
        
        return output;
    }

    public async Task<int> GetRequestPurposeVehicleCategoriesMapsTotal(int userId) {
        var total = await this._poolDbContext.BookingPurposes
            .Where(x => x.UserId == userId && !x.IsDeleted)
            .CountAsync();
        
        return total;
    }
}