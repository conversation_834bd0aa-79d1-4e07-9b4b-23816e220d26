﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.GetRequestPurposeVehicleCategoriesMap;

public record GetRequestPurposeVehicleCategoriesMapRequest(long BookingPurposeId, int Page, int PageSize) : IRequest<GetRequestPurposeVehicleCategoriesMapResponse> {
    [JsonIgnore] public string? Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; }
    [JsonIgnore] public string ClientUserId { get; set; } = "";
}