﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task ForceTerminateBooking(List<long> bookingIds, string forceTerminateClientUserId, int forceTerminateReasonId, string bookingForceTerminateNotes) {
        var existsBookings = await this._poolDbContext.Bookings.Where(b => bookingIds.Contains(b.BookingId)).ToListAsync();
        foreach (var booking in existsBookings) {
            booking.BookingStatusId = (long)BookingStatusCode.ForceTerminated;
            booking.ReturnedIgnitionTs = DateTime.UtcNow;
            booking.UpdatedTs = DateTime.UtcNow;
            booking.ReturnedClientUserId = forceTerminateClientUserId;
            booking.BookingForceTerminateReasonId = forceTerminateReasonId;
            booking.BookingForceTerminateNotes = bookingForceTerminateNotes;
            booking.BookingAuditReference = Guid.NewGuid().ToString("N") + "_" + DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            
            this._poolDbContext.Update(booking);
        }
        
        await this._poolDbContext.SaveChangesAsync();
    }
}