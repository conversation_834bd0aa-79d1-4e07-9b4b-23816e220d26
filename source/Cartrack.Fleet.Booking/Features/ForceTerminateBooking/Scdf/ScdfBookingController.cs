﻿using Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf;
using Cartrack.Fleet.Booking.Infratructure;
using Cartrack.Fleet.Booking.IO.Http.CustomAuthorization;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    
    [HttpPatch]
    [Route("forceTerminate")]
    [AuthorizeIssuance(AuthorizeIssuanceCheck.CanTerminateBooking)]
    public async Task<ActionResult<ScdfForceTerminateBookingResponse>> ForceTerminateBooking([FromBody] ScdfForceTerminateBookingRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ForceTerminateClientUserId = authClaims.ClientUserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfForceTerminateBookingResponse, BookingResponseStatus>(this.HttpContext);
    }
}