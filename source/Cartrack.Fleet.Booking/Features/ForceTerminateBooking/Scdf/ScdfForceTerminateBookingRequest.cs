﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf;

public record ScdfForceTerminateBookingRequest : IRequest<ScdfForceTerminateBookingResponse> {
    [JsonIgnore]
    public string Account { get; set; } = "";
    [JsonIgnore]
    public long UserId { get; set; }

    public List<long> BookingIds { get; set; } = [];
    public int BookingForceTerminateReasonId{get;set;}
    public string BookingForceTerminateNotes { get; set; } = "";
    public string ForceTerminateClientUserId { get; set; } = "";

}