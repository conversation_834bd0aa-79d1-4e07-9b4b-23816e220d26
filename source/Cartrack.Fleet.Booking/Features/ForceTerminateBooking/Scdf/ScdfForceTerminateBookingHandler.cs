﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Infratructure;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf;

public class ScdfForceTerminateBookingHandler(ScdfBookingRepository repo, IHttpContextAccessor context, ILogger<ScdfForceTerminateBookingHandler> logger)
    : IRequestHandler<ScdfForceTerminateBookingRequest, ScdfForceTerminateBookingResponse> {
    public async Task<ScdfForceTerminateBookingResponse> Handle(ScdfForceTerminateBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            // Validation
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            Requires.IsTrue(() => request.BookingIds.Count > 0, "Booking Ids must have at least one booking id");
            Requires.IsTrue(() => request.BookingForceTerminateReasonId > 0, "Please select one Booking Cancel Reason ");
            Requires.NotNullOrEmpty(request.BookingForceTerminateNotes, nameof(request.BookingForceTerminateNotes));

            logger.LogInformation("[{TraceId}] [{Agency}] Force Terminate booking", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var forceTerminateReason = await repo.GetForceTerminateBookingReasons(request.UserId, request.BookingForceTerminateReasonId);
            Requires.IsTrue(() => forceTerminateReason.Count > 0, "Please select one Booking Termination Reason ");

            foreach (var bookingId in request.BookingIds) {
                var booking = await repo.GetBooking(request.Account, bookingId);
                if (booking?.BookingId == 0) {
                    List<long> bIds = [bookingId];
                    return new ScdfForceTerminateBookingResponse(
                        new BookingResponseStatus(bIds, false, $"Force Terminate skipped: This BookingId doesn't exist - {bookingId}"));
                }

                await booking?.Status.Terminate()!;
            }

            await repo.ForceTerminateBooking(request.BookingIds, request.ForceTerminateClientUserId, request.BookingForceTerminateReasonId, request.BookingForceTerminateNotes);
            return new ScdfForceTerminateBookingResponse(new BookingResponseStatus(request.BookingIds, true, "Booking(s) has been Force Terminated"));
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error force terminate booking(s)", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfForceTerminateBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Unexpected error force terminate booking(s)", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfForceTerminateBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}