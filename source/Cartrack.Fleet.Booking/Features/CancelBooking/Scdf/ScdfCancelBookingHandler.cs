﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.Infratructure;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.CancelBooking.Scdf;

public class ScdfCancelBookingHandler(ScdfBookingRepository repo, ScdfVehicleBookingBuilder bookingBuilder, IHttpContextAccessor context, ILogger<ScdfCancelBookingHandler> logger)
    : IRequestHandler<ScdfCancelBookingRequest, ScdfCancelBookingResponse> {
    public async Task<ScdfCancelBookingResponse> Handle(ScdfCancelBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            // Validation
            foreach (var bookingId in request.BookingIds) {
                Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
                Requires.IsTrue(() => bookingId > 0, () => "Booking Id must be greater than zero");
                Requires.IsTrue(() => request.BookingCancelReasonId > 0, () => "Booking Cancel ReasonId must be greater than zero");
                Requires.IsTrue(() => !string.IsNullOrWhiteSpace(request.BookingCancelNotes), () => "Please provide a cancellation reason");

                logger.LogInformation("[{TraceId}] [{Agency}] Cancel booking with id {Id}", context.HttpContext?.TraceIdentifier ?? "", request.Account, bookingId);

                var booking = await repo.GetBooking(bookingBuilder, bookingId);
                if (booking is null) {
                    return new ScdfCancelBookingResponse(
                        new BookingResponseStatus([bookingId], false, "Cancel skipped: This booking doesn't exist"));
                }
                
                var cancelReason = await repo.GetBookingCancelReason(request.Account, request.BookingCancelReasonId);
                if (cancelReason is null) {
                    return new ScdfCancelBookingResponse(
                        new BookingResponseStatus([bookingId], false,
                            "Cancel skipped: This booking cancel reason id is not valid"));
                }
                
                //1. Update the existing booking with new information from the request
                await bookingBuilder.UpdateBookingDetails(request, repo);

                //2. Validate then Approve the booking
                await booking.Status.Cancel()!;
                await repo.CancelBooking(booking.BookingId, booking.BookingAuditReference ,request.CanceledClientUserId, request.BookingCancelReasonId, request.BookingCancelNotes);
            }
            
            return new ScdfCancelBookingResponse(
                new BookingResponseStatus(request.BookingIds.ToList(), true, "Booking has been canceled"));
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error cancelling booking", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfCancelBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Unexpected error cancelling booking", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfCancelBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}