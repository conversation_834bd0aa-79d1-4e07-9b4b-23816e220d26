﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.CancelBooking.Scdf;

public class ScdfCancelBookingRequest: IRequest<ScdfCancelBookingResponse> {
    [JsonIgnore] public string Account { get; set; } = "";
    public long[] BookingIds { get; set; } = [];
    public long BookingCancelReasonId { get; set; }
    public string BookingCancelNotes { get; set; } = "";
    public string CanceledClientUserId { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; }
}