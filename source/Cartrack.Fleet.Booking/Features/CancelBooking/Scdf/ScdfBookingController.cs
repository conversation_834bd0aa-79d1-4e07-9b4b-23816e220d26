﻿using Cartrack.Fleet.Booking.Features.CancelBooking.Scdf;
using Cartrack.Fleet.Booking.Infratructure;
using Cartrack.Fleet.Booking.IO.Http.CustomAuthorization;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpPost]
    [Route("cancel")]
    [AuthorizeIssuance(AuthorizeIssuanceCheck.CanCancelBooking)]
    public async Task<ActionResult<ScdfCancelBookingResponse>> CancelBooking([FromBody] ScdfCancelBookingRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.CanceledClientUserId = authClaims.ClientUserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfCancelBookingResponse, BookingResponseStatus>(this.HttpContext);
    }
}