﻿using Cartrack.Fleet.Booking.Domain.Common;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task CancelBooking(long bookingId, string bookingAuditReference, string canceledClientUserId, long bookingCancelReasonId ,string bookingCancelNotes) {
        var existsBooking = await this._poolDbContext.Bookings.FirstOrDefaultAsync(b => b.BookingId == bookingId);
        if (existsBooking is not null) {
            existsBooking.BookingStatusId = (long)BookingStatusCode.Cancelled;
            existsBooking.CanceledTs = DateTime.UtcNow;
            existsBooking.CanceledClientUserId = canceledClientUserId;
            existsBooking.BookingCancelReasonId = bookingCancelReasonId;
            existsBooking.BookingCancelNotes = bookingCancelNotes;
            existsBooking.UpdatedTs = DateTime.UtcNow;
            existsBooking.BookingAuditReference = bookingAuditReference;
            this._poolDbContext.Update(existsBooking);
            await this._poolDbContext.SaveChangesAsync();
        }
    }

    public async Task<BookingCancelReasonBase?> GetBookingCancelReason(string agency, long cancelReasonId) {
        Console.WriteLine($"[{agency}] Getting booking for {cancelReasonId}");

        var ctUser = await this._ctDbContext.Users.FirstOrDefaultAsync(c => c.UserName.Equals(agency));
        if (ctUser is null) {
            return null;
        }

        var bookingCancelReason = await this._poolDbContext.BookingCancelReasons
            .FirstOrDefaultAsync(b => b.BookingCancelReasonId == cancelReasonId && b.UserId == ctUser.UserId);
        if (bookingCancelReason != null) {
            var bookingCancelReasonBase = new BookingCancelReasonBase();
            bookingCancelReasonBase.BookingCancelReasonId = bookingCancelReason.BookingCancelReasonId;
            bookingCancelReasonBase.UserId = bookingCancelReason.UserId;
            bookingCancelReasonBase.BookingCancelReason1 = bookingCancelReason.BookingCancelReason1;
            bookingCancelReasonBase.IsDeleted = bookingCancelReason.IsDeleted;
            bookingCancelReasonBase.InternalDescription = bookingCancelReason.InternalDescription;
            return bookingCancelReasonBase;
        }
        else {
            return null;
        }
    }
}