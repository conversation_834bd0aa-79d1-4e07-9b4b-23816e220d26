﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.CT;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Common.States;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Journey = Cartrack.Fleet.Booking.Domain.Common.Journey;
using Vehicle = Cartrack.Fleet.Booking.Domain.Common.Vehicle;

namespace Cartrack.Fleet.Booking.Features.RejectBooking.Scdf;

// ReSharper disable once IdentifierTypo
public class ScdfRejectBookingHandler(
    ScdfBookingRepository repo,
    ScdfVehicleBookingBuilder bookingBuilder,
    IBookingRuleRepository ruleRepo,
    IHttpContextAccessor context,
    ILogger<ScdfRejectBookingHandler> logger)
    : IRequestHandler<ScdfRejectBookingRequest, ScdfRejectBookingResponse> {
    public async Task<ScdfRejectBookingResponse> Handle(ScdfRejectBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.BookingId != 0, () => "Booking Id is Required.");
            Requires.NotNullOrEmpty(request.BookingRejectReason, nameof(request.BookingRejectReason));
            Requires.IsTrue(() => request.BookingRejectReasonId != 0, () => "Booking rejection reason id is required.");
           
            logger.LogInformation("[{TraceId}] [{Agency}] Rejecting a booking", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var booking = await repo.GetBooking(bookingBuilder, request.BookingId);
            if (booking is null) {
                return new ScdfRejectBookingResponse(null, new Exception($"Booking {request.BookingId} not found")) { IsServerError = false };
            }
            
            //1. Update the existing booking with new information from the request
            await bookingBuilder.UpdateBookingDetails(request, repo, ruleRepo);

            //2. Validate then Approve the booking
            await booking.Status.Reject();
            
            //3. Save the booking
            var bookingId = await repo.RejectBooking(booking);
            var updatedBooking = await repo.GetBooking(request.Account!, bookingId);
            return new ScdfRejectBookingResponse(updatedBooking?.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error reject booking", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfRejectBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error reject booking", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfRejectBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}