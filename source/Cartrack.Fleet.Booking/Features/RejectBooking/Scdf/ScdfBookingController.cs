﻿using Cartrack.Fleet.Booking.Features.RejectBooking.Scdf;
using Cartrack.Fleet.Booking.IO.Http.CustomAuthorization;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

// ReSharper disable once IdentifierTypo
public partial class ScdfBookingController {
    [HttpPatch]
    [Route("reject")]
    [AuthorizeIssuance(AuthorizeIssuanceCheck.CanRejectBooking)]
    public async Task<ActionResult<ScdfRejectBookingResponse>> RejectBooking([FromBody] ScdfRejectBookingRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.RejectClientUserId = authClaims.ClientUserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfRejectBookingResponse, Booking>(this.HttpContext);
    }
}