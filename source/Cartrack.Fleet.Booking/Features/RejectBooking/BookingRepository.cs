﻿using Cartrack.EFCore.Models.Pool;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Microsoft.EntityFrameworkCore;
using Npgsql;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<long> RejectBooking(VehicleBookingBase booking) {
        long bookingId = booking.BookingId;
        var myBooking = await this._poolDbContext.Bookings.FindAsync(bookingId);
        if (myBooking == null) {
            return -1;
        }

        if (booking?.BookingStatusId != null) {
            myBooking.BookingStatusId = (long)booking.BookingStatusId;
        }
        
        myBooking.UpdatedTs = DateTime.UtcNow;
        myBooking.DecisionTs = DateTime.UtcNow;
        myBooking.IsApproved = false;
        
        var rejectedClientUserId = booking?.RejectedBy.Select(r => r.ClientUserId).FirstOrDefault();
        myBooking.DecisionClientUserId = rejectedClientUserId;
        myBooking.BookingAuditReference = booking?.BookingAuditReference;
        
        if (booking?.BookingRejectReasonId != null) {
            myBooking.BookingRejectReasonId = booking.BookingRejectReasonId;
        }
        if (booking?.BookingRejectReason != null) {
            myBooking.BookingRejectNotes = booking.BookingRejectReason;
        }
        
        await this._poolDbContext.SaveChangesAsync();

        await this.UpdateBookingApproval(booking!);
        
        return bookingId;
    }
}