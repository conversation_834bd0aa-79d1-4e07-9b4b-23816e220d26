﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.RejectBooking.Spf;

public class SpfRejectBookingHandler(IBookingRepository repo, IHttpContextAccessor context, ILogger<SpfRejectBookingHandler> logger)
    : IRequestHandler<SpfRejectBookingRequest, SpfRejectBookingResponse> {
    public async Task<SpfRejectBookingResponse> Handle(SpfRejectBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] rejecting a booking", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var booking = await repo.GetBooking(request.Account, request.BookingId);
            await booking?.Status.Approve()!;
            await repo.UpdateStatus(booking.BookingId, BookingStatusCode.Approved);
            var statusHttp = new BookingStatus(booking.BookingId, Constants.Approved);
            return new SpfRejectBookingResponse(statusHttp);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error reject booking", context.HttpContext?.TraceIdentifier ?? "");
            return new SpfRejectBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error reject booking", context.HttpContext?.TraceIdentifier ?? "");
            return new SpfRejectBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private SpfVehicleBooking ApproveFromRequest(SpfRejectBookingRequest request) {
        throw new NotImplementedException();
    }
}