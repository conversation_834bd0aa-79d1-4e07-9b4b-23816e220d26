﻿using Cartrack.Fleet.Booking.Features.RejectBooking.Spf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class SpfBookingController {
    [HttpPost]
    [Route("{id}/reject")]
    public async Task<ActionResult<SpfRejectBookingResponse>> RejectBooking(long id,
        [FromBody] SpfRejectBookingRequest request) {
        request.BookingId = id;
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<SpfRejectBookingResponse, BookingStatus>(this.HttpContext);
    }
}