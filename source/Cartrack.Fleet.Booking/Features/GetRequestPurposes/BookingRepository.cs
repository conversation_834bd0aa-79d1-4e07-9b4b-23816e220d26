﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Features.GetRequestPurposes;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<List<RequestPurpose>>
        GetRequestPurposes(long userId, long bookingPurposeId) {
        
        var query = this._poolDbContext.BookingPurposes
            .Where(x => x.UserId == userId);

        if (bookingPurposeId != 0) {
            query = query.Where(x => x.BookingPurposeId == bookingPurposeId);
        }
        
        var purposes = await query.ToListAsync() ?? null;
        
        if (purposes is null) {
            return new List<RequestPurpose>();
        }

        List<RequestPurpose> output = purposes.Select(p => new RequestPurpose {
            Id = p.BookingPurposeId,
            Title = p.BookingPurpose1 ?? string.Empty,
            Description = ""
        }).ToList();

        return output;
    }
}