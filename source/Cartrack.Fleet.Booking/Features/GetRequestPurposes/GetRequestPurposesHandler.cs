﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.GetRequestPurposes;

public class
    GetRequestPurposesHandler(IBookingRepository repo, IHttpContextAccessor context, ILogger<GetRequestPurposesHandler> logger)
    : IRequestHandler<GetRequestPurposesRequest, GetRequestPurposesResponse> {
    public async Task<GetRequestPurposesResponse> Handle(GetRequestPurposesRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.Offset >= 0, "Offset must be greater than or equal to zero");
            //Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));

            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving request purposes", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var result = await repo.GetRequestPurposes(request.UserId, request.BookingPurposeId);

            var apiRequestPurposes = result?.ToHttpRequestPurposes();
            return new GetRequestPurposesResponse(apiRequestPurposes);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving request purposes", context.HttpContext?.TraceIdentifier ?? "");
            return new GetRequestPurposesResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving request purposes", context.HttpContext?.TraceIdentifier ?? "");
            return new GetRequestPurposesResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}