﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Features.GetRequestPurposes;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Http;

public partial class BookingController {
    [HttpGet]
    [Route("requestpurposes")]
    public async Task<ActionResult<GetRequestPurposesResponse>> GetRequestPurposes() {
        var request = new GetRequestPurposesRequest();

        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = authClaims.ClientUserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetRequestPurposesResponse, RequestPurposes>(this.HttpContext);
    }
}