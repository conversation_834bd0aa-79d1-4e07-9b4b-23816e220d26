﻿using Cartrack.Fleet.Booking.Features.GetAdditionalLocations;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Http;

public partial class BookingController {
    [HttpGet]
    [Route("additionalLocations")]
    public async Task<ActionResult<GetAdditionalLocationsResponse>> GetAdditionalLocations() {
        var request = new GetAdditionalLocationsRequest();
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        //request.CanceledClientUserId = authClaims.ClientUserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetAdditionalLocationsResponse, AdditionalLocations>(this.HttpContext);
    }
}