﻿using MediatR;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.GetAdditionalLocations;

public record GetAdditionalLocationsRequest : IRequest<GetAdditionalLocationsResponse> {
    [JsonIgnore] public string? Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; } = 0;
}

public record DateRangeFilter {
    public DateTime? StartDate { get; init; }
    public DateTime? EndDate { get; init; }
}

public record SortOptions {
    private string _column = "requestedTime";
    private string _direction = "desc";

    public string Column {
        get => _column;
        init => _column = IsValidColumn(value) ? value : "requestedTime";
    }

    public string Direction {
        get => _direction;
        init => _direction = value?.ToLower() == "asc" ? "asc" : "desc";
    }

    private bool IsValidColumn(string? column) {
        return column != null && new[] {
            "pickupTime", "requestedTime", "returnedTime"
        }.Contains(column);
    }
}