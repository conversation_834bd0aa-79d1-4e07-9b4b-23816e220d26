﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.CT;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.Domain.Spf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Vehicle.IO.Http;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Data;

namespace Cartrack.Fleet.Booking.Features.CreateBooking.Spf;

public class SpfCreateBookingHandler(IBookingRepository repo, IHttpContextAccessor context, IBookingRuleRepository rule, IDriverRepository driverRepo,
    IVehicleRepository vehicleRepo, ILogger<SpfCreateBookingHandler> logger)
    : IRequestHandler<SpfCreateBookingRequest, SpfCreateBookingResponse> {
    public async Task<SpfCreateBookingResponse> Handle(SpfCreateBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            Requires.IsTrue(() => request.BookingTypeId > 0, "BookingTypeId must be greater than zero");
            logger.LogInformation("[{TraceId}] [{Agency}] Creating a booking", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            VehicleBookingBase booking = await this.CreateFromRequest(request);
            var bookingId = await repo.CreateBooking(booking);
            var createdBooking = await repo.GetBooking(request.Account, bookingId);
            return new SpfCreateBookingResponse(createdBooking.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error creating booking", context.HttpContext?.TraceIdentifier ?? "");
            return new SpfCreateBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error creating booking", context.HttpContext?.TraceIdentifier ?? "");
            return new SpfCreateBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private async Task<SpfVehicleBooking> CreateFromRequest(SpfCreateBookingRequest request) {
        //1. Create all the booking rules that applies to SPF
        VehicleTarget vehicleTarget = null;
        if (request.VehicleId != null) {
            var vInfo = await vehicleRepo.GetVehicleById(request.UserId, request.VehicleId ?? 0);
            vehicleTarget = new VehicleTarget {
                Vehicle = new Domain.Common.Vehicle {
                    VehicleId = vInfo.VehicleId,
                    Registration = vInfo!.Registration ?? "",
                    CommonPool = vInfo.CommonPool ?? false,
                    CarpoolEnabled = vInfo.IsPoolActive ?? false
                }
            };
        }

        var booking = new SpfVehicleBooking() {
            UserId = request.UserId,
            CancellationTrigger = null,
            BookingStatusId = BookingStatusCode.Requested,
            BookingPurposeId = request.BookingPurposeId,
            BookingPurposeDescription = request.BookingPurposeDescription,
            BookingVehicleTypeId = request.BookingVehicleTypeId,
            StartDate = DateTime.SpecifyKind(request.StartTs, DateTimeKind.Utc),
            EndDate = DateTime.SpecifyKind(request.EndTs, DateTimeKind.Utc),
            RequestClientDriverId = request.RequestClientDriverId,
            PickupSiteLocationId = request.PickupSiteLocationId,
            ReturnSiteLocationId = request.ReturnSiteLocationId, // in SCDF not used. All will be in 'Journeys'
            Description = null,
            Purpose = null,
            Target = vehicleTarget,
            CreatedDate = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc),
            Type = (BookingType)request.BookingTypeId,
            RequestClientUserId = request.RequestClientUserId,
            RequestedBy = new ClientUser(){ ClientUserId = request.RequestClientUserId },
            RequestedDate = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc),
            ActivationTrigger = null,
            VehicleId = request.VehicleId,
            Driver = new Domain.Common.Driver {
                DriverId = request.RequestClientDriverId!,
                DriverDepartments = await driverRepo.GetDepartmentsByDriverId(request.UserId, request.RequestClientDriverId),
                DriverQdlLicense = await driverRepo.GetQdlLicensesById(request.RequestClientDriverId),
                DriverPdpLicense = await driverRepo.GetPdpLicensesById(request.RequestClientDriverId),
            }
        };

        foreach (var r in await this.GetSpfBookingRules(request.UserId)) {
            booking.PreConditions.Add(r);
        }


        if (request.VehicleId != null) {
            booking.IsBookingTimeConflicting = await this.IsBookingExistsForVehicle(booking.VehicleId.Value, booking.StartDate, booking.EndDate);
            booking.IsVehicleCommonPoolRuleAllowed = await this.IsVehicleCommonPoolRuleAllowed(booking);
            booking.IsDriverAndVehiclePdpLicesnsesMatched = await this.CheckDriverAndVehiclePdpLicense(booking);
            booking.IsDriverAndVehicleQdlLicesnsesMatched = await this.CheckDriverAndVehicleQdlLicense(booking);           
        }

        if (request.BookingVehicleTypeId != null) {
            var vehiclesBase = await vehicleRepo.GetAllVehiclesByVehicleCategory(request.UserId, request.BookingVehicleTypeId.Value);
            var vehicleIds = vehiclesBase.Select(x=>x.VehicleId).ToList();
            booking.IsBookingTimeConflicting = await this.IsBookingExistsForVehicleByCategory(booking, vehicleIds);
            booking.IsDriverAndVehiclePdpLicesnsesMatched = await this.CheckDriverAndVehiclePdpLicenseByCategory(booking, vehicleIds);
            booking.IsDriverAndVehicleQdlLicesnsesMatched = await this.CheckDriverAndVehicleQdlLicenseByCategory(booking, vehicleIds);
            booking.IsVehicleCommonPoolRuleAllowed = await this.IsVehicleCommonPoolRuleAllowedByCategory(booking, vehiclesBase);
        }
        booking.IsBookingTimeConflictingForDriver = await this.IsBookingExistsFoDriver(booking.RequestClientDriverId, booking.StartDate, booking.EndDate);


        //foreach (var r in this.GetSpfBookingRules()) {
        //    booking.PreConditions.Add(r);
        //}

        //2. Add the journey. For SPF, StartLocation == DropOffLocation
        //booking.Journeys.Add(new Journey()); 

        //3. Validate the booking.  Execute all the pre-conditions
        await booking.Validate();
        return booking;
    }

    private async Task<List<IBookingRule>> GetSpfBookingRules(long userId) {
        var (userBookingRule, bookingSettings) = await rule.GetBookingSettingRules(userId);

        List<IBookingRule> bookingRule = [];

        foreach (var r in userBookingRule) {
            switch (r.BookingRuleId) {
                case Constants.BookingRuleCodeCheckDriverLicenseClass:
                    bookingRule.Add(new MustHaveSpecialLicenseRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeCheckDriverSpecialLicense:
                    bookingRule.Add(new MustHaveQdlLicenseRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeIsDriverRequired:
                    bookingRule.Add(new MustHaveDriverRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeMaximumBookingTime:
                    bookingRule.Add(new MustBeLessThanMaxDurationRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeBookInAdvanceBy:
                    bookingRule.Add(new MustBeLessThanMaxDaysInAdvanceRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
            }            
        }
        bookingRule.Add(new MustHaveValidBookingTime(bookingSettings) {
            IsEnabled = true
        });

        bookingRule.Add(new IsVehicleInCommonPoolRule(bookingSettings) {
            IsEnabled = true
        });

        bookingRule.Add(new MustMatchDriverAndVehiclePdpLicense(bookingSettings) {
            IsEnabled = true
        });

        bookingRule.Add(new MustMatchDriverAndVehicleQdlLicense(bookingSettings) {
            IsEnabled = true
        });

        bookingRule.Add(new MustHaveValidBookingTimeForDriver(bookingSettings) {
            IsEnabled = true
        });
        
        return bookingRule;
    }

    private List<IBookingRule> GetCommonBookingRules() {
        return [];
    }

    private async Task<bool> IsBookingExistsForVehicle(long VehicleId, DateTime StartDate, DateTime EndDate) {
        var result = await repo.GetBooingByVehicleId(VehicleId);
        if (result.Any()) {
            var isBookingExists = result.Any(b =>
                    ((StartDate >= b.StartDate && StartDate <= b.EndDate) || (EndDate >= b.StartDate && EndDate <= b.EndDate) || (StartDate <= b.StartDate && EndDate >= b.EndDate))
                    && 
                    (
                    b.BookingStatusId == BookingStatusCode.Active ||
                    b.BookingStatusId == BookingStatusCode.Requested ||
                    b.BookingStatusId == BookingStatusCode.Approved ||
                    b.BookingStatusId == BookingStatusCode.ExpiringApproval ||
                    b.BookingStatusId == BookingStatusCode.ActiveLate
                    )
                    );
            if (isBookingExists) {
                return true;
            }
            else {
                return false;
            }
        }
        return false;
    }

    private async Task<bool> IsBookingExistsFoDriver(string clientDriverId, DateTime StartDate, DateTime EndDate) {
        var result = await repo.GetBooingByDriverId(clientDriverId);
        if (result.Any()) {
            var isBookingExists = result.Any(b =>
                    ((StartDate >= b.StartDate && StartDate <= b.EndDate) || ( EndDate >= b.StartDate && EndDate <= b.EndDate) || (StartDate <= b.StartDate && EndDate >= b.EndDate)) 
                    && 
                    (
                    b.BookingStatusId == BookingStatusCode.Active ||
                    b.BookingStatusId == BookingStatusCode.Requested ||
                    b.BookingStatusId == BookingStatusCode.Approved ||
                    b.BookingStatusId == BookingStatusCode.ExpiringApproval ||
                    b.BookingStatusId == BookingStatusCode.ActiveLate
                    )
                    );
            if (isBookingExists) {
                return true;
            }
            else {
                return false;
            }
        }
        return false;
    }

    private async Task<bool> IsBookingExistsForVehicleByCategory(VehicleBookingBase booking, List<long> vehicleIds) {
        bool isBookingExists = false;
        foreach (var vehicleId in vehicleIds) {
            isBookingExists = await IsBookingExistsForVehicle(vehicleId, booking.StartDate, booking.EndDate);
            if (!isBookingExists) {
                break;
            }
        }
        return isBookingExists;
    }

    private async Task<bool> IsVehicleCommonPoolRuleAllowed(VehicleBookingBase booking) {
        var clientDriverDepartments = await driverRepo.GetDepartmentsByDriverId(booking.UserId, booking.RequestClientDriverId ?? "");
        var vehicleDepartments = await vehicleRepo.GetDepartments(booking.UserId, booking.VehicleId ?? 0);
        if (booking.Vehicle.CommonPool == false) {
            var matchFound = clientDriverDepartments
                .Any(driver => vehicleDepartments
                .Any(vehicle => driver.Id == vehicle.Id));
            return matchFound;
        }
        return true;
    }

    private async Task<bool> IsVehicleCommonPoolRuleAllowedByCategory(VehicleBookingBase booking, List<VehicleBase> vehicleBases) {
        var clientDriverDepartments = await driverRepo.GetDepartmentsByDriverId(booking.UserId, booking.RequestClientDriverId ?? "");
        bool anyMatchFound = false;
        foreach (var vehicleBase in vehicleBases) {
            var vehicleDepartments = await vehicleRepo.GetDepartments(booking.UserId, vehicleBase.VehicleId);
            if (vehicleBase.CommonPool == false) {
                anyMatchFound = clientDriverDepartments
                    .Any(driver => vehicleDepartments
                    .Any(vehicle => driver.Id == vehicle.Id));
                if (anyMatchFound) {
                    break;
                }
            }
        }
        return anyMatchFound;
    }

    private async Task<bool> CheckDriverAndVehiclePdpLicense(VehicleBookingBase booking) {
        var clientDriverPdpLicenses = await driverRepo.GetPdpLicensesById(booking.RequestClientDriverId);
        var vehiclePdpLicenses = await vehicleRepo.GetPdpLicensesById(booking.UserId, booking.VehicleId ?? 0);
        //var matchFound = clientDriverPdpLicenses
        //    .Select(x => x.LicenseTypeId)
        //    .OrderBy(id => id)
        //    .SequenceEqual(
        //        vehiclePdpLicenses
        //        .Select(x => x.LicenseTypeId)
        //        .OrderBy(id => id)
        //        );
        var clientLicenseIds = clientDriverPdpLicenses.Select(x => x.LicenseTypeId).ToHashSet();
        var vehicleLicenseIds = vehiclePdpLicenses.Select(x => x.LicenseTypeId).ToHashSet();

        var allMatchFound = clientLicenseIds.All(id => vehicleLicenseIds.Contains(id));
        return allMatchFound;
    }

    private async Task<bool> CheckDriverAndVehiclePdpLicenseByCategory(VehicleBookingBase booking, List<long> vehicleIds) {
        var clientDriverPdpLicenses = await driverRepo.GetPdpLicensesById(booking.RequestClientDriverId);
        bool allMatchFound = false;
        foreach (var vehicleId in vehicleIds) {
            var vehiclePdpLicenses = await vehicleRepo.GetPdpLicensesById(booking.UserId, vehicleId);

            //anyMatchFound = clientDriverPdpLicenses
            //.Select(x => x.LicenseTypeId)
            //.OrderBy(id => id)
            //.SequenceEqual(
            //    vehiclePdpLicenses
            //    .Select(x => x.LicenseTypeId)
            //    .OrderBy(id => id)
            //    );

            var clientLicenseIds = clientDriverPdpLicenses.Select(x => x.LicenseTypeId).ToHashSet();
            var vehicleLicenseIds = vehiclePdpLicenses.Select(x => x.LicenseTypeId).ToHashSet();

            allMatchFound = clientLicenseIds.All(id => vehicleLicenseIds.Contains(id));

            if (allMatchFound) {
                break;
            }
        }
        return allMatchFound;
    }
    private async Task<bool> CheckDriverAndVehicleQdlLicense(VehicleBookingBase booking) {
        var clientDriverQdlLicenses = await driverRepo.GetQdlLicensesById(booking.RequestClientDriverId);
        var vehicleQdlLicenses = await vehicleRepo.GetQdlLicensesById(booking.UserId, booking.VehicleId ?? 0);
        //var matchFound = clientDriverQdlLicenses
        //    .Select(x => x.LicenseTypeId)
        //    .OrderBy(id => id)
        //    .SequenceEqual(
        //        vehicleQdlLicenses
        //        .Select(x => x.LicenseTypeId)
        //        .OrderBy(id => id)
        //        );
        var clientLicenseIds = clientDriverQdlLicenses.Select(x => x.LicenseTypeId).ToHashSet();
        var vehicleLicenseIds = vehicleQdlLicenses.Select(x => x.LicenseTypeId).ToHashSet();

        var allMatchFound = clientLicenseIds.All(id => vehicleLicenseIds.Contains(id));

        return allMatchFound;
    }

    private async Task<bool> CheckDriverAndVehicleQdlLicenseByCategory(VehicleBookingBase booking, List<long> vehicleIds) {
        var clientDriverQdlLicenses = await driverRepo.GetQdlLicensesById(booking.RequestClientDriverId);
        bool allMatchFound = false;
        foreach (var vehicleId in vehicleIds) {
            var vehicleQdlLicenses = await vehicleRepo.GetQdlLicensesById(booking.UserId, vehicleId);
            //anyMatchFound = clientDriverQdlLicenses
            //.Select(x => x.LicenseTypeId)
            //.OrderBy(id => id)
            //.SequenceEqual(
            //    vehicleQdlLicenses
            //    .Select(x => x.LicenseTypeId)
            //    .OrderBy(id => id)
            //    );
            //if (anyMatchFound) {
            //    break;
            //}

            var clientLicenseIds = clientDriverQdlLicenses.Select(x => x.LicenseTypeId).ToHashSet();
            var vehicleLicenseIds = vehicleQdlLicenses.Select(x => x.LicenseTypeId).ToHashSet();

            allMatchFound = clientLicenseIds.All(id => vehicleLicenseIds.Contains(id));

            if (allMatchFound) {
                break;
            }
        }
        return allMatchFound;
    }
}