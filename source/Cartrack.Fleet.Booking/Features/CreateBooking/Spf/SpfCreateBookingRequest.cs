﻿using Cartrack.Fleet.Booking.Domain.Common;
using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.CreateBooking.Spf;

public record SpfCreateBookingRequest : IRequest<SpfCreateBookingResponse> {
    [JsonIgnore] public string Account { get; set; } = "";

    [JsonIgnore] public long UserId { get; set; }
    public long BookingPurposeId { get; set; }
    public string? BookingPurposeDescription { get; set; }
    public string? RequestClientUserId { get; set; }    
    public long? BookingVehicleTypeId { get; set; }
    public long? VehicleId { get; set; }
    public DateTime StartTs { get; set; }
    public DateTime EndTs { get; set; }
    public string? RequestClientDriverId { get; set; }
    public long? PickupSiteLocationId { get; set; }
    public long? ReturnSiteLocationId { get; set; }
    public long BookingTypeId { get; set; }
}