﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Http.Filters;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using BookingAccessory = Cartrack.Fleet.Booking.Domain.Common.BookingAccessory;

namespace Cartrack.Fleet.Booking.Features.CreateBooking.Scdf;

public class ScdfCreateBookingHandler(
    ScdfBookingRepository repo,
    ScdfVehicleBookingBuilder bookingBuilder,
    IBookingRuleRepository rule,
    IHttpContextAccessor context,
    ILogger<ScdfCreateBookingHandler> logger) : IRequestHandler<ScdfCreateBookingRequest, ScdfCreateBookingResponse> {
    
    public async Task<ScdfCreateBookingResponse> Handle(ScdfCreateBookingRequest request, CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.BookingPurposeId != 0, () => "Purpose of Request is Required.");
            Requires.IsTrue(() => request.EndTs is { } time, () => "End Date is Required.");
            Requires.IsTrue(() => request.StartTs is { } time, () => "Start Date is Required.");
            Requires.IsTrue(() => request.EndTs >= request.StartTs, () => "End Date must be greater than Start Date.");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            Requires.IsTrue(() => !string.IsNullOrWhiteSpace(request.RequestClientUserId), () => "Request Client UserId is required when creating a booking");
            Requires.IsTrue(MustHaveDriverWhenDTypeIsSpecific, () => "Driver is required when DriverType is Specific");
            Requires.IsTrue(MustHaveVehicleCommanderWhenTypeIsSpecific, () => "VehicleCommander is required when VehicleCommanderType is Specific");

            logger.LogInformation("[{TraceId}] [{Agency}] Creating a new booking, ClientUserId: {ClientUserId}",
                context.HttpContext?.TraceIdentifier ?? "",
                request.Account,
                request.RequestClientUserId);

            var bookingContext = context.GetBookingContext()!;
            await bookingBuilder.Start(0);
            var booking = (ScdfVehicleBooking)bookingBuilder.VehicleBooking!;
            await bookingBuilder.UpdateBookingDetails(request, repo, rule, bookingContext);
            await booking.Validate();
            var bookingId = await repo.CreateBooking(booking);
            await repo.AddOtherScdfBookingInfo(booking, bookingId);
            var createdBooking = await repo.GetBooking(request.Account, bookingId);
            logger.LogInformation("[{TraceId}] [{Agency}] Created booking {BookingId}", context.HttpContext?.TraceIdentifier ?? "", request.Account, bookingId);

            Requires.IsTrue(() => createdBooking != null, () => "Unable to create a booking.");
            return new ScdfCreateBookingResponse(createdBooking!.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfCreateBookingResponse(null, ex) { IsServerError = false };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error creating booking", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfCreateBookingResponse(null, ex) { IsServerError = true };
        }

        bool MustHaveDriverWhenDTypeIsSpecific() {
            if (request.DriverType == DriverType.Specific) {
                return !string.IsNullOrWhiteSpace(request.RequestClientDriverId);
            }

            return true;
        }
        
        bool MustHaveVehicleCommanderWhenTypeIsSpecific() {
            if (request.VehicleCommanderType == VehicleCommanderType.Specific) {
                return !string.IsNullOrWhiteSpace(request.VehicleCommanderClientUserId);
            }

            return true;
        }
    }
}