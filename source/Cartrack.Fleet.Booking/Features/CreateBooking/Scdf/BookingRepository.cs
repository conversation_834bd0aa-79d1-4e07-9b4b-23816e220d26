﻿using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Microsoft.EntityFrameworkCore;
using Npgsql;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task AddOtherScdfBookingInfo(VehicleBookingBase booking, long bookingId) {
       
        /////////////////
        // Booking Metric
        /////////////////
        /*var bookingMetric = new EFCore.Models.Pool.BookingMetric() {
            BookingId = bookingId,
        };
        await poolDbContext.BookingMetrics.AddAsync(bookingMetric);
        await poolDbContext.SaveChangesAsync();*/

        var bookingAuditReference = booking.BookingAuditReference ?? "";
        
        // Adding into SCDF Custom Booking Additional Info
        var scdfBookingAdditionalInfo = new ScdfBookingAdditionalInfo {
            BookingId = bookingId,
            PassengerCount = booking.NumberOfPassengers,
            VehicleCommanderClientUserId = booking.VehicleCommanderClientUserId,
            RequestedForClientUserId = booking.RequestedForClientUserId,
            LocationType = (int)booking.JourneyType,
            DriverType = (int)booking.DriverType!,
            VehicleCommanderType = (int)booking.VehicleCommanderType!,
            EquipmentType = (int)booking.EquipmentType1!,
            BookingAuditReference = bookingAuditReference
        };
        await this._tfmsCustomDbContext.ScdfBookingAdditionalInfos.AddAsync(scdfBookingAdditionalInfo);
        await this._tfmsCustomDbContext.SaveChangesAsync();

        // Journey Related
        await this.AddOrUpdateBookingJourneys(booking.UserId, bookingId, bookingAuditReference, booking.Journeys);

        // Accessories Related
        if (booking.AccessoryIds is { Count: > 0 }) {
            await this.AddOrUpdateBookingAccessory(bookingId, bookingAuditReference, booking.AccessoryIds);
        }

        // Booking Attachment
        if (booking.EquipmentAttachments is { Count: > 0 }) {
            await this.AddBookingEquipmentAttachments(bookingId, bookingAuditReference, booking.EquipmentAttachments);
        }

        // Create Booking Approval
        //await this.CreateBookingApproval(booking);

        //return bookingId;
    }
}