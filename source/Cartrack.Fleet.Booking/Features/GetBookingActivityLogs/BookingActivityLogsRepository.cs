﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.IO.Http;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingActivityLogsRepository {
    public async Task<BookingActivityLogs> GetBookingActivityLogs(long userId, long bookingId) {
        var booking = await poolDbContext.Bookings.FirstOrDefaultAsync(b => b.BookingId == bookingId);
        
        var bookingStatus = await poolDbContext.BookingStatuses
            .ToDictionaryAsync(key => key.BookingStatusId, value => value.BookingStatus1);
        
        var logs = await tfmsCustomDbContext.BookingActivityLogs
            .Where(b => b.UserId == userId && b.BookingId == bookingId)
            .OrderBy(a => a.AuditTs)
            .ToListAsync();
        
        var usernames = logs.Select(a => a.AuditUsername).Distinct().ToList();
        
        var clientUsers = await fleetDbContext.ClientUsers
            .Where(cu => usernames.Contains(cu.UserName))
            .ToDictionaryAsync(cu => cu.UserName, cu => cu.ClientUserId.ToString());
        
        var activities = logs.Select(a => new ActivityLogEntry {
            AuClientUser = new ClientUser {
                ClientUserId = clientUsers.TryGetValue(a.AuditUsername, out var id) ? id : null,
                Username = a.AuditUsername
            },
            AuTimestamp = a.AuditTs,
            AuAction = JsonSerializer.Deserialize<ActivityLogAction>(a.Jsondata)
        }).ToList();

        var bookingActivityLogs = new BookingActivityLogs {
            BookingId = booking!.BookingId,
            BookingStatus = bookingStatus.GetValueOrDefault(booking.BookingStatusId),
            Activities = activities
        };
        
        return bookingActivityLogs ?? null;
    }
}