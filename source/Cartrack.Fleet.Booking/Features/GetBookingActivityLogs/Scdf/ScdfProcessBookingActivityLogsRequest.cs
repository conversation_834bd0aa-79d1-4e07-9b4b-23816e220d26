﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.GetBookingActivityLogs.Scdf;

// ReSharper disable once IdentifierTypo
public record ScdfGetBookingActivityLogsRequest(long BookingId) : IRequest<ScdfGetBookingActivityLogsResponse> {
    [JsonIgnore] public string? Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; } = 0;
    [JsonIgnore] public long BookingId { get; set; } = BookingId;
    [JsonIgnore] public string ClientUserId { get; set; } = "";
}