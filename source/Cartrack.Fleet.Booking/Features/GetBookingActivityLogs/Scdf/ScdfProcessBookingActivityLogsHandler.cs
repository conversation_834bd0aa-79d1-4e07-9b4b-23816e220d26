﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

// ReSharper disable once IdentifierTypo
namespace Cartrack.Fleet.Booking.Features.GetBookingActivityLogs.Scdf;

// ReSharper disable once IdentifierTypo
public class ScdfGetBookingActivityLogsHandler( IBookingRepository repo, 
    IBookingActivityLogsRepository activityLogsRepo, 
    IHttpContextAccessor context,
    ILogger<ScdfGetBookingActivityLogsHandler> logger) : IRequestHandler<ScdfGetBookingActivityLogsRequest, ScdfGetBookingActivityLogsResponse> {
    public async Task<ScdfGetBookingActivityLogsResponse> Handle(ScdfGetBookingActivityLogsRequest request, CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.BookingId > 0, "Booking Id must be greater than zero");
            logger.LogInformation("[{TraceId}] [{Agency}] Get Booking Activity Logs", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var processRunActivityLog = await activityLogsRepo.RunProcessActivityLogs(request.UserId, request.BookingId);
            var activityLogs = await activityLogsRepo.GetBookingActivityLogs(request.UserId, request.BookingId);
            return new ScdfGetBookingActivityLogsResponse(activityLogs?.ToHttpBookingActivityLogs());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error getting booking activity logs", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfGetBookingActivityLogsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error getting booking activity logs", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfGetBookingActivityLogsResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    /*private async Task<ScdfVehicleBooking> BookingActivityLogsFromRequest(ScdfProcessBookingActivityLogsRequest request) {
        var getCurrentBooking = await repo.GetBooking(request.Account, request.BookingId);

        var booking = new ScdfVehicleBooking {
            BookingId = request.BookingId,
            BookingStatusId = (request.ActualDropOffTime > request.DropOffTime) ? BookingStatusCode.ReturnedLate : BookingStatusCode.Returned,
            Status = getCurrentBooking!.Status,
            UpdatedDate = DateTime.UtcNow,
            VehicleId = request.VehicleId,
            EndDate = request.DropOffTime,
            ReturnedIgnitionTs = request.ActualDropOffTime,
            ReturnedClientDriverId = request.ReturnedClientDriverId,
            ReturnedVehicleCommanderClientUserId = request.ReturnedVehicleCommanderClientUserId,
            ReturnedClientUserId = request.CompletedClientUserId,
            CompletedClientUserId = request.CompletedClientUserId
        };
        
        
        await booking.Validate();

        return booking;
    }*/

}