﻿using Cartrack.Fleet.Booking.Features.GetBookingActivityLogs.Scdf;
using Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Cartrack.Fleet.Booking.IO.Http;

// ReSharper disable once IdentifierTypo
public partial class ScdfBookingController {
    [HttpGet]
    [Route("{bookingId:long}/activity-logs")]
    public async Task<ActionResult<ScdfGetBookingActivityLogsResponse>> ScdfGetBookingActivityLogs(long bookingId) {
        var request = new ScdfGetBookingActivityLogsRequest(bookingId);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = authClaims.ClientUserId;
     
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfGetBookingActivityLogsResponse, BookingActivityLogs>(this.HttpContext);
    }
}