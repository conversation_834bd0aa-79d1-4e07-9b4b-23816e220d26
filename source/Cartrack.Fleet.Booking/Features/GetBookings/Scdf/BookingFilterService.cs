using Cartrack.Fleet.Common.IO.Sql;

namespace Cartrack.Fleet.Booking.Features.GetBookings.Scdf;

public class BookingFilterService(
    AppTfmsCustomDbContext tfmsCustomDbContext,
    AppPoolDbContext poolDbContext,
    AppFleetDbContext fleetDbContext,
    AppCtDbContext ctDbContext
) : IBookingFilterService {
    public IQueryable<EFCore.Models.Pool.Booking> ApplyFilterItem(IQueryable<EFCore.Models.Pool.Booking> query,
        FilterItem item, long userId) {
        // Apply filter based on the field and operator type
        return item switch {
            SingleStringStringFilterItem stringItem when !string.IsNullOrEmpty(stringItem.Value) => this
                .ApplySingleStringFilter(query, stringItem, userId),
            ArrayStringFilterItem { Value.Length: > 0 } arrayItem => this
                .ApplyArrayStringFilter(query, arrayItem, userId),
            EmptyStringFilterItem emptyItem => this.ApplyEmptyStringFilter(query, emptyItem),
            DateRangeFilterItem dateRangeItem => this.ApplyDateRangeFilter(query, dateRangeItem),
            _ => query // If the filter is invalid, return the original query
        };
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplySingleStringFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        SingleStringStringFilterItem item,
        long userId) {
        var isCaseSensitive = item.Case == FilterCaseSensitivity.sensitive;
        var value = item.Value;

        if (!isCaseSensitive) {
            value = value.ToLower();
        }

        return item.ParsedField switch {
            StringFilterField.vehicle => this.ApplyVehicleFilter(query, item.StringOperator, value, isCaseSensitive, userId),
            StringFilterField.driver => this.ApplyDriverFilter(query, item.StringOperator, value, isCaseSensitive, userId),
            StringFilterField.driverEmail => this.ApplyDriverEmailFilter(query, item.StringOperator, value, isCaseSensitive, userId),
            StringFilterField.vehicleType => this.ApplyVehicleTypeFilter(query, item.StringOperator, value, isCaseSensitive),
            StringFilterField.purpose => this.ApplyPurposeFilter(query, item.StringOperator, value, isCaseSensitive),
            StringFilterField.vehicleCommanderEmail => this.ApplyVehicleCommanderEmailFilter(query, item.StringOperator, value,
                isCaseSensitive, userId),
            StringFilterField.requestorEmail =>
                this.ApplyRequestorEmailFilter(query, item.StringOperator, value, isCaseSensitive, userId),
            _ => query // Invalid field
        };
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyArrayStringFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        ArrayStringFilterItem item,
        long userId) {
        string[] values = item.Value;
        bool isCaseSensitive = item.Case == FilterCaseSensitivity.sensitive;

        if (!isCaseSensitive) {
            values = values.Select(v => v.ToLower()).ToArray();
        }

        // Only supporting isAnyOf operator for arrays
        return item.ParsedField switch {
            StringFilterField.vehicle => this.ApplyVehicleArrayFilter(query, values, isCaseSensitive, userId),
            StringFilterField.driver => this.ApplyDriverArrayFilter(query, values, isCaseSensitive, userId),
            StringFilterField.driverEmail => this.ApplyDriverEmailArrayFilter(query, values, isCaseSensitive, userId),
            StringFilterField.vehicleType => this.ApplyVehicleTypeArrayFilter(query, values, isCaseSensitive),
            StringFilterField.purpose => this.ApplyPurposeArrayFilter(query, values, isCaseSensitive),
            StringFilterField.requestorEmail => this.ApplyRequestorEmailArrayFilter(query, values, isCaseSensitive, userId),
            StringFilterField.vehicleCommanderEmail => this.ApplyVehicleCommanderEmailArrayFilter(query, values, isCaseSensitive, userId),
            _ => query // Invalid field
        };
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyEmptyStringFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        EmptyStringFilterItem item) {
        var checkIsEmpty = item.EmptyOperator == EmptyOperators.isEmpty;

        return item.ParsedField switch {
            StringFilterField.vehicle => query.Where(b => checkIsEmpty ? !b.VehicleId.HasValue : b.VehicleId.HasValue),
            StringFilterField.driver => query.Where(b =>
                checkIsEmpty
                    ? string.IsNullOrEmpty(b.RequestClientDriverId)
                    : !string.IsNullOrEmpty(b.RequestClientDriverId)),
            StringFilterField.vehicleType => query.Where(b =>
                checkIsEmpty ? !b.BookingVehicleTypeId.HasValue : b.BookingVehicleTypeId.HasValue),
            StringFilterField.purpose => query.Where(b => checkIsEmpty ? !b.BookingPurposeId.HasValue : b.BookingPurposeId.HasValue),
            StringFilterField.requestorEmail => query.Where(b =>
                checkIsEmpty ? string.IsNullOrEmpty(b.Requestor) : !string.IsNullOrEmpty(b.Requestor)),
            StringFilterField.driverEmail => query.Where(b =>
                checkIsEmpty
                    ? string.IsNullOrEmpty(b.RequestClientDriverId) // Assuming driver email implies a driver is assigned
                    : !string.IsNullOrEmpty(b.RequestClientDriverId)),
            StringFilterField.vehicleCommanderEmail => query.Where(b =>
                checkIsEmpty
                    ? !tfmsCustomDbContext.ScdfBookingAdditionalInfos.Any(info =>
                        info.BookingId == b.BookingId && !string.IsNullOrEmpty(info.VehicleCommanderClientUserId))
                    : tfmsCustomDbContext.ScdfBookingAdditionalInfos.Any(info =>
                        info.BookingId == b.BookingId && !string.IsNullOrEmpty(info.VehicleCommanderClientUserId))),
            _ => query // Invalid field
        };
    }

    #region String Filter Implementations

    private IQueryable<EFCore.Models.Pool.Booking> ApplyVehicleFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        StringOperators op,
        string value,
        bool isCaseSensitive,
        long userId) {
        // First, find matching vehicles
        var vehicleQuery = ctDbContext.Vehicles.Where(u => u.UserId == userId);

        // Apply string operation on vehicle registration and materialize the results
        var matchingVehicleIds = op switch {
            StringOperators.contains => vehicleQuery.Where(v => isCaseSensitive
                    ? v.Registration.Contains(value)
                    : v.Registration.ToLower().Contains(value))
                .Select(v => v.VehicleId)
                .ToList(),

            StringOperators.equals => vehicleQuery.Where(v => isCaseSensitive
                    ? v.Registration == value
                    : v.Registration.ToLower() == value)
                .Select(v => v.VehicleId)
                .ToList(),

            StringOperators.startsWith => vehicleQuery.Where(v => isCaseSensitive
                    ? v.Registration.StartsWith(value)
                    : v.Registration.ToLower().StartsWith(value))
                .Select(v => v.VehicleId)
                .ToList(),

            StringOperators.endsWith => vehicleQuery.Where(v => isCaseSensitive
                    ? v.Registration.EndsWith(value)
                    : v.Registration.ToLower().EndsWith(value))
                .Select(v => v.VehicleId)
                .ToList(),

            _ => new List<long>() // Empty result set for unknown operator
        };

        // Then filter bookings by matching vehicle IDs
        return query.Where(b => b.VehicleId.HasValue && matchingVehicleIds.Contains(b.VehicleId.Value));
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyDriverFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        StringOperators op,
        string value,
        bool isCaseSensitive,
        long userId) {
        // Find matching drivers (combining name and surname fields)
        var driverQuery = fleetDbContext.ClientDrivers.Where(u => u.UserId == userId);

        // Apply string operation on the driver name or surname
        var matchingDriverIds = op switch {
            StringOperators.contains => isCaseSensitive
                ? driverQuery.Where(d =>
                        d.DriverName.Contains(value) ||
                        (d.DriverSurname != null && d.DriverSurname.Contains(value)))
                    .Select(d => d.ClientDriverId).ToList()
                : driverQuery.Where(d =>
                        d.DriverName.ToLower().Contains(value) ||
                        (d.DriverSurname != null && d.DriverSurname.ToLower().Contains(value)))
                    .Select(d => d.ClientDriverId).ToList(),

            StringOperators.equals => isCaseSensitive
                ? driverQuery.Where(d =>
                        d.DriverName == value ||
                        (d.DriverSurname != null && d.DriverSurname == value))
                    .Select(d => d.ClientDriverId).ToList()
                : driverQuery.Where(d =>
                        d.DriverName.ToLower() == value ||
                        (d.DriverSurname != null && d.DriverSurname.ToLower() == value))
                    .Select(d => d.ClientDriverId).ToList(),

            StringOperators.startsWith => isCaseSensitive
                ? driverQuery.Where(d =>
                        d.DriverName.StartsWith(value) ||
                        (d.DriverSurname != null && d.DriverSurname.StartsWith(value)))
                    .Select(d => d.ClientDriverId).ToList()
                : driverQuery.Where(d =>
                        d.DriverName.ToLower().StartsWith(value) ||
                        (d.DriverSurname != null && d.DriverSurname.ToLower().StartsWith(value)))
                    .Select(d => d.ClientDriverId).ToList(),

            StringOperators.endsWith => isCaseSensitive
                ? driverQuery.Where(d =>
                        d.DriverName.EndsWith(value) ||
                        (d.DriverSurname != null && d.DriverSurname.EndsWith(value)))
                    .Select(d => d.ClientDriverId).ToList()
                : driverQuery.Where(d =>
                        d.DriverName.ToLower().EndsWith(value) ||
                        (d.DriverSurname != null && d.DriverSurname.ToLower().EndsWith(value)))
                    .Select(d => d.ClientDriverId).ToList(),

            _ => driverQuery.Where(d => false).Select(d => d.ClientDriverId)
                .ToList() // Empty result for an unknown operator
        };

        // Then filter bookings by matching driver IDs
        return query.Where(b => b.RequestClientDriverId != null && matchingDriverIds.Contains(b.RequestClientDriverId));
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyDriverEmailFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        StringOperators op,
        string value,
        bool isCaseSensitive,
        long userId) {
        // Find matching drivers (combining name and surname fields)
        var driverQuery = fleetDbContext.ClientDrivers.Where(d => d.UserId == userId);

        // Apply string operation on the driver name or surname
        var matchingDriverIds = op switch {
            StringOperators.contains => isCaseSensitive
                ? driverQuery.Where(d =>
                        !string.IsNullOrEmpty(d.EMail) && d.EMail.Contains(value))
                    .Select(d => d.ClientDriverId).ToList()
                : driverQuery.Where(d =>
                        !string.IsNullOrEmpty(d.EMail) && d.EMail.ToLower().Contains(value))
                    .Select(d => d.ClientDriverId).ToList(),

            StringOperators.equals => isCaseSensitive
                ? driverQuery.Where(d =>
                        d.EMail == value)
                    .Select(d => d.ClientDriverId).ToList()
                : driverQuery.Where(d =>
                        !string.IsNullOrEmpty(d.EMail) && d.EMail.ToLower() == value)
                    .Select(d => d.ClientDriverId).ToList(),

            StringOperators.startsWith => isCaseSensitive
                ? driverQuery.Where(d =>
                        !string.IsNullOrEmpty(d.EMail) && d.EMail.StartsWith(value))
                    .Select(d => d.ClientDriverId).ToList()
                : driverQuery.Where(d =>
                        !string.IsNullOrEmpty(d.EMail) && d.EMail.ToLower().StartsWith(value))
                    .Select(d => d.ClientDriverId).ToList(),

            StringOperators.endsWith => isCaseSensitive
                ? driverQuery.Where(d =>
                        !string.IsNullOrEmpty(d.EMail) && d.EMail.EndsWith(value))
                    .Select(d => d.ClientDriverId).ToList()
                : driverQuery.Where(d =>
                        !string.IsNullOrEmpty(d.EMail) && d.EMail.ToLower().EndsWith(value))
                    .Select(d => d.ClientDriverId).ToList(),

            _ => driverQuery.Where(d => false).Select(d => d.ClientDriverId)
                .ToList() // Empty result for an unknown operator
        };

        // Then filter bookings by matching driver IDs
        return query.Where(b => b.RequestClientDriverId != null && matchingDriverIds.Contains(b.RequestClientDriverId));
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyVehicleTypeFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        StringOperators op,
        string value,
        bool isCaseSensitive) {
        // Find matching vehicle types
        var vehicleTypeQuery = poolDbContext.BookingVehicleTypes.AsQueryable();

        // Apply string operation on vehicle type name
        var matchingVehicleTypeIds = op switch {
            StringOperators.contains => isCaseSensitive
                ? vehicleTypeQuery.Where(vt => vt.BookingVehicleType1.Contains(value))
                    .Select(vt => vt.BookingVehicleTypeId).ToList()
                : vehicleTypeQuery.Where(vt => vt.BookingVehicleType1.ToLower().Contains(value))
                    .Select(vt => vt.BookingVehicleTypeId).ToList(),

            StringOperators.equals => isCaseSensitive
                ? vehicleTypeQuery.Where(vt => vt.BookingVehicleType1 == value)
                    .Select(vt => vt.BookingVehicleTypeId).ToList()
                : vehicleTypeQuery.Where(vt => vt.BookingVehicleType1.ToLower() == value)
                    .Select(vt => vt.BookingVehicleTypeId).ToList(),

            StringOperators.startsWith => isCaseSensitive
                ? vehicleTypeQuery.Where(vt => vt.BookingVehicleType1.StartsWith(value))
                    .Select(vt => vt.BookingVehicleTypeId).ToList()
                : vehicleTypeQuery.Where(vt => vt.BookingVehicleType1.ToLower().StartsWith(value))
                    .Select(vt => vt.BookingVehicleTypeId).ToList(),

            StringOperators.endsWith => isCaseSensitive
                ? vehicleTypeQuery.Where(vt => vt.BookingVehicleType1.EndsWith(value))
                    .Select(vt => vt.BookingVehicleTypeId).ToList()
                : vehicleTypeQuery.Where(vt => vt.BookingVehicleType1.ToLower().EndsWith(value))
                    .Select(vt => vt.BookingVehicleTypeId).ToList(),

            _ => vehicleTypeQuery.Where(vt => false).Select(vt => vt.BookingVehicleTypeId).ToList() // Empty result set
        };

        // Then filter bookings by matching vehicle type IDs
        return query.Where(b =>
            b.BookingVehicleTypeId.HasValue && matchingVehicleTypeIds.Contains(b.BookingVehicleTypeId.Value));
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyVehicleCommanderEmailFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        StringOperators op,
        string value,
        bool isCaseSensitive,
        long userId) {
        // Find matching vehicle types
        var clientUsersQuery = fleetDbContext.ClientUsers.Where(u => u.UserId == userId);


        // Apply string operation on the client user name
        var matchedClientUserIds = op switch {
            StringOperators.contains => isCaseSensitive
                ? clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.Contains(value))
                    .Select(vt => vt.ClientUserId).ToList()
                : clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.ToLower().Contains(value))
                    .Select(vt => vt.ClientUserId).ToList(),

            StringOperators.equals => isCaseSensitive
                ? clientUsersQuery.Where(vt => vt.EMail == value)
                    .Select(vt => vt.ClientUserId).ToList()
                : clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.ToLower() == value)
                    .Select(vt => vt.ClientUserId).ToList(),

            StringOperators.startsWith => isCaseSensitive
                ? clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.StartsWith(value))
                    .Select(vt => vt.ClientUserId).ToList()
                : clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.ToLower().StartsWith(value))
                    .Select(vt => vt.ClientUserId).ToList(),

            StringOperators.endsWith => isCaseSensitive
                ? clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.EndsWith(value))
                    .Select(vt => vt.ClientUserId).ToList()
                : clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.ToLower().EndsWith(value))
                    .Select(vt => vt.ClientUserId).ToList(),

            _ => clientUsersQuery.Where(vt => false).Select(vt => vt.ClientUserId).ToList() // Empty result set
        };

        var matchedBookingIds = tfmsCustomDbContext.ScdfBookingAdditionalInfos
            .Where(info => info.BookingId.HasValue && !string.IsNullOrEmpty(info.VehicleCommanderClientUserId)
                                                   && matchedClientUserIds.Contains(info.VehicleCommanderClientUserId))
            .Select(info => info.BookingId.Value)
            .ToList();


        // Then filter bookings by matching vehicle type IDs
        return query.Where(b => matchedBookingIds.Contains(b.BookingId));
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyPurposeFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        StringOperators op,
        string value,
        bool isCaseSensitive) {
        // Find matching booking purposes
        var purposeQuery = poolDbContext.BookingPurposes.AsQueryable();

        // Apply string operation on purpose name
        var matchingPurposeIds = op switch {
            StringOperators.contains => isCaseSensitive
                ? purposeQuery.Where(p => p.BookingPurpose1.Contains(value))
                    .Select(p => p.BookingPurposeId).ToList()
                : purposeQuery.Where(p => p.BookingPurpose1.ToLower().Contains(value))
                    .Select(p => p.BookingPurposeId).ToList(),

            StringOperators.equals => isCaseSensitive
                ? purposeQuery.Where(p => p.BookingPurpose1 == value)
                    .Select(p => p.BookingPurposeId).ToList()
                : purposeQuery.Where(p => p.BookingPurpose1.ToLower() == value)
                    .Select(p => p.BookingPurposeId).ToList(),

            StringOperators.startsWith => isCaseSensitive
                ? purposeQuery.Where(p => p.BookingPurpose1.StartsWith(value))
                    .Select(p => p.BookingPurposeId).ToList()
                : purposeQuery.Where(p => p.BookingPurpose1.ToLower().StartsWith(value))
                    .Select(p => p.BookingPurposeId).ToList(),

            StringOperators.endsWith => isCaseSensitive
                ? purposeQuery.Where(p => p.BookingPurpose1.EndsWith(value))
                    .Select(p => p.BookingPurposeId).ToList()
                : purposeQuery.Where(p => p.BookingPurpose1.ToLower().EndsWith(value))
                    .Select(p => p.BookingPurposeId).ToList(),

            _ => purposeQuery.Where(p => false).Select(p => p.BookingPurposeId).ToList() // Empty result set
        };

        // Then filter bookings by matching purpose IDs
        return query.Where(b => b.BookingPurposeId.HasValue && matchingPurposeIds.Contains(b.BookingPurposeId.Value));
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyRequestorEmailFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        StringOperators op,
        string value,
        bool isCaseSensitive,
        long userId) {
        var clientUsersQuery = fleetDbContext.ClientUsers.Where(u => u.UserId == userId);

        // Apply string operation on the client user name
        var matchedClientUserIds = op switch {
            StringOperators.contains => isCaseSensitive
                ? clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.Contains(value))
                    .Select(vt => vt.ClientUserId).ToList()
                : clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.ToLower().Contains(value))
                    .Select(vt => vt.ClientUserId).ToList(),

            StringOperators.equals => isCaseSensitive
                ? clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail == value)
                    .Select(vt => vt.ClientUserId).ToList()
                : clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.ToLower() == value)
                    .Select(vt => vt.ClientUserId).ToList(),

            StringOperators.startsWith => isCaseSensitive
                ? clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.StartsWith(value))
                    .Select(vt => vt.ClientUserId).ToList()
                : clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.ToLower().StartsWith(value))
                    .Select(vt => vt.ClientUserId).ToList(),

            StringOperators.endsWith => isCaseSensitive
                ? clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.EndsWith(value))
                    .Select(vt => vt.ClientUserId).ToList()
                : clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && vt.EMail.ToLower().EndsWith(value))
                    .Select(vt => vt.ClientUserId).ToList(),

            _ => clientUsersQuery.Where(vt => false).Select(vt => vt.ClientUserId).ToList() // Empty result set
        };

        return query
            .Where(info => !string.IsNullOrEmpty(info.RequestClientUserId) && matchedClientUserIds.Contains(info.RequestClientUserId));
    }

    #endregion

    #region Array Filter Implementations

    private IQueryable<EFCore.Models.Pool.Booking> ApplyDriverEmailArrayFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        string[] values,
        bool isCaseSensitive,
        long userId) {
        // Find drivers that match any of the email values
        var driverQuery = fleetDbContext.ClientDrivers.Where(u => u.UserId == userId);

        List<string> matchingDriverIds;
        if (isCaseSensitive) {
            matchingDriverIds = driverQuery.Where(d => !string.IsNullOrEmpty(d.EMail) && values.Contains(d.EMail))
                .Select(d => d.ClientDriverId).ToList();
        }
        else {
            matchingDriverIds = driverQuery.Where(d => !string.IsNullOrEmpty(d.EMail) && values.Contains(d.EMail.ToLower()))
                .Select(d => d.ClientDriverId).ToList();
        }

        // Filter bookings by matching driver IDs
        return query.Where(b => b.RequestClientDriverId != null && matchingDriverIds.Contains(b.RequestClientDriverId));
    }


    private IQueryable<EFCore.Models.Pool.Booking> ApplyVehicleCommanderEmailArrayFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        string[] values,
        bool isCaseSensitive,
        long userId) {
        var clientUsersQuery = fleetDbContext.ClientUsers.Where(u => u.UserId == userId);

        var matchedClientUserIds = clientUsersQuery.Where(vt =>
                !string.IsNullOrEmpty(vt.EMail) && (isCaseSensitive ? values.Contains(vt.EMail) : values.Contains(vt.EMail.ToLower())))
            .Select(vt => vt.ClientUserId).ToList();

        var matchedBookingIds = tfmsCustomDbContext.ScdfBookingAdditionalInfos
            .Where(info => info.BookingId.HasValue && !string.IsNullOrEmpty(info.VehicleCommanderClientUserId)
                                                   && matchedClientUserIds.Contains(info.VehicleCommanderClientUserId))
            .Select(info => info.BookingId.Value)
            .ToList();

        return query.Where(b => matchedBookingIds.Contains(b.BookingId));
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyVehicleArrayFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        string[] values,
        bool isCaseSensitive,
        long userId) {
        // Find vehicles that match any of the values
        var vehicleQuery = ctDbContext.Vehicles.Where(u => u.UserId == userId);

        List<long> matchingVehicleIds;
        if (isCaseSensitive) {
            matchingVehicleIds = vehicleQuery.Where(v => values.Contains(v.Registration))
                .Select(v => v.VehicleId).ToList();
        }
        else {
            matchingVehicleIds = vehicleQuery.Where(v => values.Contains(v.Registration.ToLower()))
                .Select(v => v.VehicleId).ToList();
        }

        // Filter bookings by matching vehicle IDs
        return query.Where(b => b.VehicleId.HasValue && matchingVehicleIds.Contains(b.VehicleId.Value));
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyDriverArrayFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        string[] values,
        bool isCaseSensitive,
        long userId) {
        // Find drivers that match any of the values (in either name or surname)
        var driverQuery = fleetDbContext.ClientDrivers.Where(u => u.UserId == userId);

        List<string> matchingDriverIds;
        if (isCaseSensitive) {
            matchingDriverIds = driverQuery.Where(d =>
                    values.Contains(d.DriverName) ||
                    (d.DriverSurname != null && values.Contains(d.DriverSurname)))
                .Select(d => d.ClientDriverId).ToList();
        }
        else {
            matchingDriverIds = driverQuery.Where(d =>
                    values.Contains(d.DriverName.ToLower()) ||
                    (d.DriverSurname != null && values.Contains(d.DriverSurname.ToLower())))
                .Select(d => d.ClientDriverId).ToList();
        }

        // Filter bookings by matching driver IDs
        return query.Where(b => b.RequestClientDriverId != null && matchingDriverIds.Contains(b.RequestClientDriverId));
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyVehicleTypeArrayFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        string[] values,
        bool isCaseSensitive) {
        // Find vehicle types that match any of the values
        var typeQuery = poolDbContext.BookingVehicleTypes.AsQueryable();

        List<long> matchingTypeIds;
        if (isCaseSensitive) {
            matchingTypeIds = typeQuery.Where(vt => values.Contains(vt.BookingVehicleType1))
                .Select(vt => vt.BookingVehicleTypeId).ToList();
        }
        else {
            matchingTypeIds = typeQuery.Where(vt => values.Contains(vt.BookingVehicleType1.ToLower()))
                .Select(vt => vt.BookingVehicleTypeId).ToList();
        }

        // Filter bookings by matching vehicle type IDs
        return query.Where(b =>
            b.BookingVehicleTypeId.HasValue && matchingTypeIds.Contains(b.BookingVehicleTypeId.Value));
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyPurposeArrayFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        string[] values,
        bool isCaseSensitive) {
        // Find booking purposes that match any of the values
        var purposeQuery = poolDbContext.BookingPurposes.AsQueryable();

        List<long> matchingPurposeIds;
        if (isCaseSensitive) {
            matchingPurposeIds = purposeQuery.Where(p => values.Contains(p.BookingPurpose1))
                .Select(p => p.BookingPurposeId).ToList();
        }
        else {
            matchingPurposeIds = purposeQuery.Where(p => values.Contains(p.BookingPurpose1.ToLower()))
                .Select(p => p.BookingPurposeId).ToList();
        }

        // Filter bookings by matching purpose IDs
        return query.Where(b => b.BookingPurposeId.HasValue && matchingPurposeIds.Contains(b.BookingPurposeId.Value));
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyRequestorEmailArrayFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        string[] values,
        bool isCaseSensitive,
        long userId) {
        var clientUsersQuery = fleetDbContext.ClientUsers.Where(u => u.UserId == userId);

        var matchedClientUserIds = isCaseSensitive
            ? clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && values.Contains(vt.EMail))
                .Select(vt => vt.ClientUserId).ToList()
            : clientUsersQuery.Where(vt => !string.IsNullOrEmpty(vt.EMail) && values.Contains(vt.EMail.ToLower()))
                .Select(vt => vt.ClientUserId).ToList();

        return query
            .Where(info => !string.IsNullOrEmpty(info.RequestClientUserId) && matchedClientUserIds.Contains(info.RequestClientUserId));
    }

    #endregion

    private IQueryable<EFCore.Models.Pool.Booking> ApplyDateRangeFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        DateRangeFilterItem item) {
        // Apply different date filters based on the parsed field
        var afterDate = item.After.Value;
        var beforeDate = item.Before.Value;

        // Ensure DateTimeKind is consistent, assuming UTC for database comparisons
        if (afterDate.Kind != DateTimeKind.Utc) {
            afterDate = DateTime.SpecifyKind(afterDate, DateTimeKind.Utc);
        }

        if (beforeDate.Kind != DateTimeKind.Utc) {
            beforeDate = DateTime.SpecifyKind(beforeDate, DateTimeKind.Utc);
        }

        // Apply date filters directly for each field type without using a delegate function
        return item.ParsedField switch {
            DateRangeFilterField.requestDate => ApplyRequestTsDateFilter(query, item, afterDate, beforeDate),
            DateRangeFilterField.startDate => ApplyStartTsDateFilter(query, item, afterDate, beforeDate),
            _ => query // Default case - return unchanged query
        };
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyRequestTsDateFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        DateRangeFilterItem item,
        DateTime afterDate,
        DateTime beforeDate) {
        if (item.OperatorType == LogicOperator.And) {
            query = query.Where(b =>
                (item.After.Strategy == Strategy.inclusive ? b.RequestTs >= afterDate : b.RequestTs > afterDate)
                && (item.Before.Strategy == Strategy.inclusive ? b.RequestTs <= beforeDate : b.RequestTs < beforeDate));
        }
        else {
            // OR logic
            if (item.After.Strategy == Strategy.inclusive && item.Before.Strategy == Strategy.inclusive) {
                query = query.Where(b => b.RequestTs >= afterDate || b.RequestTs <= beforeDate);
            }
            else if (item.After.Strategy == Strategy.inclusive && item.Before.Strategy == Strategy.exclusive) {
                query = query.Where(b => b.RequestTs >= afterDate || b.RequestTs < beforeDate);
            }
            else if (item.After.Strategy == Strategy.exclusive && item.Before.Strategy == Strategy.inclusive) {
                query = query.Where(b => b.RequestTs > afterDate || b.RequestTs <= beforeDate);
            }
            else {
                // exclusive for both
                query = query.Where(b => b.RequestTs > afterDate || b.RequestTs < beforeDate);
            }
        }

        return query;
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplyStartTsDateFilter(
        IQueryable<EFCore.Models.Pool.Booking> query,
        DateRangeFilterItem item,
        DateTime afterDate,
        DateTime beforeDate) {
        if (item.OperatorType == LogicOperator.And) {
            query = query.Where(b =>
                (item.After.Strategy == Strategy.inclusive ? b.StartTs >= afterDate : b.StartTs > afterDate)
                && (item.Before.Strategy == Strategy.inclusive ? b.StartTs <= beforeDate : b.StartTs < beforeDate));
        }
        else {
            // OR logic
            if (item.After.Strategy == Strategy.inclusive && item.Before.Strategy == Strategy.inclusive) {
                query = query.Where(b => b.StartTs >= afterDate || b.StartTs <= beforeDate);
            }
            else if (item.After.Strategy == Strategy.inclusive && item.Before.Strategy == Strategy.exclusive) {
                query = query.Where(b => b.StartTs >= afterDate || b.StartTs < beforeDate);
            }
            else if (item.After.Strategy == Strategy.exclusive && item.Before.Strategy == Strategy.inclusive) {
                query = query.Where(b => b.StartTs > afterDate || b.StartTs <= beforeDate);
            }
            else {
                // exclusive for both
                query = query.Where(b => b.StartTs > afterDate || b.StartTs < beforeDate);
            }
        }

        return query;
    }
}