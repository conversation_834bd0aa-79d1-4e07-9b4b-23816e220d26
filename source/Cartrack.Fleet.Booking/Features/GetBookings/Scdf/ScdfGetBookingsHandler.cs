﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Http.Filters;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.GetBookings.Scdf;

public class ScdfGetBookingsHandler(
    ScdfBookingRepository repo,
    IHttpContextAccessor context,
    ILogger<ScdfGetBookingsHandler> logger) : IRequestHandler<ScdfGetBookingsRequest, ScdfGetBookingsResponse> {
    public async Task<ScdfGetBookingsResponse> Handle(ScdfGetBookingsRequest request, CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.ServerRequestModal.Pagination.Offset >= 0, "Offset must be greater than or equal to zero");
            Requires.IsTrue(() => request.ServerRequestModal.Pagination.PageSize > 0, "Page size must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving bookings with criteria", context.HttpContext?.TraceIdentifier ?? "",
                request.Account);

            var bookingContext = context.GetBookingContext()!;
            var result = await repo.GetBookings(request.LoginClientUserId ?? "", request.ServerRequestModal, bookingContext);
            var httpBookings = result.Bookings.Select(b => {
                var httpBooking = b.ToHttpBooking();

                // Create a ScdfBooking instance
                var scdfBooking = new ScdfBooking();

                // Copy all properties from the base class using reflection
                foreach (var prop in typeof(IO.Http.Booking).GetProperties()) {
                    if (prop.CanWrite) {
                        var value = prop.GetValue(httpBooking);
                        prop.SetValue(scdfBooking, value);
                    }
                }

                // Set the custom email properties
                scdfBooking.CommanderEmail = b.VehicleCommander?.Email;
                scdfBooking.RequestedByEmail = b.RequestedBy.Email;

                return scdfBooking;
            }).ToArray();

            var statusMetrics = result.StatusCounts
                .Select(kvp => new StatusCount {
                    StatusId = kvp.Key, Count = kvp.Value
                })
                .ToArray();

            return new ScdfGetBookingsResponse(
                httpBookings,
                new PaginationMetrics(result.Total, statusMetrics)
            );
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving bookings",
                context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfGetBookingsResponse(null, new PaginationMetrics(0, []), ex) {
                IsServerError = false
            };
        }

        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving bookings", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfGetBookingsResponse(null, new PaginationMetrics(0, []), ex) {
                IsServerError = true
            };
        }
    }
}