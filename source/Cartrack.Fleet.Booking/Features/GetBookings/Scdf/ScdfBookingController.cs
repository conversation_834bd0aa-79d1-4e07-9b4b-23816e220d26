﻿using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpPost]
    public async Task<ActionResult<ScdfGetBookingsResponse>> GetBookings([FromBody] ServerRequestModel serverRequestModel = null) {
        var request = new ScdfGetBookingsRequest {
            ServerRequestModal = serverRequestModel
        };

        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = authClaims.ClientUserId;
        request.LoginClientUserId = authClaims.ClientUserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfGetBookingsResponse, ScdfBooking[]>(this.HttpContext);
    }
}