﻿using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Common;

namespace Cartrack.Fleet.Booking.Features.GetBookings.Scdf;

public class ScdfBooking : IO.Http.Booking {
    public string? CommanderEmail { get; set; }
    public string? RequestedByEmail { get; set; }
}

public record ScdfGetBookingsResponse : BaseResponse<ScdfBooking[]> {
    public PaginationMetrics Metrics { get; init; }

    public ScdfGetBookingsResponse(ScdfBooking[]? value, PaginationMetrics metrics, Exception? error = null)
        : base(value, error) {
        Metrics = metrics;
    }
}

public record PaginationMetrics {
    public int Total { get; init; }
    public StatusCount[] StatusMetrics { get; init; }

    public PaginationMetrics(int total, StatusCount[] statusMetrics) {
        Total = total;
        StatusMetrics = statusMetrics;
    }
}

public record StatusCount {
    public long StatusId { get; init; }
    public int Count { get; init; }
}