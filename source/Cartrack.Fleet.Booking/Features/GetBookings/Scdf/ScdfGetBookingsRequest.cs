﻿using MediatR;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.GetBookings.Scdf;

public record ScdfGetBookingsRequest : IRequest<ScdfGetBookingsResponse> {
    [JsonIgnore] public string? Account { get; set; }
    [JsonIgnore] public string? LoginClientUserId { get; set; }
    public ServerRequestModel ServerRequestModal { get; init; } = new();
    [JsonIgnore] public long UserId { get; set; }
    [JsonIgnore] public string ClientUserId { get; set; } = "";
}

public record ServerRequestModel {
    public Pagination Pagination { get; init; } = new();

    public string? StatusIds { get; init; }

    public SortOption? Sort { get; init; }
    public FilterOptions? Filters { get; init; }

    public SearchTextFilterItem? QuickSearch { get; init; }
}

public record Pagination {
    [Range(0, int.MaxValue, ErrorMessage = "Offset must be greater than or equal to zero")]
    public int Offset { get; init; } = 0;

    [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
    public int PageSize { get; init; } = 10;
}

// Ensure your FilterOptions record uses the custom converter
public record FilterOptions {
    public FilterItem[]? Items { get; init; }

    public LogicOperator LogicOperator { get; init; }
}

public record SearchTextFilterItem {
    public string[] Text { get; init; } = [];

    public string SearchText => this.Text.FirstOrDefault() ?? "";
}

public record SortOption {
    public SortField ParsedField {
        get => Enum.TryParse<SortField>(this.Field, out var parsedField) ? parsedField : SortField.requestDate;
        init => this.Field = value.ToString();
    }

    public string Sort { get; init; } = "desc";
    public string Field { get; init; } = "requestDate";

    public SortDirection ParsedSort {
        get => Enum.TryParse<SortDirection>(this.Sort, out var parsedSort) ? parsedSort : SortDirection.desc;
        init => this.Sort = value.ToString();
    }
}

# region Enums

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum LogicOperator {
    [EnumMember(Value = "and")] And,
    [EnumMember(Value = "or")] Or
}

// Enum for case sensitivity
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum FilterCaseSensitivity {
    sensitive,
    insensitive
}

public enum DateRangeFilterField {
    [EnumMember(Value = "requestDate")] requestDate,
    [EnumMember(Value = "startDate")] startDate,
}

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum Strategy {
    inclusive,
    exclusive,
}

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum StringFilterField {
    vehicle,
    driver,
    driverEmail,
    vehicleType,
    purpose,
    vehicleCommanderEmail,
    requestorEmail
}

// Enums for operators with string values for JSON
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum StringOperators {
    contains,
    equals,
    startsWith,
    endsWith
}

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum ArrayOperators {
    isAnyOf
}

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum EmptyOperators {
    isNotEmpty,
    isEmpty
}

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum SortField {
    requestDate,
    pickUpAt,
    startDate,
    endDate,
    returnedAt
}

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum SortDirection {
    asc,
    desc
}

#endregion

#region Filter

[JsonConverter(typeof(FilterItemConverter))] // Apply the custom converter here
public abstract record FilterItem {
    [JsonPropertyName("type")] public abstract string Type { get; }

    [JsonPropertyName("field")] public abstract string Field { get; init; }
}

public record DateFilterItem {
    public DateTime Value { get; init; }
    public Strategy Strategy { get; init; } = Strategy.inclusive;
}

public record DateRangeFilterItem : FilterItem {
    public DateRangeFilterField ParsedField {
        get => Enum.TryParse<DateRangeFilterField>(this.Field, out var parsedField) ? parsedField : DateRangeFilterField.requestDate;
        init => this.Field = value.ToString();
    }

    public override string Field { get; init; } = "requestDate";
    public override string Type { get; } = "date";

    public string Operator { get; } = "condition";
    public LogicOperator OperatorType { get; init; } = LogicOperator.And;

    public DateFilterItem After { get; init; } = new();
    public DateFilterItem Before { get; init; } = new();
}

// Base class for all filter items
public abstract record StringFilterItem : FilterItem {
    public override string Type { get; } = "string";
    public FilterCaseSensitivity Case { get; init; }

    public StringFilterField ParsedField {
        get => Enum.TryParse<StringFilterField>(this.Field, out var parsedField) ? parsedField : StringFilterField.vehicle;
        init => this.Field = value.ToString();
    }

    public override string Field { get; init; } = "vehicle";

    // This property will be used to discriminate the types during deserialization
    // It should be abstract and implemented by derived classes
    [JsonPropertyName("operator")] public abstract string Operator { get; }
}

// Filter item for string operations (contains, equals, startsWith, endsWith)
public record SingleStringStringFilterItem : StringFilterItem {
    [JsonPropertyName("operator")]
    public override string Operator =>
        this.StringOperator.ToString();

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public StringOperators StringOperator { get; init; }

    public string Value { get; init; } = "";
}

// Filter item for isAnyOf operation
public record ArrayStringFilterItem : StringFilterItem {
    [JsonPropertyName("operator")]
    public override string Operator =>
        this.ArrayOperator.ToString();

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public ArrayOperators ArrayOperator { get; init; }

    public string[] Value { get; init; }
}

// Filter item for isEmpty / isNotEmpty operations

public record EmptyStringFilterItem : StringFilterItem {
    [JsonPropertyName("operator")]
    public override string Operator =>
        this.EmptyOperator.ToString();

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public EmptyOperators EmptyOperator { get; init; }
}

public class FilterItemConverter : JsonConverter<FilterItem> {
    public override FilterItem Read(ref Utf8JsonReader reader, Type typeToConvert,
        JsonSerializerOptions options) {
        // Read the entire object into a JsonDocument to inspect it without advancing the original reader
        using var document = JsonDocument.ParseValue(ref reader); // This consumes the reader for this level
        var root = document.RootElement;

        // Try to get the operator and type property
        if (!root.TryGetProperty("operator", out var operatorElement) ||
            operatorElement.ValueKind != JsonValueKind.String) {
            // Log this or throw a more descriptive exception if "operator" is missing or not a string
            throw new JsonException("Filter item must contain a string 'operator' property.");
        }

        var op = operatorElement.GetString();

        if (!root.TryGetProperty("type", out var typeElement) ||
            typeElement.ValueKind != JsonValueKind.String) {
            // Log this or throw a more descriptive exception if "type" is missing or not a string
            throw new JsonException("Filter item must contain a string 'type' property.");
        }

        var type = typeElement.GetString();

        var innerOptions = new JsonSerializerOptions(options); // Copy existing options
        if (innerOptions.Converters.Any(c => c.GetType() == typeof(FilterItemConverter))) {
            // Remove *this specific instance/type* of converter to break recursion
            innerOptions.Converters.Remove(this);
        }

        // Now, re-serialize the root element to a string, and then deserialize that string
        // using the innerOptions. This is a common pattern when you've already consumed
        // the Utf8JsonReader to peek at a property.
        var rawText = root.GetRawText();

        FilterItem? result = type switch {
            "string" => DeserializeStringFilterItem(rawText, innerOptions, op),
            "date" => JsonSerializer.Deserialize<DateRangeFilterItem>(rawText, innerOptions),
            _ => throw new JsonException($"Unknown operator: '{type}' found in filter item.")
        };

        if (result == null) {
            // This indicates an issue during the inner deserialization
            throw new JsonException($"Failed to deserialize FilterItem for type '{type}'. Raw JSON: {rawText}");
        }

        return result;
    }

    private static StringFilterItem? DeserializeStringFilterItem(string rawText,
        JsonSerializerOptions innerOptions, string op) {
        return op switch {
            "contains" or "equals" or "startsWith" or "endsWith" => DeserializeSingleStringFilterItem(rawText,
                innerOptions, op),
            "isAnyOf" => DeserializeArrayStringFilterItem(rawText, innerOptions),
            "isEmpty" or "isNotEmpty" => DeserializeEmptyStringFilterItem(rawText, innerOptions, op),
            _ => throw new JsonException($"Unknown operator: '{op}' found in filter item.")
        };
    }

    private static EmptyStringFilterItem? DeserializeEmptyStringFilterItem(string rawText,
        JsonSerializerOptions innerOptions, string op) {
        var emptyFilter = JsonSerializer.Deserialize<EmptyStringFilterItem>(rawText, innerOptions);
        if (emptyFilter != null) {
            emptyFilter = emptyFilter with {
                EmptyOperator = op == "isEmpty" ? EmptyOperators.isEmpty : EmptyOperators.isNotEmpty
            };
        }

        return emptyFilter;
    }

    private static SingleStringStringFilterItem? DeserializeSingleStringFilterItem(string rawText,
        JsonSerializerOptions innerOptions, string op) {
        var stringFilter = JsonSerializer.Deserialize<SingleStringStringFilterItem>(rawText, innerOptions);
        if (stringFilter != null) {
            var stringOperator = op switch {
                "contains" => StringOperators.contains,
                "equals" => StringOperators.equals,
                "startsWith" => StringOperators.startsWith,
                "endsWith" => StringOperators.endsWith,
                _ => throw new JsonException($"Unknown string operator: '{op}'")
            };

            stringFilter = stringFilter with {
                StringOperator = stringOperator
            };
        }

        return stringFilter;
    }

    private static ArrayStringFilterItem? DeserializeArrayStringFilterItem(string rawText,
        JsonSerializerOptions innerOptions) {
        var arrayFilter = JsonSerializer.Deserialize<ArrayStringFilterItem>(rawText, innerOptions);
        if (arrayFilter != null) {
            arrayFilter = arrayFilter with {
                ArrayOperator = ArrayOperators.isAnyOf
            };
        }

        return arrayFilter;
    }

    public override void Write(Utf8JsonWriter writer, FilterItem value, JsonSerializerOptions options) {
        var innerOptions = new JsonSerializerOptions(options);
        if (innerOptions.Converters.Any(c => c.GetType() == typeof(FilterItemConverter))) {
            innerOptions.Converters.Remove(this);
        }

        JsonSerializer.Serialize(writer, value, value.GetType(), innerOptions);
    }
}

#endregion