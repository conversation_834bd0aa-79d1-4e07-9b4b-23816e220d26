﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.States;
using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Booking.IO.Http.Filters;
using Microsoft.EntityFrameworkCore;
using VehicleBooking = Cartrack.Fleet.Booking.Domain.VehicleBooking;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<BookingsResult> GetBookings(
        string clientUserId,
        ServerRequestModel serverRequestModel,
        BookingContext bookingContext) {
        var agency = bookingContext.Claims.Account;
        var ctUser = await this._ctDbContext.Users.FirstOrDefaultAsync(c => c.UserName.Equals(agency));
        var offset = serverRequestModel.Pagination.Offset;
        var pageSize = serverRequestModel.Pagination.PageSize;
        var statusIds = serverRequestModel.StatusIds;
        var sort = serverRequestModel.Sort;
        var searchTextFilter = ExtractSearchTextFilterItem(serverRequestModel);
        var filters = serverRequestModel.Filters;

        // extract the status ids
        long[]? parsedStatusIds = null;
        if (!string.IsNullOrEmpty(statusIds)) {
            parsedStatusIds = statusIds.Split(',')
                .Where(s => long.TryParse(s, out _))
                .Select(long.Parse)
                .ToArray();
        }

        if (ctUser is null) {
            return new BookingsResult();
        }

        var bookingsQuery = this._poolDbContext.Bookings
            .Where(b => b.UserId == ctUser.UserId);

        if (!string.IsNullOrEmpty(clientUserId)) {
            if (agency == this._appSettings.ScdfAccount && bookingContext.Permissions.IssuancePermissions.CanViewOnlyMyBookings) {
                var bookingIdsScdf = await this._tfmsCustomDbContext.ScdfBookingAdditionalInfos
                    .Where(b => b.RequestedForClientUserId == clientUserId).Select(x => x.BookingId)
                    .ToListAsync();

                bookingsQuery = bookingsQuery.Where(b => b.RequestClientUserId == clientUserId || bookingIdsScdf.Contains(b.BookingId));
            }
        }

        bookingsQuery = bookingsQuery.AsQueryable();

        // Get status counts for metrics
        var statusCountLookup = await bookingsQuery
            .GroupBy(b => b.BookingStatusId)
            .Select(g => new {
                StatusId = g.Key, Count = g.Count()
            })
            .ToDictionaryAsync(x => x.StatusId, x => x.Count);

        // Apply status filters if provided
        if (parsedStatusIds is { Length: > 0 }) {
            bookingsQuery = bookingsQuery.Where(b => parsedStatusIds.Contains(b.BookingStatusId));
        }

        // Apply search filters if provided
        if (searchTextFilter != null) {
            // NOTE: userId is important for filtering out bookings that the user doesn't have access to
            bookingsQuery = await this.ApplySearchTextFilterAsync(bookingsQuery, searchTextFilter, ctUser.UserId);
        }

        // Apply filters if provided
        if (filters is { Items.Length: > 0 }) {
            var filterItems = filters.Items;
            LogicOperator filterLogicOperator = filters.LogicOperator;

            if (filterLogicOperator == LogicOperator.And) {
                // Apply AND logic - all conditions must be true
                foreach (var item in filterItems) {
                    bookingsQuery = this._filterService.ApplyFilterItem(bookingsQuery, item, ctUser.UserId);
                }
            }
            else {
                // Apply OR logic - any condition can be true
                var combinedQuery = bookingsQuery.Where(b => false); // Start with an empty result set

                foreach (var item in filterItems) {
                    var partialQuery = this._filterService.ApplyFilterItem(bookingsQuery, item, ctUser.UserId);
                    combinedQuery = combinedQuery.Union(partialQuery);
                }

                bookingsQuery = combinedQuery;
            }
        }

        // Get total count for pagination
        int total = await bookingsQuery.CountAsync();

        // Apply sorting
        bookingsQuery = this.ApplySorting(bookingsQuery, sort);

        // Get paginated bookings
        var bookings = await bookingsQuery
            .Skip(offset)
            .Take(pageSize)
            .ToArrayAsync();

        // Return early if no bookings found
        if (bookings.Length == 0) {
            return new BookingsResult {
                Total = total
            };
        }

        // Extract IDs for related data lookups
        var vehicleIds = bookings
            .Select(b => b.VehicleId)
            .Where(id => id.HasValue)
            .Select(id => id!.Value)
            .Distinct()
            .ToArray();

        var driverIds = bookings
            .Select(b => b.RequestClientDriverId)
            .Where(id => id != null)
            .Distinct()
            .ToArray();

        // Get vehicle registrations lookup
        var vehicleRegistrationLookup = vehicleIds.Length > 0
            ? await this._ctDbContext.Vehicles
                .Where(v => vehicleIds.Contains(v.VehicleId))
                .ToDictionaryAsync(v => v.VehicleId, v => v.Registration)
            : new Dictionary<long, string>();

        // Get driver details lookup - properly typed as a ValueTuple
        var driverNameLookup = driverIds.Length > 0
            ? await this._fleetDbContext.ClientDrivers
                .Where(d => driverIds.Contains(d.ClientDriverId))
                .ToDictionaryAsync(
                    d => d.ClientDriverId,
                    d => (Name: d.DriverName, Surname: d.DriverSurname ?? string.Empty, Email: d.EMail ?? string.Empty))
            : new Dictionary<string, (string Name, string Surname, string Email)>();

        // Get booking status lookup
        var dbStatusIds = bookings.Select(b => b.BookingStatusId).Distinct().ToArray();
        var bookingStatusLookup = dbStatusIds.Length > 0
            ? await this._poolDbContext.BookingStatuses
                .Where(s => dbStatusIds.Contains(s.BookingStatusId))
                .ToDictionaryAsync(s => s.BookingStatusId, s => s.BookingStatus1)
            : new Dictionary<long, string>();

        // Get booking approvals for the current bookings
        var bookingIds = bookings.Select(b => b.BookingId).ToArray();
        var bookingApprovals = await this._poolDbContext.BookingApprovals
            .Where(ba => bookingIds.Contains(ba.BookingId))
            .ToListAsync();

        // Get Vehicle commander lookup
        var bookingAdditionalInfos = await this._tfmsCustomDbContext.ScdfBookingAdditionalInfos
            .Where(info => info.BookingId.HasValue && bookingIds.Contains(info.BookingId.Value))
            .ToListAsync();
        var vehicleCommanderByBookingId = bookingAdditionalInfos
            .Where(info => !string.IsNullOrEmpty(info.VehicleCommanderClientUserId))
            .ToDictionary(info => info.BookingId!.Value, info => info.VehicleCommanderClientUserId!);

        // Get site location lookup
        var pickupSiteLocationIds = bookings
            .Select(b => b.PickupSiteLocationId)
            .Distinct()
            .ToArray();
        var siteLocationLookup = pickupSiteLocationIds.Length > 0
            ? await this._fleetDbContext.SiteLocations
                .Where(sl => pickupSiteLocationIds.Contains(sl.SiteLocationId))
                .ToDictionaryAsync(sl => sl.SiteLocationId, sl => sl.SiteLocationName)
            : new Dictionary<long, string>();

        // Collect all client user IDs
        var clientUserIds = new HashSet<string>();
        foreach (var booking in bookings) {
            if (!string.IsNullOrEmpty(booking.RequestClientUserId)) {
                clientUserIds.Add(booking.RequestClientUserId);
            }

            if (!string.IsNullOrEmpty(booking.DecisionClientUserId)) {
                clientUserIds.Add(booking.DecisionClientUserId);
            }
        }

        foreach (var commanderId in vehicleCommanderByBookingId.Values) {
            clientUserIds.Add(commanderId);
        }

        foreach (var approval in bookingApprovals) {
            if (!string.IsNullOrEmpty(approval.UnitManagerId)) {
                clientUserIds.Add(approval.UnitManagerId);
            }
        }

        // Get client username lookup
        var clientUserLookup = clientUserIds.Count > 0
            ? await this._fleetDbContext.ClientUsers
                .Where(cu => clientUserIds.Contains(cu.ClientUserId))
                .ToDictionaryAsync(cu => cu.ClientUserId, cu => cu)
            : new Dictionary<string, EFCore.Models.Fleet.ClientUser>();

        // Get booking purpose lookup
        var bookingPurposeIds = bookings
            .Select(b => b.BookingPurposeId)
            .Where(id => id.HasValue)
            .Select(id => id!.Value)
            .Distinct()
            .ToArray();

        var bookingPurposeLookup = bookingPurposeIds.Length > 0
            ? await this._poolDbContext.BookingPurposes
                .Where(bp => bookingPurposeIds.Contains(bp.BookingPurposeId))
                .ToDictionaryAsync(bp => bp.BookingPurposeId, bp => bp.BookingPurpose1)
            : new Dictionary<long, string>();

        // Get vehicle type lookup
        var bookingVehicleTypeIds = bookings
            .Select(b => b.BookingVehicleTypeId)
            .Where(id => id.HasValue)
            .Select(id => id!.Value)
            .Distinct()
            .ToArray();

        var vehicleTypeLookup = bookingVehicleTypeIds.Length > 0
            ? await this._poolDbContext.BookingVehicleTypes
                .Where(vt => bookingVehicleTypeIds.Contains(vt.BookingVehicleTypeId))
                .ToDictionaryAsync(vt => vt.BookingVehicleTypeId, vt => vt.BookingVehicleType1)
            : [];

        // Get journeys lookup
        var bookingJourneys = await this._poolDbContext.BookingJourneys
            .Where(j => j.BookingId.HasValue && bookingIds.Contains(j.BookingId.Value))
            .ToListAsync();

        var journeysByBookingId = bookingJourneys
            .GroupBy(j => j.BookingId!.Value)
            .ToDictionary(g => g.Key, g => g.ToList());

        var result = bookings.Select(booking => {
            var vehicleBooking = new VehicleBooking {
                BookingId = booking.BookingId,
                StartDate = booking.StartTs,
                EndDate = booking.EndTs,
                PickupTime = booking.PickupIgnitionTs,
                DropoffTime = booking.ReturnedIgnitionTs,
                Description = booking.BookingPurposeDescription,
                CreatedDate = booking.RequestTs,
                UpdatedDate = booking.DecisionTs ?? booking.RequestTs,
                RequestedDate = booking.RequestTs,
                Type = booking.BookingType.HasValue ? (BookingType)booking.BookingType.Value : BookingType.Standard,
                BookingReference = booking.BookingReference,
                KeyReturnTs = booking.KeyReturnTs,
                KeyCollectionTs = booking.KeyCollectionTs,
                PickupLocation = new RequestLocation {
                    Id = booking.PickupSiteLocationId,
                    Name = siteLocationLookup.GetValueOrDefault(booking.PickupSiteLocationId, string.Empty)
                }
            };

            // Set vehicle category
            if (booking.BookingVehicleTypeId.HasValue) {
                vehicleBooking.VehicleCategory = new RequestVehicleType {
                    Id = booking.BookingVehicleTypeId.Value,
                    Name = vehicleTypeLookup.GetValueOrDefault(booking.BookingVehicleTypeId.Value, string.Empty)
                };
            }

            // Set requested by user
            if (booking.RequestClientUserId != null) {
                var requestedUser = clientUserLookup.GetValueOrDefault(booking.RequestClientUserId, new EFCore.Models.Fleet.ClientUser());
                vehicleBooking.RequestedBy = new ClientUser {
                    ClientUserId = booking.RequestClientUserId.Trim(),
                    Username = requestedUser.UserName,
                    Email = requestedUser.EMail ?? string.Empty
                };
            }

            // Set booking status
            vehicleBooking.Status = BaseBookingState.GetState(booking.BookingStatusId, new VehicleBooking());

            // Set booking purpose
            if (booking.BookingPurposeId.HasValue) {
                vehicleBooking.Purpose = new RequestPurpose {
                    Id = booking.BookingPurposeId.Value,
                    Title = bookingPurposeLookup.GetValueOrDefault(booking.BookingPurposeId.Value, string.Empty),
                    Description = booking.BookingPurposeDescription
                };
            }

            // Set vehicle target
            if (booking.VehicleId.HasValue) {
                vehicleBooking.Target = new VehicleTarget {
                    Vehicle = new Domain.Common.Vehicle {
                        VehicleId = booking.VehicleId.Value,
                        Registration =
                            vehicleRegistrationLookup.GetValueOrDefault(booking.VehicleId.Value, string.Empty)
                    }
                };
            }

            // Set driver
            if (booking.RequestClientDriverId != null &&
                driverNameLookup.TryGetValue(booking.RequestClientDriverId, out var driverInfo)) {
                vehicleBooking.Driver = new Domain.Common.Driver {
                    DriverId = booking.RequestClientDriverId,
                    DriverName = driverInfo.Name,
                    DriverSurname = driverInfo.Surname,
                    Email = driverInfo.Email
                };
            }

            // Set journeys for this booking
            var journeysForBooking =
                journeysByBookingId.GetValueOrDefault(booking.BookingId, []);
            vehicleBooking.Journeys = journeysForBooking.Select(j => new Journey {
                Id = j.Id,
                Start = j.StartTs,
                End = j.EndTs,
                Location = j.Location ?? string.Empty,
                Order = j.Order,
                LocationReference = new JourneyLocation {
                    Type = j.JourneyLocationType ?? string.Empty, Value = j.JourneyLocationId ?? string.Empty
                }
            }).ToList();

            var bookingAdditionalInfo =
                bookingAdditionalInfos.FirstOrDefault(info => info.BookingId == booking.BookingId);
            // set journey type
            vehicleBooking.LocationType = bookingAdditionalInfo?.LocationType ?? null;

            // Get approvals specific to this booking
            var approvalsForBooking = bookingApprovals
                .Where(ba => ba.BookingId == booking.BookingId)
                .ToList();

            // Set ApprovedBy list
            var approvedManagers = new List<ClientUser>();

            var approvedManagerIds = approvalsForBooking
                .Where(ba => ba.IsApproved == true)
                .Select(ba => ba.UnitManagerId)
                .ToList();

            foreach (var managerId in approvedManagerIds) {
                var manager = clientUserLookup.GetValueOrDefault(managerId, new EFCore.Models.Fleet.ClientUser());
                approvedManagers.Add(new ClientUser {
                    ClientUserId = managerId,
                    Username = manager.UserName,
                    Email = manager.EMail ?? string.Empty
                });
            }

            // If no approvals found but booking is approved, consider decision_client_user_id
            if (approvedManagers.Count == 0 && booking.IsApproved == true) {
                if (booking.DecisionClientUserId != null) {
                    var decisionUser =
                        clientUserLookup.GetValueOrDefault(booking.DecisionClientUserId, new EFCore.Models.Fleet.ClientUser());
                    approvedManagers.Add(new ClientUser {
                        ClientUserId = booking.DecisionClientUserId,
                        Username = decisionUser.UserName,
                        Email = decisionUser.EMail ?? string.Empty
                    });
                }
                else {
                    // System approval case
                    approvedManagers.Add(new ClientUser {
                        ClientUserId = "", Username = "System"
                    });
                }
            }

            vehicleBooking.ApprovedBy = approvedManagers;

            // Set RejectedBy list 
            var rejectedManagers = new List<ClientUser>();

            // we should only fetch declined managers when it's rejected
            if (booking.BookingStatusId == 4) {
                var declinedManagerIds = approvalsForBooking
                    .Where(ba => ba.IsApproved == false)
                    .Select(ba => ba.UnitManagerId)
                    .ToList();

                foreach (var managerId in declinedManagerIds) {
                    var manager = clientUserLookup.GetValueOrDefault(managerId, new EFCore.Models.Fleet.ClientUser());
                    rejectedManagers.Add(new ClientUser {
                        ClientUserId = managerId,
                        Username = manager.UserName,
                        Email = manager.EMail ?? string.Empty
                    });
                }

                // If no declined approvals found but booking is declined, consider decision_client_user_id
                if (rejectedManagers.Count == 0 && (booking.IsApproved == false || booking.BookingStatusId == 4)) {
                    if (booking.DecisionClientUserId != null) {
                        var decisionUser =
                            clientUserLookup.GetValueOrDefault(booking.DecisionClientUserId, new EFCore.Models.Fleet.ClientUser());
                        rejectedManagers.Add(new ClientUser {
                            ClientUserId = booking.DecisionClientUserId,
                            Username = decisionUser.UserName,
                            Email = decisionUser.EMail ?? string.Empty
                        });
                    }
                    else {
                        // System decline case
                        rejectedManagers.Add(new ClientUser {
                            ClientUserId = "", Username = "System"
                        });
                    }
                }
            }

            vehicleBooking.RejectedBy = rejectedManagers;

            // Set the vehicle commander
            if (vehicleCommanderByBookingId.TryGetValue(booking.BookingId, out var commanderId)) {
                var commander = clientUserLookup.GetValueOrDefault(commanderId, new EFCore.Models.Fleet.ClientUser());
                vehicleBooking.VehicleCommander = new ClientUser {
                    ClientUserId = commanderId,
                    Username = commander.UserName,
                    Email = commander.EMail ?? string.Empty
                };
            }

            return (VehicleBookingBase)vehicleBooking;
        }).ToArray();

        return new BookingsResult {
            Bookings = result,
            Total = total,
            StatusCounts = statusCountLookup
        };
    }

    private static SearchTextFilterItem? ExtractSearchTextFilterItem(ServerRequestModel serverRequestModel) {
        var searchText = serverRequestModel?.QuickSearch?.SearchText;

        if (string.IsNullOrEmpty(searchText)) {
            return null;
        }

        return serverRequestModel?.QuickSearch;
    }

    private async Task<IQueryable<EFCore.Models.Pool.Booking>> ApplySearchTextFilterAsync(
        IQueryable<EFCore.Models.Pool.Booking> bookingsQuery,
        SearchTextFilterItem searchTextFilter,
        long userId) {
        var searchText = searchTextFilter.SearchText;
        var searchLower = searchText.ToLower();

        // search vehicle registration
        var matchingVehicleIds = await this._ctDbContext.Vehicles
            .Where(v => v.UserId == userId && v.Registration.ToLower().Contains(searchLower))
            .Select(v => v.VehicleId)
            .ToListAsync();

        // search driver name and email
        var matchingDriverIds = await this._fleetDbContext.ClientDrivers
            .Where(d => d.UserId == userId)
            .Where(d =>
                d.DriverName.ToLower().Contains(searchLower) ||
                (d.DriverSurname != null && d.DriverSurname.ToLower().Contains(searchLower)) ||
                d.EMail != null && d.EMail.ToLower().Contains(searchLower)
            )
            .Select(d => d.ClientDriverId)
            .ToListAsync();

        var matchedClientUserIds = await this._fleetDbContext.ClientUsers
            .Where(vt => vt.UserId == userId)
            .Where(vt =>
                !string.IsNullOrEmpty(vt.EMail) && vt.EMail.ToLower().Contains(searchLower))
            .Select(vt => vt.ClientUserId).ToListAsync();

        var matchedVehicleCommanderBookingIds = await this._tfmsCustomDbContext.ScdfBookingAdditionalInfos
            .Where(info => info.BookingId.HasValue
                           && !string.IsNullOrEmpty(info.VehicleCommanderClientUserId)
                           && matchedClientUserIds.Contains(info.VehicleCommanderClientUserId))
            .Select(info => info.BookingId.Value)
            .ToListAsync();

        var matchedBookingTypeIds = await this._poolDbContext.BookingVehicleTypes
            .Where(vt => vt.BookingVehicleType1.ToLower().Contains(searchLower))
            .Select(vt => vt.BookingVehicleTypeId)
            .ToListAsync();

        var matchedPurposeIds = await this._poolDbContext.BookingPurposes
            .Where(bp => bp.BookingPurpose1.ToLower().Contains(searchLower))
            .Select(bp => bp.BookingPurposeId)
            .ToListAsync();

        // search purpose, requestor, vehicle category id
        bookingsQuery = bookingsQuery.Where(b =>
            matchedVehicleCommanderBookingIds.Contains(b.BookingId) ||
            b.BookingId.ToString().ToLower().Contains(searchLower) ||
            (b.VehicleId.HasValue && matchingVehicleIds.Contains(b.VehicleId.Value)) ||
            (b.BookingPurposeId.HasValue && matchedPurposeIds.Contains(b.BookingPurposeId.Value)) ||
            (b.BookingVehicleTypeId.HasValue && matchedBookingTypeIds.Contains(b.BookingVehicleTypeId.Value)) ||
            (!string.IsNullOrEmpty(b.RequestClientUserId) && matchedClientUserIds.Contains(b.RequestClientUserId)) ||
            (!string.IsNullOrEmpty(b.RequestClientDriverId) && matchingDriverIds.Contains(b.RequestClientDriverId))
        );
        return bookingsQuery;
    }

    private IQueryable<EFCore.Models.Pool.Booking> ApplySorting(IQueryable<EFCore.Models.Pool.Booking> bookingsQuery, SortOption? sort) {
        if (sort != null) {
            bookingsQuery = sort.ParsedField switch {
                SortField.pickUpAt => sort.ParsedSort == SortDirection.asc
                    ? bookingsQuery.OrderBy(b => b.PickupIgnitionTs)
                    : bookingsQuery.OrderByDescending(b => b.PickupIgnitionTs),

                SortField.returnedAt => sort.ParsedSort == SortDirection.asc
                    ? bookingsQuery.OrderBy(b => b.ReturnedIgnitionTs)
                    : bookingsQuery.OrderByDescending(b => b.ReturnedIgnitionTs),

                SortField.startDate => sort.ParsedSort == SortDirection.asc
                    ? bookingsQuery.OrderBy(b => b.StartTs)
                    : bookingsQuery.OrderByDescending(b => b.StartTs),

                SortField.endDate => sort.ParsedSort == SortDirection.asc
                    ? bookingsQuery.OrderBy(b => b.EndTs)
                    : bookingsQuery.OrderByDescending(b => b.EndTs),

                // Default is requestedTime
                _ => sort.ParsedSort == SortDirection.asc
                    ? bookingsQuery.OrderBy(b => b.RequestTs)
                    : bookingsQuery.OrderByDescending(b => b.RequestTs)
            };
        }
        else {
            bookingsQuery = bookingsQuery.OrderByDescending(b => b.RequestTs);
        }

        return bookingsQuery;
    }
}