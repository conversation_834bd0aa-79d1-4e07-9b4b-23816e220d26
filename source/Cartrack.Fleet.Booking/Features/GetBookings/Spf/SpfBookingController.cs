﻿using Cartrack.Fleet.Booking.Features.GetBookings.Spf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class SpfBookingController {
    [HttpGet]
    public async Task<ActionResult<SpfGetBookingsResponse>>
        GetBooking([FromQuery] int offset, [FromQuery] int pageSize) {
        var resp = await this._mediator.Send(new SpfGetBookingsRequest(offset, pageSize));
        return resp.ToContentResult<SpfGetBookingsResponse, Booking[]>(this.HttpContext);
    }
}