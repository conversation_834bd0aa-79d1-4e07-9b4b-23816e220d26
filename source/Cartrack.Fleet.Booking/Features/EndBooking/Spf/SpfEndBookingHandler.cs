﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.EndBooking.Spf;

public class SpfEndBookingHandler(IBookingRepository repo, IHttpContextAccessor context, ILogger<SpfEndBookingHandler> logger)
    : IRequestHandler<SpfEndBookingRequest, SpfEndBookingResponse> {
    public async Task<SpfEndBookingResponse> Handle(SpfEndBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] rejecting a booking", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var booking = await repo.GetBooking(request.Account, request.BookingId);
            await booking?.Status.Approve()!;
            await repo.UpdateStatus(booking.BookingId, BookingStatusCode.Approved);
            var statusHttp = new BookingStatus(booking.BookingId, Constants.Approved);
            return new SpfEndBookingResponse(statusHttp);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error reject booking", context.HttpContext?.TraceIdentifier ?? "");
            return new SpfEndBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error reject booking", context.HttpContext?.TraceIdentifier ?? "");
            return new SpfEndBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private SpfVehicleBooking ApproveFromRequest(SpfEndBookingRequest request) {
        throw new NotImplementedException();
    }
}