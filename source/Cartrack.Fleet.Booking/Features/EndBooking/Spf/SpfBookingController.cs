﻿using Cartrack.Fleet.Booking.Features.EndBooking.Spf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class SpfBookingController {
    [HttpPost]
    [Route("complete")]
    public async Task<ActionResult<SpfEndBookingResponse>> SpfEndBooking(long id,
        [FromBody] SpfEndBookingRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.BookingId = id;
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<SpfEndBookingResponse, BookingStatus>(this.HttpContext);
    }
}