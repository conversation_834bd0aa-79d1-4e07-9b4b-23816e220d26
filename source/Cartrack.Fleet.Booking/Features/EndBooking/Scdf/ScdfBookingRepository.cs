﻿

using Microsoft.EntityFrameworkCore;
// ReSharper disable once CheckNamespace
using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class ScdfBookingRepository {
    public async Task<long> EndBooking(VehicleBookingBase booking) {
        long bookingId = booking.BookingId;
        var myBooking = await _poolDbContext.Bookings.FindAsync(bookingId);
        if (myBooking == null) {
            return -1;
        }
        
        myBooking.BookingStatusId = (long)booking.BookingStatusId!;
        myBooking.Requestor = booking.RequestedBy.Username;
        myBooking.VehicleId = booking.VehicleId;
        myBooking.UpdatedTs = DateTime.UtcNow;
        myBooking.EndTs = booking.EndDate;
        myBooking.RequestClientDriverId = booking.RequestClientDriverId;
        myBooking.ReturnedClientDriverId = booking?.RequestClientDriverId;
        myBooking.ReturnedClientUserId = booking?.ReturnedClientUserId;
        myBooking.ReturnedIgnitionTs = booking?.ReturnedIgnitionTs;
        myBooking.CompletedTs = DateTime.UtcNow;
        myBooking.CompletedClientUserId = booking?.CompletedClientUserId;
        myBooking.BookingAuditReference = booking?.BookingAuditReference;
        
        await _poolDbContext.SaveChangesAsync();

        // ReSharper disable once IdentifierTypo
        var scdfBookingInfo = await _tfmsCustomDbContext.ScdfBookingAdditionalInfos.Where(x => x.BookingId == bookingId).FirstOrDefaultAsync();
        if (scdfBookingInfo == null) {
            return bookingId;
        }
        
        scdfBookingInfo.ReturnedVehicleCommanderClientUserId = booking?.ReturnedVehicleCommanderClientUserId;
        scdfBookingInfo.BookingAuditReference = booking?.BookingAuditReference;
        
        await _tfmsCustomDbContext.SaveChangesAsync();

        return bookingId;
    }
}