﻿using Cartrack.Fleet.Booking.Features.EndBooking.Scdf;
using Cartrack.Fleet.Booking.IO.Http.CustomAuthorization;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Cartrack.Fleet.Booking.IO.Http;

// ReSharper disable once IdentifierTypo
public partial class ScdfBookingController {
    [HttpPatch]
    [Route("completed")]
    [AuthorizeIssuance(AuthorizeIssuanceCheck.CanEndBooking)]
    public async Task<ActionResult<ScdfEndBookingResponse>> ScdfEndBooking([FromBody] ScdfEndBookingRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.CompletedClientUserId = authClaims.ClientUserId;
        
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfEndBookingResponse, Booking>(this.HttpContext);
    }
}