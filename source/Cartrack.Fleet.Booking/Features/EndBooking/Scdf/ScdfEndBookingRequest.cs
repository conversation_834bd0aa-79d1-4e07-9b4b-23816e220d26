﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.EndBooking.Scdf;

// ReSharper disable once IdentifierTypo
public record ScdfEndBookingRequest() : IRequest<ScdfEndBookingResponse> {
    [JsonIgnore] public string? Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; }
    public long BookingId { get; set; }
    public DateTime DropOffTime { get; set; }
    public DateTime ActualDropOffTime { get; set; }
    public long VehicleId { get; set; }
    public string? ReturnedClientDriverId { get; set; }
    public string? ReturnedVehicleCommanderClientUserId {get;set;}
    public string? CompletedClientUserId { get; set; }
}