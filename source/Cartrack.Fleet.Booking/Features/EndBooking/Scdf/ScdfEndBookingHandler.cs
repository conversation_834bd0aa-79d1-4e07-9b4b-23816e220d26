﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

// ReSharper disable once IdentifierTypo
namespace Cartrack.Fleet.Booking.Features.EndBooking.Scdf;

// ReSharper disable once IdentifierTypo
public class ScdfEndBookingHandler( 
    ScdfBookingRepository repo, 
    IBookingRuleRepository rule,
    IDriverRepository driverRepo, 
    IVehicleRepository vehicleRepo,
    ScdfVehicleBookingBuilder bookingBuilder,
    IHttpContextAccessor context,
    ILogger<ScdfEndBookingHandler> logger) : IRequestHandler<ScdfEndBookingRequest, ScdfEndBookingResponse> {
    public async Task<ScdfEndBookingResponse> Handle(ScdfEndBookingRequest request, CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() =>  request.BookingId != 0, "Booking Id is Required.");
            logger.LogInformation("[{TraceId}] [{Agency}] Complete a booking", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var booking = await repo.GetBooking(bookingBuilder, request.BookingId);
            if (booking is null) {
                return new ScdfEndBookingResponse(null, new NotFoundException($"Booking {request.BookingId} not found")) { IsServerError = false };
            }
            
            //1. Update the existing booking with new information from the request
            await bookingBuilder.UpdateBookingDetails(request, repo, rule, driverRepo, vehicleRepo);
            booking.IsVehicleCommonPoolRuleAllowed = await this.IsVehicleCommonPoolRuleAllowed(booking);
            
            if (request.ActualDropOffTime > request.DropOffTime.AddMinutes(1)) {
                await booking?.Status.ReturnLate()!;
            }
            else {
                await booking?.Status.Return()!;
            }
            
            var bookingId = await repo.EndBooking(booking);
            
            await repo.UpdateClientDriverIdToTerminalTripBasedOnBooking(booking);
            
            var updatedBooking = await repo.GetBooking(request.Account, bookingId);
            return new ScdfEndBookingResponse(updatedBooking?.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error complete/end booking", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfEndBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error complete/end booking", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfEndBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }
    
    private async Task<bool> IsVehicleCommonPoolRuleAllowed(VehicleBookingBase booking) {
        var clientDriverDepartments = await driverRepo.GetDepartmentsByDriverId(booking.UserId, booking.RequestClientDriverId ?? "");
        var vehicleDepartments = await vehicleRepo.GetDepartments(booking.UserId, booking.VehicleId ?? 0);
        bool result = await repo.IsVehicleCommonPoolRuleAllowed(booking.Vehicle.CommonPool, clientDriverDepartments, vehicleDepartments);
        return result;
    }
}