﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf;

public record ScdfUploadSingleImageBookingRequest : IRequest<ScdfUploadSingleImageBookingResponse> {
    [JsonIgnore] public string Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; }
    public string? ImageBase64 { get; set; }
    public string? Extension { get; set; }
    [JsonIgnore] public string ClientUserId { get; set; } = "";
}