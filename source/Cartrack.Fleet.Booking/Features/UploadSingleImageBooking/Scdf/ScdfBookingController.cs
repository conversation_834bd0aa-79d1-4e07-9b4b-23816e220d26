﻿using Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpPost]
    [Route("uploadSingleImageTemp")]
    public async Task<ActionResult<ScdfUploadSingleImageBookingResponse>> UploadSingleImageBooking([FromBody] ScdfUploadSingleImageBookingRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = authClaims.ClientUserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfUploadSingleImageBookingResponse, TempUploadImageStatus>(this.HttpContext);
    }
}