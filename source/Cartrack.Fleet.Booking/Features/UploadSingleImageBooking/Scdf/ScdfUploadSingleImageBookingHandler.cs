﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf;

public class ScdfUploadSingleImageBookingHandler(
    IBookingRepository repo,
    IHttpContextAccessor context,
    ILogger<ScdfUploadSingleImageBookingHandler> logger)
    : IRequestHandler<ScdfUploadSingleImageBookingRequest, ScdfUploadSingleImageBookingResponse> {
    public async Task<ScdfUploadSingleImageBookingResponse> Handle(ScdfUploadSingleImageBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Uploading temporary single image to server", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var tempAttachment = await repo.TempUploadSingleImage(request.ImageBase64, request.Extension);

            var statusHttp = new TempUploadImageStatus(
                tempAttachment.TempFileAttachmentGuid ?? "",
                tempAttachment.Extension ?? "",
                tempAttachment.Status ?? "",
                tempAttachment.ErrorMessage ?? "");

            return new ScdfUploadSingleImageBookingResponse(statusHttp);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error uploading temporary single image to server", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfUploadSingleImageBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error uploading temporary single image", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfUploadSingleImageBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}