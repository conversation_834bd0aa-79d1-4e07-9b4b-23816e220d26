﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Features.GetForceTerminateBookingReasons;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<List<ForceTerminateBookingReason>>
        GetForceTerminateBookingReasons(long userId, long forceTerminateBookingReasonId) {
        
        var query = this._poolDbContext.BookingForceTerminateReasons
            .Where(x => x.UserId == userId && !x.IsDeleted);

        if (forceTerminateBookingReasonId != 0) {
            query = query.Where(x => x.BookingForceTerminateReasonId == forceTerminateBookingReasonId);
        }
        
        var x = await query.ToListAsync() ?? null;
        
        if (x is null) {
            return new List<ForceTerminateBookingReason>();
        }

        List<ForceTerminateBookingReason> output = x.Select(p => new ForceTerminateBookingReason {
            Id = p.BookingForceTerminateReasonId,
            Title = p.BookingForceTerminateReason1 ?? string.Empty,
            Description = ""
        }).ToList();

        return output;
    }
}