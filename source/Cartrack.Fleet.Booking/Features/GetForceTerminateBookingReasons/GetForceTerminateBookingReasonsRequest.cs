﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.GetForceTerminateBookingReasons;

public record GetForceTerminateBookingReasonsRequest(long ForceTerminateBookingReasonId = 0) : IRequest<GetForceTerminateBookingReasonsResponse> {
    [JsonIgnore] public string? Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; }
    [JsonIgnore] public string ClientUserId { get; set; } = "";
}