﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.UpdateBooking.Spf;

public class SpfUpdateBookingHandler(IBookingRepository repo, IHttpContextAccessor context, ILogger<SpfUpdateBookingHandler> logger)
    : IRequestHandler<SpfUpdateBookingRequest, SpfUpdateBookingResponse> {
    public async Task<SpfUpdateBookingResponse> Handle(SpfUpdateBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Updating a booking", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            VehicleBookingBase booking = await this.CreateFromRequest(request);
            var bookingId = await repo.UpdateBooking(booking);
            var createdBooking = await repo.GetBooking(request.Account, bookingId);
            return new SpfUpdateBookingResponse(createdBooking.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error updating booking", context.HttpContext?.TraceIdentifier ?? "");
            return new SpfUpdateBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error updating booking", context.HttpContext?.TraceIdentifier ?? "");
            return new SpfUpdateBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private async Task<SpfVehicleBooking> CreateFromRequest(SpfUpdateBookingRequest request) {
        //1. Create all the booking rules that applies to SPF
        var booking = new SpfVehicleBooking();
        foreach (var r in this.GetCommonBookingRules()) {
            booking.PreConditions.Add(r);
        }

        foreach (var r in this.GetSpfBookingRules()) {
            booking.PreConditions.Add(r);
        }

        //2. Add the journey. For SPF, StartLocation == DropOffLocation
        //booking.Journeys.Add(new Journey()); 

        //3. Validate the booking.  Execute all the pre-conditions
        await booking.Validate();

        return booking;
    }

    private List<IBookingRule> GetCommonBookingRules() {
        return [];
    }

    private List<IBookingRule> GetSpfBookingRules() {
        return [];
    }
}