﻿using Cartrack.Fleet.Booking.Features.UpdateBooking.Spf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class SpfBookingController {
    [HttpPatch]
    [Route("update/{bookingId:long}")]
    public async Task<ActionResult<SpfUpdateBookingResponse>>
        UpdateBooking([FromBody] SpfUpdateBookingRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<SpfUpdateBookingResponse, Booking>(this.HttpContext);
    }
}