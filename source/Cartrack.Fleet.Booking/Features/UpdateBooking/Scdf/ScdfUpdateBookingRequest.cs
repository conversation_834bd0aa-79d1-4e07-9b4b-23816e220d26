﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf;

public record ScdfUpdateBookingRequest : IRequest<ScdfUpdateBookingResponse> {
    [JsonIgnore] public string Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; }
    [JsonIgnore] public string? LoginClientUserId{get;set;}
    [JsonIgnore] public long BookingId { get; set; }
    public long BookingPurposeId { get; set; }
    public string? BookingPurposeDescription { get; set; }
    public string? Requestor { get; set; }

    public bool IsBookingForOtherParty { get; set; } = false;
    //public string? RequestClientUserId { get; set; } // this will be taken from the JWT/Auth
    public string? RequestedForClientUserId { get; set; } // Requested For
    public DateTime StartTs { get; set; }
    public DateTime EndTs { get; set; }
    public long? BookingVehicleTypeId { get; set; }
    public long? VehicleId { get; set; } = 0;
    public DriverType DriverType { get; set; } = DriverType.AnyDriver;
    public string? RequestClientDriverId { get; set; }
    public EquipmentType? EquipmentType { get; set; }
    public List<int>? Accessories { get; set; }
    public string? Remarks { get; set; }
    public int PickupSiteLocationId { get; set; }
    public JourneyType JourneyType { get; set; }

    public required List<JourneyLocation> Journeys { get; set; } = [];

    //tfmsCustom.Scdf_booking_additional_info
    public VehicleCommanderType VehicleCommanderType { get; set; } = VehicleCommanderType.AnyCommander;
    public string? VehicleCommanderClientUserId { get; set; }
    public int NumberOfPassengers { get; set; } = 0;
    public List<InputEquipmentAttachment>? EquipmentAttachments { get; set; } // for old attachment link to the booking
    public List<int>? EquipmentAttachmentIds { get; set; } // for existing attachment link to the booking
}