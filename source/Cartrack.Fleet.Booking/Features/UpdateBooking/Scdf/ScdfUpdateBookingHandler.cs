﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.Pool;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Common.States;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.User.IO.Sql;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using BookingAccessory = Cartrack.Fleet.Booking.Domain.Common.BookingAccessory;

namespace Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf;

public class ScdfUpdateBookingHandler(
    ScdfBookingRepository repo,
    ScdfVehicleBookingBuilder bookingBuilder,
    IBookingRuleRepository rule,
    IDriverRepository driverRepo,
    IVehicleRepository vehicleRepo,
    IUserRepository userRepo, 
    IUserAppSettingsRepository userAppSettingsRepo,
    IHttpContextAccessor context,
    ILogger<ScdfUpdateBookingHandler> logger) : IRequestHandler<ScdfUpdateBookingRequest, ScdfUpdateBookingResponse> {
    public async Task<ScdfUpdateBookingResponse> Handle(ScdfUpdateBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() =>  request.BookingPurposeId != 0, () => "Purpose of Request is Required.");
            Requires.IsTrue(() =>  request.BookingId != 0, () => "Booking Id is Required.");
            Requires.IsTrue(() => request.EndTs is { } time, () => "End Date is Required.");
            Requires.IsTrue(() => request.StartTs is { } time, () => "Start Date is Required.");
            Requires.IsTrue(() => request.EndTs >= request.StartTs, () => "End Date must be greater than Start Date.");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Updating a booking", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var booking = await repo.GetBooking(bookingBuilder, request.BookingId);
            if (booking is null) {
                return new ScdfUpdateBookingResponse(null, new Exception($"Booking {request.BookingId} not found")) { IsServerError = false };
            }

            //1. Update the existing booking with new information from the request 
            //request.RequestClientUserId = booking.RequestedBy.ClientUserId;
            await bookingBuilder.UpdateBookingDetails(request, repo, rule, driverRepo, vehicleRepo, userRepo, userAppSettingsRepo); 
            await bookingBuilder.VehicleBooking!.Validate();
            await repo.UpdateBooking(booking);
            await repo.UpdateClientDriverIdToTerminalTripBasedOnBooking(booking);
            var updatedBooking = await repo.GetBooking(request.Account, booking.BookingId);
            return new ScdfUpdateBookingResponse(updatedBooking?.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value in update booking. Parameter is Required", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfUpdateBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error update booking", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfUpdateBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}