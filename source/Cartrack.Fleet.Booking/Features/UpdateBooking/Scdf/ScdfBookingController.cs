﻿using Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpPatch]
    [Route("update/{bookingId:long}")]
    public async Task<ActionResult<ScdfUpdateBookingResponse>> UpdateBooking(long bookingId, [FromBody] ScdfUpdateBookingRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        //request.RequestClientUserId = authClaims.ClientUserId;
        request.LoginClientUserId = authClaims.ClientUserId;
        request.BookingId = bookingId;
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfUpdateBookingResponse, Booking>(this.HttpContext);
    }
}