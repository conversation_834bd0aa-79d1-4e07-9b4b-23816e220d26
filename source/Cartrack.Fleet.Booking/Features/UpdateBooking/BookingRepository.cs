﻿using Cartrack.EFCore.Models.Pool;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Microsoft.EntityFrameworkCore;
using Npgsql;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<long> UpdateBooking(VehicleBookingBase booking) {
        long bookingId = booking.BookingId;
        var myBooking = await this._poolDbContext.Bookings.FindAsync(bookingId);
        if (myBooking == null) {
            return -1;
        }
        
        // EDIT mode when booking_status_id is never changed.
        bool editMode = myBooking.BookingStatusId == booking.Status.Id;

        if (this.IsBookingStartTsCrossingPartitionMonthYear(myBooking, booking)) {
            // TODO : Since we have Booking Partition Rules, we need to take care of this if StartTs changed month / year.
            //         
            // Create a New Clone Object
            // Remove the existing row
            //this._poolDbContext.Bookings.Remove(existingBooking);
            // await this._poolDbContext.SaveChangesAsync();
        }
        
        if (booking.BookingStatusId != null && booking.BookingStatusId != BookingStatusCode.Unknown) {
            myBooking.BookingStatusId = (long)booking.BookingStatusId;
        }

        var vehicleId = (booking.Target as VehicleTarget)?.Vehicle?.VehicleId;
        if (vehicleId != null) {
            myBooking.VehicleId = vehicleId;
        }

        myBooking.BookingVehicleTypeId = booking.BookingVehicleTypeId;
        myBooking.BookingPurposeId = booking.BookingPurposeId;
        myBooking.BookingPurposeDescription = booking.BookingPurposeDescription;
        myBooking.PickupSiteLocationId = booking.PickupSiteLocationId ?? 0;
        if (booking.JourneyType == JourneyType.Return) {
            myBooking.ReturnSiteLocationId = booking.PickupSiteLocationId ?? 0;
        }

        myBooking.StartTs = booking.StartDate;
        myBooking.EndTs = booking.EndDate;
        myBooking.RequestClientDriverId = booking.RequestClientDriverId;
        myBooking.RequestClientUserId = booking.RequestClientUserId;
        //myBooking.Requestor = booking.Requestor; // username, must catch in JWT
        myBooking.Remarks = booking.Remarks;
        myBooking.BookingAuditReference = booking.BookingAuditReference;
        myBooking.UpdatedTs = DateTime.UtcNow;
        if (booking?.IsApproved != null && !editMode) {
            myBooking.IsApproved = booking.IsApproved;
            myBooking.DecisionClientUserId = (booking.IsApproved) ? booking.ApprovedBy[0].ClientUserId : booking.RejectedBy[0].ClientUserId;
            myBooking.DecisionTs = DateTime.UtcNow;
        }
        
        await this._poolDbContext.SaveChangesAsync();

        // Edit into SCDF Custom Booking Additional Info
        var b = await this._tfmsCustomDbContext.ScdfBookingAdditionalInfos.FirstOrDefaultAsync(b => b.BookingId == bookingId);
        if (b != null) {
            b.PassengerCount = booking.NumberOfPassengers;
            b.VehicleCommanderClientUserId = booking.VehicleCommanderClientUserId;
            b.RequestedForClientUserId = booking.RequestedForClientUserId;
            b.LocationType = (int)booking.JourneyType;
            b.BookingAuditReference = booking.BookingAuditReference;
            
            if (booking.DriverType != null) { b.DriverType = (int)booking.DriverType; }
            if (booking.VehicleCommanderType != null) { b.VehicleCommanderType = (int)booking.VehicleCommanderType; }
            if (booking.EquipmentType1 != null) { b.EquipmentType = (int)booking.EquipmentType1; }
        }

        await this._tfmsCustomDbContext.SaveChangesAsync();

        // Journey Related
        await this.AddOrUpdateBookingJourneys(myBooking.UserId, bookingId, booking.BookingAuditReference, booking.Journeys);

        // Accessories Related
        if (booking.AccessoryIds is { Count: > 0 }) {
            await this.AddOrUpdateBookingAccessory(bookingId, booking.BookingAuditReference, booking.AccessoryIds);
        }

        // Booking Attachment
        // Existing Booking Attachment Ids
        if (booking.EquipmentAttachmentIds != null) {
            await this.UpdateBookingEquipmentAttachmentIds(bookingId, booking.BookingAuditReference ,booking.EquipmentAttachmentIds);
        }

        // New Booking Attachments
        if (booking.EquipmentAttachments is { Count: > 0 }) {
            await this.AddBookingEquipmentAttachments(bookingId, booking.BookingAuditReference, booking.EquipmentAttachments);
        }

        return bookingId;
    }

    private bool IsBookingStartTsCrossingPartitionMonthYear(EFCore.Models.Pool.Booking? existingBooking, VehicleBookingBase newUpdateBooking) {
        bool isCrossingPartition = existingBooking?.StartTs.Month != newUpdateBooking.StartDate.Month
                                   || existingBooking.StartTs.Year != newUpdateBooking.StartDate.Year;
        return isCrossingPartition;
    }
}