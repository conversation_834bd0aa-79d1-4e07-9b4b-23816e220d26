﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf;

public class ScdfDeleteTempSingleImageBookingHandler(
    ScdfBookingRepository repo,
    IHttpContextAccessor context,
    ILogger<ScdfDeleteTempSingleImageBookingHandler> logger)
    : IRequestHandler<ScdfDeleteTempSingleImageBookingRequest, ScdfDeleteTempSingleImageBookingResponse> {
    public async Task<ScdfDeleteTempSingleImageBookingResponse> Handle(ScdfDeleteTempSingleImageBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Deleting temporary single image from server", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var tempAttachment = await repo.DeleteTempUploadSingleImage(request.Guid, request.Extension);

            var statusHttp = new TempDeleteImageStatus(
                tempAttachment.TempFileAttachmentGuid ?? "",
                tempAttachment.Extension ?? "",
                "OK");

            return new ScdfDeleteTempSingleImageBookingResponse(statusHttp);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error uploading temporary single image to server", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfDeleteTempSingleImageBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error uploading temporary single image", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfDeleteTempSingleImageBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private SpfVehicleBooking ApproveFromRequest(ScdfDeleteTempSingleImageBookingRequest request) {
        throw new NotImplementedException();
    }
}