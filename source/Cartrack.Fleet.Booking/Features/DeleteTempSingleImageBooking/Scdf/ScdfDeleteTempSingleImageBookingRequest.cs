﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf;

public record ScdfDeleteTempSingleImageBookingRequest : IRequest<ScdfDeleteTempSingleImageBookingResponse> {
    [JsonIgnore] public string Account { get; set; } = "";

    [JsonIgnore] public long UserId { get; set; }
    public string? Guid { get; set; }
    public string? Extension { get; set; }
    [JsonIgnore] public string DeletedByClientUserId { get; set; } = "";
}