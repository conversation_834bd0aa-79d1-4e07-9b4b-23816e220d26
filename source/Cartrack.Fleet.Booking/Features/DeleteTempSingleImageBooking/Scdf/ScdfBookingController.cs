﻿using Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpPost]
    [Route("deleteTempSingleImage")]
    public async Task<ActionResult<ScdfDeleteTempSingleImageBookingResponse>> DeleteTempSingleImageBooking([FromBody] ScdfDeleteTempSingleImageBookingRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.DeletedByClientUserId = authClaims.ClientUserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfDeleteTempSingleImageBookingResponse, TempDeleteImageStatus>(this.HttpContext);
    }
}