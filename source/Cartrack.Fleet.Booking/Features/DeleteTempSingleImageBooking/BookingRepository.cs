﻿using Minio;
using Minio.DataModel.Args;
using Cartrack.EFCore.Models.Pool;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using Npgsql;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<BookingAttachments> DeleteTempUploadSingleImage(string? uuid, string? extension) {
        var output = new BookingAttachments();
        if (uuid is null || extension is null) {
            return output;
        }

        MinIoHandler minIo = new MinIoHandler(this._minioClient,this._appSettings, this._user, this._logger);
        MinIoResponseResult result = await minIo.TempDeleteBase64Image(uuid, extension);

        output = new BookingAttachments {
            TempFileAttachmentGuid = result.Guid,
            ContentType = result.ContentType,
            Extension = result.Extension
        };
        return output;
    }
}