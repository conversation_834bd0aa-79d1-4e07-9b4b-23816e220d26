﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Features.GetCancelBookingReasons;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Http;

public partial class BookingController {
    [HttpGet]
    [Route("cancel-booking-reasons")]
    public async Task<ActionResult<GetCancelBookingReasonsResponse>> GetCancelBookingReasons() {
        var request = new GetCancelBookingReasonsRequest();

        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = authClaims.ClientUserId;
        
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetCancelBookingReasonsResponse, CancelBookingReasons>(this.HttpContext);
    }
}