﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.GetCancelBookingReasons;

public record GetCancelBookingReasonsRequest(long CancelBookingReasonId = 0) : IRequest<GetCancelBookingReasonsResponse> {
    [JsonIgnore] public string? Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; }
    [JsonIgnore] public string ClientUserId { get; set; } = "";
}