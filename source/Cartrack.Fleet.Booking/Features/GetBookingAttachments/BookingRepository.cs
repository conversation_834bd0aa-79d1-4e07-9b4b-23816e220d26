﻿using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Minio.DataModel.Args;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<List<BookingAttachments>> GetBookingAttachments(long bookingId) {
        var attachments = await this._poolDbContext.BookingAttachments
            .Where(ba => ba.BookingId == bookingId)
            .ToListAsync();

        var result = new List<BookingAttachments>();

        foreach (var attachment in attachments) {
            var bookingAttachment = new BookingAttachments {
                Id = attachment.Id,
                BookingId = attachment.BookingId ?? 0,
                UrlAttachment = attachment.UrlAttachment,
                FileName = attachment.Filename
            };

            // read the file and convert to Base64
            if (!string.IsNullOrEmpty(attachment.UrlAttachment)) {
                try {
                    MinIoHandler minIo = new MinIoHandler(this._minioClient, this._appSettings, this._user, this._logger);

                    Uri uri = new Uri(attachment.UrlAttachment);
                    string bucketName = uri.Segments[1].TrimEnd('/');
                    string objectName = string.Join("", uri.Segments.Skip(2));

                    var getObjectArgs = new GetObjectArgs()
                        .WithBucket(bucketName)
                        .WithObject(objectName)
                        .WithCallbackStream(stream => {
                            using var memoryStream = new MemoryStream();
                            stream.CopyTo(memoryStream);
                            byte[] fileBytes = memoryStream.ToArray();
                            bookingAttachment.Base64Content = Convert.ToBase64String(fileBytes);
                        });

                    await this._minioClient.GetObjectAsync(getObjectArgs);
                }
                catch (Exception ex) {
                    this._logger.LogError(ex, "Failed to fetch booking assignment(s)");
                    bookingAttachment.Base64Content = null;
                }
            }

            result.Add(bookingAttachment);
        }

        return result;
    }
}