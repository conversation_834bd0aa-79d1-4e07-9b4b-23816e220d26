﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Http.Filters;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Vehicle.IO.Http.Filters;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.GetBookingAttachments.Scdf;

public class ScdfGetBookingAttachmentsHandler(AppSettings appSettings, ScdfBookingRepository repo, IHttpContextAccessor context, ILogger<ScdfGetBookingAttachmentsHandler> logger)
    : IRequestHandler<ScdfGetBookingAttachmentsRequest, ScdfGetBookingAttachmentsResponse> {
    public async Task<ScdfGetBookingAttachmentsResponse> Handle(ScdfGetBookingAttachmentsRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.Id > 0, "Booking ID must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] Retrieving booking attachments for booking {BookingId}", context.HttpContext?.TraceIdentifier ?? "", request.Id);
            
            var bookingAttachments = await repo.GetBookingAttachments(request.Id);
            var bookingContext = context.GetBookingContext();
            var apiBookingAttachments = bookingAttachments.Select(ba => new BookingAttachment {
                Id = ba.Id,
                BookingId = ba.BookingId,
                UrlAttachment = GetUrl(ba.UrlAttachment ?? ""),
                FileName = ba.FileName,
                Base64Content = ba.Base64Content
            }).ToArray();

            return new ScdfGetBookingAttachmentsResponse(apiBookingAttachments);

            string GetUrl(string originalUrl) {
                var minio = appSettings.GetStorageSettings(bookingContext.Claims).MinIo;
                if (string.IsNullOrWhiteSpace(minio.Proxy))
                    return originalUrl;

                var uri = new Uri(originalUrl);
                return originalUrl.Replace(uri.Host, minio.Proxy).Replace($":{uri.Port}", "");
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving booking attachments", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfGetBookingAttachmentsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving booking attachments", context.HttpContext?.TraceIdentifier ?? "");
            return new ScdfGetBookingAttachmentsResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}