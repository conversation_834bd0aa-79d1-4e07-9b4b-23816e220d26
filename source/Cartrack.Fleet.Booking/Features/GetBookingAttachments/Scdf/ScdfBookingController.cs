using Cartrack.Fleet.Booking.Features.GetBooking.Scdf;
using Cartrack.Fleet.Booking.Features.GetBookingAttachments.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpGet]
    [Route("{id}/attachments")]
    public async Task<ActionResult<ScdfGetBookingAttachmentsResponse>> GetBookingAttachments(long id) {
        var request = new ScdfGetBookingAttachmentsRequest(id);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = authClaims.ClientUserId;

        
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfGetBookingAttachmentsResponse, BookingAttachment[]>(this.HttpContext);
    }
}