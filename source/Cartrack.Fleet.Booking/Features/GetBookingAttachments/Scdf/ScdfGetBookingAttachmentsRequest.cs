﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.GetBookingAttachments.Scdf;

public record ScdfGetBookingAttachmentsRequest(long Id) : IRequest<ScdfGetBookingAttachmentsResponse> {
    [JsonIgnore] public string Account { get; set; } = "";
    [JsonIgnore]  public long UserId { get; set; }
    [JsonIgnore] public string ClientUserId { get; set; } = "";
}