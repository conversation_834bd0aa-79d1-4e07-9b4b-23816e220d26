﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf;
using Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf;
using Cartrack.Fleet.Booking.Features.CancelBooking.Scdf;
using Cartrack.Fleet.Booking.Features.CreateBooking.Scdf;
using Cartrack.Fleet.Booking.Features.EndBooking.Scdf;
using Cartrack.Fleet.Booking.Features.RejectBooking.Scdf;
using Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf;
using Cartrack.Fleet.Booking.IO.Http.Filters;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.User.IO.Sql;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.EntityFrameworkCore;
using ClientUser = Cartrack.Fleet.Booking.Domain.Common.ClientUser;
using Constants = Cartrack.Fleet.Booking.Domain.Common.Constants;

namespace Cartrack.Fleet.Booking.Domain.Scdf;

public interface IVehicleBookingBuilder {
    Task<VehicleBookingBase?> Build(long bookingId);
}

public class ScdfVehicleBookingBuilder : VehicleBookingBuilder {
    private ScdfBookingAdditionalInfo? _additionalInfo;

    public ScdfVehicleBookingBuilder(
        AppSettings appSettings,
        IMediator mediator,
        IDriverRepository driverRepo,
        AppCtDbContext ctDbContext,
        AppFleetDbContext fleetDbContext,
        AppPoolDbContext poolDbContext,
        IHttpContextAccessor contextAccessor,
        AppTfmsCustomDbContext tfmsCustomDbContext) : base(appSettings, mediator, driverRepo, ctDbContext, fleetDbContext, poolDbContext, tfmsCustomDbContext, contextAccessor) {
    }
    
    public override async Task<VehicleBookingBase?> Build(long id) {
        await this.Start(id);
        await this.IncludeVehicle();
        await this.IncludePickupLocation();
        await this.IncludePurpose();
        await this.IncludeCategory();
        await this.IncludeJourneys();
        await this.IncludeAccessories();
        await this.IncludeAdditionalInfo();
        await this.IncludeVehicleCommander();
        await this.IncludeRequestedBy();
        await this.IncludeCancelledBy();
        await this.IncludeCancellation();
        await this.IncludeApprovedBy();
        await this.IncludeRejectedBy();
        await this.IncludeDriver();
        return this.VehicleBooking;
    }

    public override VehicleBookingBase Create() {
        return new ScdfVehicleBooking();
    }

    public override async Task Start(long bookingId) {
        await base.Start(bookingId);
        
        if (bookingId > 0) {
            this._additionalInfo = (await this._tfmsCustomDbContext.ScdfBookingAdditionalInfos
                .Where(info => info.BookingId == this._booking!.BookingId)
                .ToArrayAsync())
                .FirstOrDefault();
        }
    }

    public async Task IncludeAdditionalInfo() {
        await Task.Yield();
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        
        // Adding other additional info for scdf booking
        if (this._additionalInfo != null) {
            this.VehicleBooking!.RequestedForClientUserId = this._additionalInfo.RequestedForClientUserId?.Trim();
            this.VehicleBooking.LocationType = this._additionalInfo.LocationType;
            this.VehicleBooking.DriverType = this._additionalInfo.DriverType == null ? null : (DriverType)this._additionalInfo.DriverType.Value;
            this.VehicleBooking.VehicleCommanderType = this._additionalInfo.VehicleCommanderType != null ? (VehicleCommanderType)this._additionalInfo.VehicleCommanderType : VehicleCommanderType.AnyCommander;
            this.VehicleBooking.EquipmentType = this._additionalInfo.EquipmentType;
            this.VehicleBooking.NumberOfPassengers = this._additionalInfo.PassengerCount;
        }
    }

    public async Task IncludeVehicleCommander() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Set vehicle commander
        if (this._additionalInfo != null && !string.IsNullOrEmpty(this._additionalInfo?.VehicleCommanderClientUserId?.Trim())) {
            await this.IncludeVehicleCommander(this._additionalInfo.VehicleCommanderClientUserId);
        }
    }

    public async Task IncludeVehicleCommander(string vehicleCommanderClientUserId) {
        vehicleCommanderClientUserId = vehicleCommanderClientUserId.Trim();
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        Requires.NotNullOrEmpty(vehicleCommanderClientUserId, nameof(vehicleCommanderClientUserId));

        // Set vehicle commander

        this.VehicleBooking!.VehicleCommander = new ClientUser {
            ClientUserId = vehicleCommanderClientUserId, 
            Username = (await this.GetClientUser(vehicleCommanderClientUserId))?.UserName ?? ""
        };

        var isSelfCommand = this.VehicleBooking.RequestClientUserId == vehicleCommanderClientUserId;
        this.VehicleBooking.VehicleCommanderType = isSelfCommand ? VehicleCommanderType.Self : VehicleCommanderType.Specific;
        this.VehicleBooking.VehicleCommanderClientUserId = vehicleCommanderClientUserId;
    }

    public async Task IncludeBookingApprovalRules(long userId, IBookingRuleRepository ruleRepository) {
        foreach (var r in await GetBookingApprovalRules(userId, ruleRepository)) {
            if (r.IsEnabled == true) {
                this.VehicleBooking!.PreConditions.Add(r);
            }
        }

        return;

        async Task<List<IBookingRule>> GetBookingApprovalRules(long userId, IBookingRuleRepository ruleRepository) {
            var (userBookingRule, bookingSettings) = await ruleRepository.GetBookingSettingRules(userId);

            List<IBookingRule> bookingRule = [];
            foreach (var r in userBookingRule) {
                switch (r.BookingRuleId) {
                    // case Constants.BookingRuleCodeCheckDriverLicenseClass:
                    //     bookingRule.Add(new MustHaveSpecialLicenseRule(bookingSettings) {
                    //         IsEnabled = r.Status
                    //     });
                    //     break;
                    // case Constants.BookingRuleCodeCheckDriverSpecialLicense:
                    //     bookingRule.Add(new MustHaveQdlLicenseRule(bookingSettings) {
                    //         IsEnabled = r.Status
                    //     });
                    //     break;
                    case Constants.BookingRuleCodeIsDriverRequired:
                        bookingRule.Add(new MustHaveDriverRule(bookingSettings) {
                            IsEnabled = r.Status
                        });
                        break;
                    case Constants.BookingRuleCodeMaximumBookingTime:
                        bookingRule.Add(new MustBeLessThanMaxDurationRule(bookingSettings) {
                            IsEnabled = r.Status
                        });
                        break;
                    case Constants.BookingRuleCodeBookInAdvanceBy:
                        bookingRule.Add(new MustBeLessThanMaxDaysInAdvanceRule(bookingSettings) {
                            IsEnabled = r.Status
                        });
                        break;
                }
            }

            //ADD THE SCDF-specific rules
            bookingRule.Add(new MustHaveDescriptionWhenPurposeIsOthers(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveRequestedForWhenBookingForOtherParty(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHavePassengers(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHavePickupLocation(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveValidJourneys(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveValidBookingTime(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveVehicleRule(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new IsVehicleInCommonPoolRule(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveValidLicenseForVehicle(bookingSettings, _mediator) { IsEnabled = true });
            return bookingRule;
        }
    }
 
    public async Task UpdateBookingDetails(ScdfApproveBookingRequest request, ScdfBookingRepository repo,
        IBookingRuleRepository ruleRepo, IDriverRepository driverRepo, IVehicleRepository vehicleRepo) {
        var booking = (ScdfVehicleBooking)this.VehicleBooking!;
        var journeys = request.Journeys
            .Select((r, journeyOrder) => new Journey {
                LocationReference = new JourneyLocation {
                    Type = r.Type,
                    Value = r.Value
                },
                Order = journeyOrder + 1,
            })
            .ToList();

        
        await this.IncludeJourneys(journeys);
        await this.IncludeVehicle(request.VehicleId);
        await this.IncludePurpose(request.BookingPurposeId);
        booking.BookingPurposeDescription = request.BookingPurposeDescription;
        booking.BookingVehicleTypeId = request.BookingVehicleTypeId;
        booking.StartDate = DateTime.SpecifyKind(request.StartTs.AddSeconds(1), DateTimeKind.Utc);
        booking.EndDate = DateTime.SpecifyKind(request.EndTs, DateTimeKind.Utc);
        booking.IsApproved = true;
        
        await this.IncludeDriver(request.RequestClientDriverId!);

        if (!string.IsNullOrWhiteSpace(request.VehicleCommanderClientUserId))
            await this.IncludeVehicleCommander(request.VehicleCommanderClientUserId);

        await this.IncludePickupLocation(request.PickupSiteLocationId);
        booking.ReturnSiteLocationId = (request.JourneyType == JourneyType.Return ? request.PickupSiteLocationId : 0);
        //UpdatedDate = null,
        booking.RequestedForClientUserId = request.RequestedForClientUserId;
    
        var approvedBy = new List<ClientUser> {
            new ClientUser {
                ClientUserId = request?.ApproveClientUserId, Username = request?.Account
            }
        };

        booking.ApprovedBy = approvedBy;
        //booking.RejectedBy = null;
        booking.ActivationTrigger = null;
        booking.Remarks = request?.Remarks ?? "";
        booking.NumberOfPassengers = request?.NumberOfPassengers;
        booking.EquipmentType1 = request?.EquipmentType;
        booking.AccessoryIds = request?.Accessories ?? null;
        booking.JourneyType = request.JourneyType;

        // Equipment Attachment 
        booking.EquipmentAttachmentIds = request.EquipmentAttachmentIds; // for existing attachment
        booking.EquipmentAttachments = request.EquipmentAttachments; // for new attachments

        booking.IsVehicleCommonPoolRuleAllowed = await IsVehicleCommonPoolRuleAllowed();
        booking.IsBookingTimeConflicting = await IsBookingTimeAvailable();
        booking.IsBookingPurposeOthersRequired = await IsBookingPurposeOthersRequired();
        await this.IncludeBookingApprovalRules(request.UserId, ruleRepo);
        return;

        async Task<bool> IsBookingTimeAvailable() {
            bool result = await repo.IsBookingTimeConflicting(booking);
            return result;
        }

        async Task<bool> IsBookingPurposeOthersRequired() {
            bool result = await repo.IsBookingPurposeOthersRequired(booking);
            return result;
        }

        async Task<bool> IsVehicleCommonPoolRuleAllowed() {
            var vehicle = (booking.Target as VehicleTarget)?.Vehicle;
            bool result = vehicle!.CommonPool;
            
            if (!result) {
                var clientDriverDepartments =
                    await driverRepo.GetDepartmentsByDriverId(booking.UserId, booking.Driver?.DriverId ?? "");
                var vehicleDepartments =
                    await vehicleRepo.GetDepartments(booking.UserId, vehicle.VehicleId);
                result = await repo.IsVehicleCommonPoolRuleAllowed(booking.Vehicle.CommonPool,
                    clientDriverDepartments,
                    vehicleDepartments);
                return result;
            }

            return result;
        }
    }

    public async Task UpdateBookingDetails(ScdfEndBookingRequest request, ScdfBookingRepository repo, IBookingRuleRepository ruleRepo, IDriverRepository driverRepo, IVehicleRepository vehicleRepo) {
        var booking = (ScdfVehicleBooking)this.VehicleBooking!;
        Requires.IsTrue(() => booking.StartDate < request.DropOffTime, "Drop-off Time must be more than booking start time.");
        //Requires.IsTrue(() => booking?.StartDate < request.ActualDropOffTime, "Actual Drop-off Time must be more than Start Booking Time.");

        await this.IncludeVehicle(request.VehicleId);
        await this.IncludeDriver(request.ReturnedClientDriverId!);

        booking.BookingStatusId = (request.ActualDropOffTime > request.DropOffTime) ? BookingStatusCode.ReturnedLate : BookingStatusCode.Returned;
        booking.UpdatedDate = DateTime.UtcNow;
        booking.EndDate = request.DropOffTime;
        booking.ReturnedIgnitionTs = request.ActualDropOffTime;
        booking.ReturnedClientDriverId = request.ReturnedClientDriverId;
        booking.ReturnedVehicleCommanderClientUserId = request.ReturnedVehicleCommanderClientUserId;
        booking.ReturnedClientUserId = request.CompletedClientUserId;
        booking.CompletedClientUserId = request.CompletedClientUserId;
        await this.IncludeEndBookingRules(request.UserId, ruleRepo);
    }

    private async Task IncludeEndBookingRules(long userId, IBookingRuleRepository rule) {
        foreach (var r in await GetEndBookingRules(userId, rule)) {
            if (r.IsEnabled == true) {
                this.VehicleBooking!.PreConditions.Add(r);
            }
        }
        return;

        async Task<List<IBookingRule>> GetEndBookingRules(long userId, IBookingRuleRepository rule) {
            var (userBookingRule, bookingSettings) = await rule.GetBookingSettingRules(userId);
           
            List<IBookingRule> bookingRule = [];
            foreach (var r in userBookingRule) {
                switch (r.BookingRuleId) {
                    // case Constants.BookingRuleCodeCheckDriverLicenseClass:
                    //     bookingRule.Add(new MustHaveSpecialLicenseRule(bookingSettings) { IsEnabled = r.Status });
                    //     break;
                    // case Constants.BookingRuleCodeCheckDriverSpecialLicense:
                    //     bookingRule.Add(new MustHaveQdlLicenseRule(bookingSettings) { IsEnabled = r.Status });
                    //     break;
                    case Constants.BookingRuleCodeIsDriverRequired:
                        bookingRule.Add(new MustHaveDriverRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                }
            }

            bookingRule.Add(new MustHaveVehicleRule(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new IsVehicleInCommonPoolRule(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveValidBookingTime(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveValidLicenseForVehicle(bookingSettings, _mediator) { IsEnabled = true });
            return bookingRule;
        }
    }

    public async Task UpdateBookingDetails(ScdfUpdateBookingRequest request, ScdfBookingRepository repo, IBookingRuleRepository ruleRepo, IDriverRepository driverRepo,
        IVehicleRepository vehicleRepo, IUserRepository userRepo, IUserAppSettingsRepository userAppSettingsRepo) {

        var booking = (ScdfVehicleBooking)this.VehicleBooking!;
        if (request.VehicleId is > 0)
            await this.IncludeVehicle(request.VehicleId.Value);
        if (!string.IsNullOrWhiteSpace(request.RequestClientDriverId))
            await this.IncludeDriver(request.RequestClientDriverId!);

        List<Journey> journeys = new List<Journey>();
        int journeyOrder = 1;
        foreach (var r in request.Journeys) {
            journeys.Add(new Journey { LocationReference = new JourneyLocation { Type = r.Type, Value = r.Value }, Order = journeyOrder, });
            journeyOrder++;
        }

        await this.IncludeJourneyType(request.JourneyType);
        await this.IncludeJourneys(journeys);
        await this.IncludePurpose(request.BookingPurposeId);
        await this.IncludeCategory(request.BookingVehicleTypeId ?? 0);
        await this.IncludePickupLocation(request.PickupSiteLocationId);
        await this.IncludeRequestedFor(request.RequestedForClientUserId ?? "");
        await this.IncludeDriverByDriverType(request.DriverType, request.LoginClientUserId ?? "", request.RequestClientDriverId ?? "");
        await this.IncludeVehicleCommanderByType(request.VehicleCommanderType, request.LoginClientUserId ?? "", request.VehicleCommanderClientUserId ?? "");
        
        if (request.StartTs.Kind == DateTimeKind.Unspecified) {
            booking.StartDate =  DateTime.SpecifyKind(request.StartTs, DateTimeKind.Utc);
        }
        else {
            booking.StartDate = request.StartTs.ToUniversalTime().AddSeconds(1);
        }

        if (request.EndTs.Kind == DateTimeKind.Unspecified) {
            booking.EndDate = DateTime.SpecifyKind(request.EndTs, DateTimeKind.Utc);
        } else {
            booking.EndDate = request.EndTs.ToUniversalTime();
        }
        
        booking.BookingPurposeDescription = request.BookingPurposeDescription;
        booking.DriverType = request.DriverType;
        booking.ReturnSiteLocationId = (request.JourneyType == JourneyType.Return ? request.PickupSiteLocationId : 0);
        booking.Description = request.BookingPurposeDescription;
        booking.UpdatedDate = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
        booking.Type = BookingType.Standard;
        booking.RequestedDate = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
        booking.Remarks = request.Remarks ?? "";
        booking.NumberOfPassengers = request.NumberOfPassengers;
        booking.EquipmentType1 = request.EquipmentType;
        booking.AccessoryIds = request.Accessories ?? null;
    
        // Equipment Attachment 
        booking.EquipmentAttachmentIds = request.EquipmentAttachmentIds; // for existing attachment
        booking.EquipmentAttachments = request.EquipmentAttachments; // for new attachments
        
        booking.IsBookingTimeConflicting = await IsBookingTimeConflicting(booking);
        booking.IsBookingPurposeOthersRequired = await IsBookingPurposeOthersRequired(booking);

        var subUserAccountLogin = request.LoginClientUserId ?? "";
             booking.IsSubUserLoginAccountAbleToEditBooking =
                 this.BookingContext!.Permissions.IssuancePermissions.CanEditBookingRequest ||
                 await CheckIfBookingBelongsToSubUser(booking, subUserAccountLogin);
         
        await this.IncludeUpdateBookingRules(request.UserId, ruleRepo, booking);
        return;

        async Task<bool> IsBookingTimeConflicting(VehicleBookingBase booking) {
            bool result = await repo.IsBookingTimeConflicting(booking);
            return result;
        }

        async Task<bool> IsBookingPurposeOthersRequired(VehicleBookingBase booking) {
            bool result = await repo.IsBookingPurposeOthersRequired(booking);
            return result;
        }
        
        async Task<bool> CheckIfBookingBelongsToSubUser(VehicleBookingBase booking, string clientUserId) {
            await Task.Yield();
            var scdfBooking = booking;
            var result = scdfBooking.Status.Id == (int) BookingStatusCode.Requested && (scdfBooking.RequestClientUserId == clientUserId || scdfBooking.RequestedForClientUserId == clientUserId);
            return result;
        }
    }

    private async Task IncludeUpdateBookingRules(long userId, IBookingRuleRepository rule, ScdfVehicleBooking booking) {
        foreach (var r in await GetUpdateBookingRules()) {
            if (r.IsEnabled == true) {
                this.VehicleBooking!.PreConditions.Add(r);
            }
        }
        return;
        
        async Task<List<IBookingRule>> GetUpdateBookingRules() {
            var (userBookingRule, bookingSettings) = await rule.GetBookingSettingRules(userId);

            List<IBookingRule> bookingRule = [];

            foreach (var r in userBookingRule) {
                switch (r.BookingRuleId) {
                    case Constants.BookingRuleCodeIsDriverRequired:
                        bookingRule.Add(new MustHaveDriverRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                    // case Constants.BookingRuleCodeCheckDriverLicenseClass when booking.Driver is not null:
                    //     bookingRule.Add(new MustHaveSpecialLicenseRule(bookingSettings) { IsEnabled = r.Status });
                    //     break;
                    // case Constants.BookingRuleCodeCheckDriverSpecialLicense when booking.Driver is not null:
                    //     bookingRule.Add(new MustHaveQdlLicenseRule(bookingSettings) { IsEnabled = r.Status });
                    //     break;
                    case Constants.BookingRuleCodeMaximumBookingTime:
                        bookingRule.Add(new MustBeLessThanMaxDurationRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                    case Constants.BookingRuleCodeBookInAdvanceBy:
                        bookingRule.Add(new MustBeLessThanMaxDaysInAdvanceRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                }
            }

            bookingRule.Add(new IsClientUserAbleToEditBookingRequest(bookingSettings) { IsEnabled = true });
            
            //ADD THE SCDF-specific rules
            bookingRule.Add(new MustHaveDescriptionWhenPurposeIsOthers(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveRequestedForWhenBookingForOtherParty(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHavePassengers(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHavePickupLocation(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveValidJourneys(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveValidBookingTime(bookingSettings) { IsEnabled = true });
            
            if (booking.Driver is not null)
                bookingRule.Add(new MustHaveValidLicenseForVehicle(bookingSettings, _mediator) { IsEnabled = true });

            return bookingRule;
        }
    }

    public async Task UpdateBookingDetails(ScdfRejectBookingRequest request, ScdfBookingRepository repo, IBookingRuleRepository ruleRepo) {
        var booking = (ScdfVehicleBooking)this.VehicleBooking!;
        await this.IncludeRejectedBy(request.RejectClientUserId ?? "");

        booking.BookingId = request.BookingId;
        booking.UpdatedDate = DateTime.UtcNow;
        booking.BookingRejectReasonId = request.BookingRejectReasonId;
        booking.BookingRejectReason = request.BookingRejectReason;
    }

    public async Task UpdateBookingDetails(ScdfCancelBookingRequest request, ScdfBookingRepository repo) {
        var booking = (ScdfVehicleBooking)this.VehicleBooking!;
        await this.IncludeCancelledBy(request.CanceledClientUserId ?? "");
        booking.CancellationDate = DateTime.UtcNow;
        booking.CancellationNotes = request.BookingCancelNotes;
        booking.CancellationReasonId = request.BookingCancelReasonId;
        booking.UpdatedDate = DateTime.UtcNow;
    }

    public async Task UpdateBookingDetails(ScdfCreateBookingRequest request, ScdfBookingRepository repo, IBookingRuleRepository ruleRepo, BookingContext bookingContext) {
        var vehicleBooking = (ScdfVehicleBooking)this.VehicleBooking!;

        List<Journey> journeys = new List<Journey>();
        int journeyOrder = 1;
        foreach (var r in request.Journeys) {
            journeys.Add(new Journey { LocationReference = new JourneyLocation { Type = r.Type, Value = r.Value }, Order = journeyOrder, });
            journeyOrder++;
        }

        // Booking Approval by Manager if enabled.
        BookingStatusCode bsc = BookingStatusCode.Requested;
      
        //1. Create all the booking rules that applies to SCDF Booking
        vehicleBooking.UserId = request.UserId;
        vehicleBooking.CancellationTrigger = null;
        vehicleBooking.BookingStatusId = bsc;
        vehicleBooking.BookingPurposeId = request.BookingPurposeId;
        vehicleBooking.BookingPurposeDescription = request.BookingPurposeDescription;
        vehicleBooking.BookingVehicleTypeId = request.BookingVehicleTypeId;
        vehicleBooking.StartDate = request.StartTs.AddSeconds(1).ToUniversalTime();
        vehicleBooking.EndDate = request.EndTs.ToUniversalTime();
        vehicleBooking.DriverType = request.DriverType;
        vehicleBooking.RequestClientDriverId = request.RequestClientDriverId;
        
        vehicleBooking.PickupSiteLocationId = request.PickupSiteLocationId;
        vehicleBooking.ReturnSiteLocationId = 0; // in SCDF not used. All will be in 'Journeys'
        vehicleBooking.Description = null;
        vehicleBooking.Purpose = null;
        vehicleBooking.CreatedDate = DateTime.Now.ToUniversalTime();
        vehicleBooking.Type = BookingType.Standard;
        vehicleBooking.RequestedForClientUserId = request.RequestedForClientUserId;
        vehicleBooking.RequestedDate = DateTime.Now.ToUniversalTime();
        vehicleBooking.IsAutoApproveEnabled = bookingContext.BookingSettings.IsAutoApprovalEnabled ?? false;
        vehicleBooking.ActivationTrigger = null;
        vehicleBooking.Remarks = request.Remarks ?? "";
        vehicleBooking.NumberOfPassengers = request.NumberOfPassengers;
        vehicleBooking.EquipmentType1 = request.EquipmentType;
        vehicleBooking.AccessoryIds = request.Accessories ?? null;
        vehicleBooking.JourneyType = request.JourneyType;
        vehicleBooking.Journeys = journeys;
        vehicleBooking.EquipmentAttachmentIds = request.EquipmentAttachmentIds; // for existing attachment
        vehicleBooking.EquipmentAttachments = request.EquipmentAttachments;
        
        await this.IncludeRequestedBy(request.RequestClientUserId ?? "");
        await this.IncludeCreateBookingRules(request.UserId, ruleRepo);
        vehicleBooking.IsBookingTimeConflicting = await IsBookingTimeAvailable(vehicleBooking);
        vehicleBooking.IsBookingPurposeOthersRequired = await IsBookingPurposeOthersRequired(vehicleBooking);

        await this.IncludeRequestedBy(request.RequestClientUserId!);
        await this.IncludeDriverByDriverType(request.DriverType, request.RequestClientUserId ?? "", request.RequestClientDriverId ?? "");
        await this.IncludeVehicleCommanderByType(request.VehicleCommanderType, request.RequestClientUserId ?? "", request.VehicleCommanderClientUserId ?? "");
        

        async Task<bool> IsBookingTimeAvailable(VehicleBookingBase booking) {
            bool result = await repo.IsBookingTimeConflicting(booking);
            return result;
        }

        async Task<bool> IsBookingPurposeOthersRequired(VehicleBookingBase booking) {
            bool result = await repo.IsBookingPurposeOthersRequired(booking);
            return result;
        }
    }

    private async Task IncludeDriverByDriverType(DriverType driverType, string driverClientUserId, string driverClientDriverId) {
        switch (driverType)
        {
            case DriverType.SelfDrive:
                Requires.NotNullOrEmpty(driverClientUserId, nameof(driverClientUserId));
                await this.IncludeDriverByClientUserId(driverClientUserId);
                this.VehicleBooking!.DriverType = DriverType.SelfDrive;
                break;
            case DriverType.Specific:
                Requires.NotNullOrEmpty(driverClientDriverId, nameof(driverClientDriverId));
                await this.IncludeDriver(driverClientDriverId);
                this.VehicleBooking!.DriverType = DriverType.Specific;
                break;
            case DriverType.AnyDriver:
                this.VehicleBooking!.DriverType = DriverType.AnyDriver;
                this.VehicleBooking.Driver = null;
                this.VehicleBooking.RequestClientDriverId = "";
                break;
        }
    }
    
    private async Task IncludeVehicleCommanderByType(VehicleCommanderType vehicleCommanderType, string requestClientUserId, string vehicleCommanderClientUserId) {
        switch (vehicleCommanderType)
        {
            case VehicleCommanderType.Self:
                Requires.NotNullOrEmpty(requestClientUserId, nameof(requestClientUserId));
                await this.IncludeVehicleCommander(requestClientUserId!);
                this.VehicleBooking!.VehicleCommanderType = VehicleCommanderType.Self;
                break;
            case VehicleCommanderType.Specific:
                Requires.NotNullOrEmpty(vehicleCommanderClientUserId, nameof(vehicleCommanderClientUserId));
                await this.IncludeVehicleCommander(vehicleCommanderClientUserId);
                this.VehicleBooking!.VehicleCommanderType = VehicleCommanderType.Specific;
                break;
            case  VehicleCommanderType.AnyCommander:
                this.VehicleBooking!.VehicleCommanderType = VehicleCommanderType.AnyCommander;
                this.VehicleBooking.VehicleCommander = null;
                this.VehicleBooking.VehicleCommanderClientUserId = "";
                if (this._additionalInfo != null) 
                    this._additionalInfo.VehicleCommanderClientUserId = "";
                break;
        }
    }

    private async Task IncludeCreateBookingRules(long userId, IBookingRuleRepository rule) {
        foreach (var r in await GetCreateBookingRules()) {
            if (r.IsEnabled == true) {
                this.VehicleBooking!.PreConditions.Add(r);
            }
        }
        return;

        async Task<List<IBookingRule>> GetCreateBookingRules() {
            var (userBookingRule, bookingSettings) = await rule.GetBookingSettingRules(userId);
            List<IBookingRule> bookingRule = [];

            foreach (var r in userBookingRule) {
                switch (r.BookingRuleId) {
                    // case Constants.BookingRuleCodeCheckDriverLicenseClass:
                    //     bookingRule.Add(new MustHaveSpecialLicenseRule(bookingSettings) { IsEnabled = r.Status });
                    //     break;
                    // case Constants.BookingRuleCodeCheckDriverSpecialLicense:
                    //     bookingRule.Add(new MustHaveQdlLicenseRule(bookingSettings) { IsEnabled = r.Status });
                    //     break;
                    case Constants.BookingRuleCodeIsDriverRequired:
                        bookingRule.Add(new MustHaveDriverRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                    case Constants.BookingRuleCodeMaximumBookingTime:
                        bookingRule.Add(new MustBeLessThanMaxDurationRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                    case Constants.BookingRuleCodeBookInAdvanceBy:
                        bookingRule.Add(new MustBeLessThanMaxDaysInAdvanceRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                }
            }

            //ADD THE SCDF-specific rules
            bookingRule.Add(new MustHaveDescriptionWhenPurposeIsOthers(bookingSettings) { IsEnabled = true });
            // Main Booking Rules
            bookingRule.Add(new MustHaveRequestedForWhenBookingForOtherParty(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHavePassengers(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHavePickupLocation(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveValidJourneys(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveValidBookingTime(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveValidRequestClientUserIdRule(bookingSettings) { IsEnabled = true });

            return bookingRule;
        }
    }

    public async Task UpdateBookingDetails(ScdfActivateBookingRequest request, ScdfBookingRepository repo, IBookingRuleRepository ruleRepo, IDriverRepository bookingContext, IVehicleRepository vehicleRepo) {
        var vehicleBooking = (ScdfVehicleBooking)this.VehicleBooking!;
        Requires.IsTrue(() => vehicleBooking.EndDate > request.PickUpTime, "Pickup Time must be less than End Booking Time.");
        Requires.IsTrue(() => vehicleBooking.EndDate > request.ActualPickUpTime, "Actual Pickup Time must be less than End Booking Time.");

        var userId = request.UserId;
        var vehicleInfo = await vehicleRepo.GetVehicleById(userId, request.VehicleId);
        Requires.IsTrue(() => vehicleInfo != null, "Vehicle Id does not exist in the system.");

        await this.IncludeVehicle(request.VehicleId);
        await this.IncludeDriver(request.ClientDriverId);
        Requires.IsTrue(() => vehicleBooking.Driver != null, "Driver is not exist in the system.");

        await this.IncludeVehicleCommander(request.VehicleCommanderClientUserId);
        await this.IncludeRequestedBy(request.ActivatedClientUserId);
         
       
        vehicleBooking.StartDate = DateTime.SpecifyKind(request.PickUpTime.AddSeconds(1), DateTimeKind.Utc);
        vehicleBooking.PickupTime = DateTime.SpecifyKind(request.ActualPickUpTime, DateTimeKind.Utc);
        vehicleBooking.UpdatedDate = DateTime.UtcNow;
        await this.IncludeActivateBookingRules(request.UserId, ruleRepo); 

        vehicleBooking.IsBookingTimeConflicting = await IsBookingTimeAvailable(vehicleBooking);
        vehicleBooking.IsVehicleCommonPoolRuleAllowed = await IsVehicleCommonPoolRuleAllowed(vehicleBooking);
        async Task<bool> IsBookingTimeAvailable(ScdfVehicleBooking booking) {
            bool result = await repo.IsBookingTimeConflicting(booking);
            return result;
        }
    
        async Task<bool> IsVehicleCommonPoolRuleAllowed(ScdfVehicleBooking booking) {
            var clientDriverDepartments = booking.Driver!.DriverDepartments!;
            var vehicleDepartments = await vehicleRepo.GetDepartments(booking.UserId, booking.VehicleId ?? 0);
            bool result = await repo.IsVehicleCommonPoolRuleAllowed(booking.Vehicle.CommonPool, clientDriverDepartments, vehicleDepartments);
            return result;
        }
        
        
    }

    private async Task IncludeActivateBookingRules(long userId, IBookingRuleRepository rule) {
        foreach (var r in await GeActivateBookingRules()) {
            if (r.IsEnabled == true) {
                this.VehicleBooking!.PreConditions.Add(r);
            }
        }
        return;

        async Task<List<IBookingRule>> GeActivateBookingRules() {
            var (userBookingRule, bookingSettings) = await rule.GetBookingSettingRules(userId);

            List<IBookingRule> bookingRules = [];
            foreach (var r in userBookingRule) {
                switch (r.BookingRuleId) {
                    case Constants.BookingRuleCodeIsDriverRequired:
                        bookingRules.Add(new MustHaveDriverRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                    case Constants.BookingRuleCodeMaximumBookingTime:
                        bookingRules.Add(new MustBeLessThanMaxDurationRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                    case Constants.BookingRuleCodeBookInAdvanceBy:
                        bookingRules.Add(new MustBeLessThanMaxDaysInAdvanceRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                }
            }

            bookingRules.Add(new MustHaveVehicleRule(bookingSettings) { IsEnabled = true });
            bookingRules.Add(new IsVehicleInCommonPoolRule(bookingSettings) { IsEnabled = true });
            bookingRules.Add(new MustMatchBookingVehiclePickupLocationRule(bookingSettings) { IsEnabled = true });
            bookingRules.Add(new MustHaveValidBookingTime(bookingSettings) { IsEnabled = true });
            bookingRules.Add(new MustHaveValidLicenseForVehicle(bookingSettings, _mediator) { IsEnabled = true });
            return bookingRules;
        }
    }

    
}