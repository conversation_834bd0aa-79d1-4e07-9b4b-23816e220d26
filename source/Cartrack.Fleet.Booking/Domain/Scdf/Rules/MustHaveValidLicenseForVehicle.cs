﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.License.Domain.Scdf.LicenseValidation;
using Cartrack.Fleet.License.Features.IsLicenseMatching;
using MediatR;

namespace Cartrack.Fleet.Booking.Domain.Scdf.Rules;

public class MustHaveValidLicenseForVehicle(BookingSettings settings, IMediator mediator) : IBookingRule {
    public string Name => nameof(MustHaveValidLicenseForVehicle);
    public required bool? IsEnabled { get; init; } = true;
    
    public string Description { get; } =
        "Driver must have a license that matches the vehicle according to SCDF rules";

    public (bool, string) Execute(IBooking booking) {
        if (settings is { CheckDriverLicenseClass: false, CheckDriverSpecialLicense: false }) {
            //If the license checks are turned off, return true
            return (true, string.Empty);
        }

        if (booking.Status.Id == (int)BookingStatusCode.Requested) {
            return (true, string.Empty);
        }
        
        var scdfBooking = (ScdfVehicleBooking)booking;
        //Requires.NotNull(booking.Target, nameof(booking));
        Requires.NotNull(booking, nameof(booking));
        Requires.NotNull(scdfBooking.Driver, nameof(scdfBooking.Driver));

        // Ignore this rule if no target vehicle intact
        if (booking?.Target == null) {
            return (true, string.Empty);
        }
        
        var vehicle = ((VehicleTarget)scdfBooking.Target!).Vehicle;
        
        var licReq = new IsLicenseMatchingRequest(booking.Driver!.DriverId, vehicle!.VehicleId);
        var licResp = mediator.Send(licReq).GetAwaiter().GetResult();
        if (licResp.IsOk)
            return (licResp.Value!.IsMatch, this.Description + "." + licResp.Value.ValidationError);

        return (false, licResp.Error?.Message ?? this.Description);
    }

    
}