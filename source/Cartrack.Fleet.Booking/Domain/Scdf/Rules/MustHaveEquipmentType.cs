﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Scdf.Rules;

public class MustHaveEquipmentType(BookingSettings settings) : IBookingRule {
    public string Name => nameof(MustHaveEquipmentType);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = "Invalid 'equipment type' specified. `EquipmentType` should be filled.";

    public (bool, string) Execute(IBooking booking) {
        var scdfBooking = (ScdfVehicleBooking)booking;
        var pass = scdfBooking.EquipmentType1 is not null;
        return (pass, pass ? string.Empty : $"Validation failed. {this.Description}");
    }
}