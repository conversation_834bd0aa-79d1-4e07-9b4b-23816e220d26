﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Scdf.Rules;

public class IsClientUserAbleToEditBookingRequest(BookingSettings settings) : IBookingRule {
    const string DescriptionRequired = "Client User is not able to edit this booking.";

    public string Name => nameof(IsClientUserAbleToEditBookingRequest);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = DescriptionRequired;

    public (bool, string) Execute(IBooking booking) {
        var scdfBooking = (ScdfVehicleBooking)booking;

        bool validationOk = scdfBooking.IsSubUserLoginAccountAbleToEditBooking;
        
        var pass = validationOk;
        return (pass, pass ? string.Empty : $"Validation failed. {this.Description}");
    }
}