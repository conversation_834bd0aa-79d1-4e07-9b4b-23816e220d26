﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Scdf.Rules;

public class MustHaveValidJourneys(BookingSettings settings) : IBookingRule {
    const string JourneyMustBeValid = "Journeys must be valid";

    const string DropoffIsRequired = "Drop-off Point(s) is/are required.";

    //const string VehicleCommanderTypeIsRequiredForSingleTrip = "Vehicle Commander Type is required for Single Trip.";
    //const string VehicleCommanderTypeIsRequiredForReturnTrip = "Vehicle Commander Type is required for Return Trip.";
    const string JourneyLocationTypeIsNotValid =
        "Journey Location Type is not valid. e.g : 'location','additionallocation','freetext'. ";

    const string JourneyTypeMustBeValid = "Journey Type must be valid";

    public string Name => nameof(MustHaveValidJourneys);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = JourneyMustBeValid;

    public (bool, string) Execute(IBooking booking) {
        var scdfBooking = (ScdfVehicleBooking)booking;

        switch (scdfBooking.JourneyType) {
            case JourneyType.Single:
                if (scdfBooking.Journeys.Count == 0) {
                    return (false, DropoffIsRequired);
                }

                break;
            case JourneyType.Return:
                break;
            case JourneyType.MultiStop:
                //No validations for multi-stop?
                if (scdfBooking.Journeys.Count == 0) {
                    return (false, DropoffIsRequired);
                }

                break;
            default:
                return (false, JourneyTypeMustBeValid);
                break;
        }

        foreach (var j in scdfBooking.Journeys) {
            switch (j.LocationReference.Type.ToLower()) {
                case "location":
                case "additionallocation":
                case "freetext":
                    break;
                default:
                    return (false, JourneyLocationTypeIsNotValid);
            }
        }

        return (true, string.Empty);
    }
}