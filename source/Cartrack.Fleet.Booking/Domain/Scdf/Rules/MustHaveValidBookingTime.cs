﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Scdf.Rules;

public class MustHaveValidBookingTime(BookingSettings settings) : IBookingRule {
    const string ConflictingBookingTime =
        "The booking time conflicts with another booking for the specific driver / vehicle commander / vehicle.";

    public string Name => nameof(MustHaveValidBookingTime);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = ConflictingBookingTime;

    public (bool, string) Execute(IBooking booking) {
        var scdfBooking = (ScdfVehicleBooking)booking;
        var pass = !scdfBooking.IsBookingTimeConflicting;
        return (pass, pass ? string.Empty : $"Validation failed. {this.Description}");
    }
}