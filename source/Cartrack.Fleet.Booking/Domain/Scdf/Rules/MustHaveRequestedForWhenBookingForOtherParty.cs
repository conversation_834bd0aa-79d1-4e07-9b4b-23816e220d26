﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Scdf.Rules;

public class MustHaveRequestedForWhenBookingForOtherParty(BookingSettings settings) : IBookingRule {
    const string RequestedForIsRequired = "Requested For is required when booking for another party";

    public string Name => nameof(MustHaveRequestedForWhenBookingForOtherParty);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = RequestedForIsRequired;

    public (bool, string) Execute(IBooking booking) {
        var scdfBooking = (ScdfVehicleBooking)booking;
        //var validationFailed = scdfBooking.IsBookingForOtherParty && string.IsNullOrEmpty(scdfBooking.RequestedFor);
        var validationFailed = scdfBooking.IsBookingForOtherParty &&
                               string.IsNullOrEmpty(scdfBooking.RequestedFor.ClientUserId);
        var pass = !validationFailed;
        return (pass, pass ? string.Empty : $"Validation failed. {this.Description}");
    }
}