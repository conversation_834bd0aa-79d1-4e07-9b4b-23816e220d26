﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Scdf.Rules;

public class MustHavePassengers(BookingSettings settings) : IBookingRule {
    public string Name => nameof(MustHavePassengers);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = "Number of passengers is required";

    public (bool, string) Execute(IBooking booking) {
        var scdfBooking = (ScdfVehicleBooking)booking;
        var pass = scdfBooking.NumberOfPassengers is > 0;
        return (pass, pass ? string.Empty : $"Validation failed. {this.Description}");
    }
}