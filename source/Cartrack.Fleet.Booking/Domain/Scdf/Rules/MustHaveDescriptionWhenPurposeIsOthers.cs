﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Scdf.Rules;

public class MustHaveDescriptionWhenPurposeIsOthers(BookingSettings settings) : IBookingRule {
    const string DescriptionRequired = "Description is required when Request Purpose is 'Others'";

    public string Name => nameof(MustHaveDescriptionWhenPurposeIsOthers);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = DescriptionRequired;

    public (bool, string) Execute(IBooking booking) {
        var scdfBooking = (ScdfVehicleBooking)booking;
        var validationFailed = scdfBooking.IsBookingPurposeOthersRequired &&
                               string.IsNullOrEmpty(scdfBooking.BookingPurposeDescription);
        var pass = !validationFailed;
        return (pass, pass ? string.Empty : $"Validation failed. {this.Description}");
    }
}