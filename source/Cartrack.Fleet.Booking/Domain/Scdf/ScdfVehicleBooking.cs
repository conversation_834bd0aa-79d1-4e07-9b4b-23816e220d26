﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Scdf;

public class ScdfVehicleBooking : VehicleBookingBase {
    public override IList<Journey> Journeys { get; set; } = [];
    public bool IsBookingForOtherParty { get; set; }
    public bool IsBookingPurposeOthersRequired { get; set; }
   
    public override Task Validate() {
        if (this.PreConditions is { Count: > 0 }) {
            foreach (var p in this.PreConditions) {
                var (isValid, err) = p.Execute(this);
                Requires.IsTrue(() => isValid, err);
            }
        }

        ;

        return Task.CompletedTask;
    }

    
}