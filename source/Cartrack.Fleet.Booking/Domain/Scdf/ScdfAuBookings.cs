﻿using PoolDb = Cartrack.EFCore.Models.Pool;
using Cartrack.EFCore.Models.TfmsCustom;

namespace Cartrack.Fleet.Booking.Domain.Common;

public class ScdfAuBookings {
    public required PoolDb.AuBooking AuBooking;
    public AuScdfBookingAdditionalInfo? AuScdfBookingAdditionalInfo;
    public string? AuBookingJourneys;
    public string? AuBookingAccessories;
    public string? AuBookingAttachments;
}