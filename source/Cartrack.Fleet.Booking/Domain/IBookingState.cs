﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain;

public interface IBookingState {
    int Id { get; set; }
    string Name { get; }
    string TranslationKey { get; }
    Task Request();
    Task Approve();
    Task Reject();
    Task Cancel();
    Task Activate();
    Task ActivateLate();
    Task Return();
    Task ReturnLate();
    Task Terminate();
    Task UpdateDetails();
    IBookingState GetState();
}