﻿namespace Cartrack.Fleet.Booking.Domain.Common;

public abstract class VehicleBookingBase : BookingBase {
    public override long? VehicleId { get; set; }
    public JourneyType JourneyType { get; set; }
    public abstract IList<Journey> Journeys { get; set; }
    public ClientUser? VehicleCommander { get; set; }
    public string? ReturnedClientDriverId { get; set; } = "";
    public string? ReturnedClientUserId{get;set;} = "";
    public string? ActivatedClientUserId { get; set; } = "";
    public string? CompletedClientUserId { get; set; } = "";
    public int? LocationType { get; set; }
    //public int? DriverType { get; set; }
    //public int? VehicleCommanderType { get; set; }
    public int? EquipmentType { get; set; }
    public List<int>? AccessoryIds { get; set; }
    public List<BookingAccessoryDetail> DetailedAccessories { get; set; } = [];
    public int? NumberOfPassengers { get; set; }
    public VehicleCommanderType? VehicleCommanderType { get; set; }
    public string? VehicleCommanderClientUserId { get; set; } = "";
    public string? ReturnedVehicleCommanderClientUserId { get; set; } = "";
    public List<int>? EquipmentAttachmentIds { get; set; } // for existing attachment in a booking (for edit purpose)
    public List<InputEquipmentAttachment>? EquipmentAttachments { get; set; } // for (non-existent) new attachments (for create / edit purpose)
    public bool IsVehicleCommonPoolRuleAllowed {get;set;}
    public bool IsBookingPurposeOthersRequired { get; set; }
    public bool IsDriverAndVehiclePdpLicesnsesMatched { get; set; }
    public bool IsDriverAndVehicleQdlLicesnsesMatched { get; set; }
    public DriverType? DriverType { get; set; }
    public EquipmentType? EquipmentType1 { get; set; }
    public Vehicle Vehicle => this.Target is not null ? (((VehicleTarget)this.Target!)).Vehicle! : Common.Vehicle.Empty();
    
    public DateTime? PickedupIgnitionTs { get; set; }
    public DateTime? ReturnedIgnitionTs { get; set; }
}