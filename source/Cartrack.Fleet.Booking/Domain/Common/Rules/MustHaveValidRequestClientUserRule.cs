﻿namespace Cartrack.Fleet.Booking.Domain.Common.Rules;

public class MustHaveValidRequestClientUserIdRule(BookingSettings settings) : IBookingRule {
    public string Name => nameof(MustHaveValidRequestClientUserIdRule);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = "Request Client User must be valid";

    public (bool, string) Execute(IBooking booking) {
        bool pass = !string.IsNullOrWhiteSpace(booking.RequestedBy.ClientUserId);
        return (pass, pass ? string.Empty : $"Validation failed. {Description}");
    }
}