﻿namespace Cartrack.Fleet.Booking.Domain.Common.Rules;

public class MustBeLessThanMaxDaysInAdvanceRule(BookingSettings settings) : IBookingRule {
    public string Name => nameof(MustBeLessThanMaxDaysInAdvanceRule);
    public required bool? IsEnabled { get; init; } = true;

    public string Description { get; } =
        $"Advanced bookings should be less than {settings.BookInAdvanceBy.TotalDays} days";

    public (bool, string) Execute(IBooking booking) {
        DateTime futureDate = booking.StartDate;
        DateTime nowDate = DateTime.Now;

        TimeSpan difference = futureDate - nowDate;
        int days = (int)Math.Ceiling(difference.TotalDays);

        var pass = (days <= settings.BookInAdvanceBy.TotalDays);
        return (pass, pass ? string.Empty : $"Validation failed. {Description}");
    }
}