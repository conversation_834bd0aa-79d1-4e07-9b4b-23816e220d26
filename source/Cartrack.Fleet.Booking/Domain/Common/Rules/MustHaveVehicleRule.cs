﻿using Cartrack.Fleet.Booking.Domain.Scdf;

namespace Cartrack.Fleet.Booking.Domain.Common.Rules;

public class MustHaveVehicleRule(BookingSettings settings) : IBookingRule {
    public string Name => nameof(MustHaveVehicleRule);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = "Booking must specify a vehicle";

    public (bool, string) Execute(IBooking booking) {
        var scdfBooking = (ScdfVehicleBooking)booking;
        bool pass = (scdfBooking.Target as VehicleTarget)?.Vehicle?.VehicleId != null;
        return (pass, pass ? string.Empty : $"Validation failed. {Description}");
    }
}