﻿namespace Cartrack.Fleet.Booking.Domain.Common.Rules;

public class MustMatchBookingVehiclePickupLocationRule(BookingSettings settings) : IBookingRule {
    public string Name => nameof(MustMatchBookingVehiclePickupLocationRule);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = "Booking and Vehicle Pickup Location must match";

    public (bool, string) Execute(IBooking booking) {
        var vehicleBooking = (VehicleBookingBase)booking;
        var vehicleDefaultSiteLocation = (vehicleBooking.Target as VehicleTarget)?.Vehicle?.DefaultSiteLocationId;
        var vehicleBookingLocation = vehicleBooking.PickupLocation.Id;
        bool pass = (vehicleBookingLocation == vehicleDefaultSiteLocation);
        return (pass, pass ? string.Empty : $"Validation failed. {Description}");
    }
}