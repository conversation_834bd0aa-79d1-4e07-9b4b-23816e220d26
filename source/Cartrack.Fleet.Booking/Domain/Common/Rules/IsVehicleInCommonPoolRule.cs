﻿using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Spf;

namespace Cartrack.Fleet.Booking.Domain.Common.Rules;

public class IsVehicleInCommonPoolRule(BookingSettings settings) : IBookingRule {
    public string Name => nameof(IsVehicleInCommonPoolRule);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = "You are only able to book vehicle within its department with driver's.";

    public (bool, string) Execute(IBooking booking) {
        var vehicleBookingBase = (VehicleBookingBase)booking;
        bool pass = vehicleBookingBase.IsVehicleCommonPoolRuleAllowed;
        return (pass, pass ? string.Empty : $"Validation failed. {Description}");
    }
}