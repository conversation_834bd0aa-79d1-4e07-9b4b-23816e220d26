﻿namespace Cartrack.Fleet.Booking.Domain.Common.Rules;

public class MustHaveDriverRule(BookingSettings settings) : IBookingRule {
    public string Name => nameof(MustHaveDriverRule);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = "Booking must specify a driver";

    public (bool, string) Execute(IBooking booking) {
        var pass = settings.IsDriverRequired != true || !string.IsNullOrEmpty(booking.Driver?.DriverId);
        return (pass, pass ? string.Empty : $"Validation failed. {Description}");
    }
}