﻿namespace Cartrack.Fleet.Booking.Domain.Common.Rules;

public class MustHaveQdlLicenseRule(BookingSettings settings) : IBookingRule {
    public string Name => nameof(MustHaveQdlLicenseRule);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = "Driver must have a valid QDL License";

    public (bool, string) Execute(IBooking booking) {
        if (booking.Driver is null) {
            return (true, String.Empty);
        }

        bool pass = booking.Driver?.DriverQdlLicense is not null && booking.Driver.DriverQdlLicense.Count > 0;
        return (pass, pass ? string.Empty : $"Validation failed. {this.Description}");
    }

}