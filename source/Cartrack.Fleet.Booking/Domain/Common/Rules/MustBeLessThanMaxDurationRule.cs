﻿namespace Cartrack.Fleet.Booking.Domain.Common.Rules;

public class MustBeLessThanMaxDurationRule(BookingSettings settings) : IBookingRule {
    public string Name => nameof(MustBeLessThanMaxDurationRule);
    public required bool? IsEnabled { get; init; } = true;

    public string Description { get; } =
        $"Booking start/end dates must be less than the maximum booking duration of {settings.MaximumBookingTime.TotalHours} hours";

    public (bool, string) Execute(IBooking booking) {
        DateTime startDate = booking.StartDate;
        DateTime endDate = booking.EndDate;

        TimeSpan duration = endDate - startDate;
        double hours = duration.TotalHours;

        var pass = (hours <= settings.MaximumBookingTime.TotalHours);
        return (pass, pass ? string.Empty : $"Validation failed. {Description}");
    }
}