﻿using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Common;

public class Driver {
    public string DriverId { get; set; } = string.Empty;
    public string DriverName { get; set; } = string.Empty;
    public string DriverSurname { get; set; } = string.Empty;
    public List<DriverDepartment>? DriverDepartments { get; set; }
    public List<DriverQdlLicense>? DriverQdlLicense { get; set; }
    public List<DriverPdpLicense>? DriverPdpLicense { get; set; }
    public string FullName => $"{this.DriverSurname}{this.DriverName}";
    public string Email { get; set; } = string.Empty;
}