﻿namespace Cartrack.Fleet.Booking.Domain.Common.States;

public class RejectedState(IBooking booking) : BaseBookingState(booking) {
    public override int Id {
        get => (int)BookingStatusCode.Rejected;
        set => throw new NotImplementedException();
    }

    public override string Name => Constants.Rejected;
    public override string TranslationKey => Constants.BookingStatusTranslationKeyRejected;
}