﻿namespace Cartrack.Fleet.Booking.Domain.Common.States;

public class ReturnedState(IBooking booking) : BaseBookingState(booking) {
    public override int Id {
        get => (int)BookingStatusCode.Returned;
        set => throw new NotImplementedException();
    }

    public override string Name => Constants.Returned;
    public override string TranslationKey => Constants.BookingStatusTranslationKeyReturned;
}