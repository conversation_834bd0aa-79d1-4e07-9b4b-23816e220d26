﻿namespace Cartrack.Fleet.Booking.Domain.Common.States;

public class ApprovedState(IBooking booking) : BaseBookingState(booking) {
    public override int Id {
        get => (int)BookingStatusCode.Approved;
        set => throw new NotImplementedException();
    }

    public override string Name => Constants.Approved;
    public override string TranslationKey => Constants.BookingStatusTranslationKeyApproved;

    public override Task Activate() {
        var prevState = this.Booking.Status;
        try {
            this.Booking.Validate();
            this.Booking.Status = new ActiveState(this.Booking);
            this.Booking!.BookingStatusId = (BookingStatusCode)this.Booking.Status.Id;
        }
        catch {
            this.Booking.Status = prevState;
            this.Booking!.BookingStatusId = (BookingStatusCode)prevState.Id;
            throw;
        }

        return Task.CompletedTask;
    }

    public override Task Cancel() {
        this.Booking.Status = new RejectedState(this.Booking);
        return Task.CompletedTask;
    }
 
}