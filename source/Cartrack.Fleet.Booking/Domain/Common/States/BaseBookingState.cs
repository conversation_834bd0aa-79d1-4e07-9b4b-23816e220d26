﻿namespace Cartrack.Fleet.Booking.Domain.Common.States;

public abstract class BaseBookingState(IBooking booking) : IBookingState {
    public IBooking Booking => booking;
    public abstract int Id { get; set; }
    public abstract string Name { get; }
    public abstract string TranslationKey { get; }

    public virtual Task Request() {
        return Task.CompletedTask;
    }

    public virtual Task Approve() {
        throw new InvalidOperationException("Unable to approve a booking that is not in the requested state");
    }

    public virtual Task Reject() {
        throw new InvalidOperationException("Unable to reject a booking that is not in the requested state");
    }

    public virtual Task Cancel() {
        throw new InvalidOperationException("Unable to cancel a booking that is not in the requested state");
    }

    public virtual Task Activate() {
        throw new InvalidOperationException("Unable to activate a booking that has not been approved");
    }

    public virtual Task ActivateLate() {
        throw new InvalidOperationException("Unable to activate(late) a booking that has not been approved");
    }

    public virtual Task Return() {
        throw new InvalidOperationException("Unable to return a booking that has not been activated");
    }

    public virtual Task ReturnLate() {
        throw new InvalidOperationException("Unable to return(late) a booking that has not been activated");
    }

    public virtual Task Terminate() {
        throw new InvalidOperationException("Unable to force terminate a booking that has not been activated");
    }

    public virtual Task UpdateDetails() {
        throw new NotImplementedException("TODO");
    }

    public IBookingState GetState() {
        throw new NotImplementedException();
    }

    public static IBookingState GetState(long statusCode, IBooking booking) {
        return statusCode switch {
            2 => new RequestedState(booking),
            3 => new ApprovedState(booking),
            4 => new RejectedState(booking),
            5 => new CancelledState(booking),
            6 => new ActiveState(booking),
            8 => new ActiveLateState(booking),
            9 => new ReturnedState(booking),
            10 => new ReturnedLateState(booking),
            11 => new ExpiringApprovalState(booking),
            12 => new ForceTerminatedState(booking),
            _ => new UnknownState(booking)
        };
    }
}