﻿namespace Cartrack.Fleet.Booking.Domain.Common.States;

public class ActiveLateState(IBooking booking) : BaseBookingState(booking) {
    public override int Id {
        get => (int)BookingStatusCode.ActiveLate;
        set => throw new NotImplementedException();
    }

    public override string Name => Constants.ActivateLate;
    public override string TranslationKey => Constants.BookingStatusTranslationKeyActivateLate;

    public override Task ReturnLate() {
        var prevState = this.Booking.Status;
        try {
            this.Booking.Validate();
            this.Booking.Status = new ReturnedLateState(this.Booking);
            this.Booking!.BookingStatusId = BookingStatusCode.ReturnedLate;
        }
        catch {
            this.Booking.Status = prevState;
            this.Booking!.BookingStatusId = (BookingStatusCode)prevState.Id;
            throw;
        }

        return Task.CompletedTask;
    }
    
    public override Task Return() {
        var prevState = this.Booking.Status;
        try {
            this.Booking.Validate();
            this.Booking.Status = new ReturnedState(this.Booking);
            this.Booking!.BookingStatusId = BookingStatusCode.Returned;
        }
        catch {
            this.Booking.Status = prevState;
            this.Booking!.BookingStatusId = (BookingStatusCode)prevState.Id;
            throw;
        }

        return Task.CompletedTask;
    }

    public override Task Terminate() {
        var prevState = this.Booking.Status;
        try {
            this.Booking.Validate();
            this.Booking.Status = new ForceTerminatedState(this.Booking);
            this.Booking!.BookingStatusId = BookingStatusCode.ForceTerminated;
        }
        catch {
            this.Booking.Status = prevState;
            this.Booking!.BookingStatusId = (BookingStatusCode)prevState.Id;
            throw;
        }

        return Task.CompletedTask;
    }
}