﻿namespace Cartrack.Fleet.Booking.Domain.Common.States;

public class CancelledState(IBooking booking) : BaseBookingState(booking) {
    public override int Id {
        get => (int)BookingStatusCode.Cancelled;
        set => throw new NotImplementedException();
    }

    public override string Name => Constants.Cancelled;
    public override string TranslationKey => Constants.BookingStatusTranslationKeyCancelled;
}