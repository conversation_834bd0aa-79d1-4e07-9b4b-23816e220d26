﻿namespace Cartrack.Fleet.Booking.Domain.Common.States;

public class ForceTerminatedState(IBooking booking) : BaseBookingState(booking) {
    public override int Id {
        get => (int)BookingStatusCode.ForceTerminated;
        set => throw new NotImplementedException();
    }

    public override string Name => Constants.ForceTerminated;
    public override string TranslationKey => Constants.BookingStatusTranslationKeyForceTerminated;
}