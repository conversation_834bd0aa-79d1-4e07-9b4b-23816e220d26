﻿namespace Cartrack.Fleet.Booking.Domain.Common.States;

public class ExpiringApprovalState(IBooking booking) : BaseBookingState(booking) {
    public override int Id {
        get => (int)BookingStatusCode.ExpiringApproval;
        set => throw new NotImplementedException();
    }

    public override string Name => Constants.ExpiringApproval;
    public override string TranslationKey => Constants.BookingStatusTranslationKeyExpiringApproval;
 
    public override Task Cancel() {
        this.Booking.Status = new CancelledState(this.Booking);
        return Task.CompletedTask;
    }
}