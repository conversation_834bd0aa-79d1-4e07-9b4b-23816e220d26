﻿namespace Cartrack.Fleet.Booking.Domain.Common.States;

public class ReturnedLateState(IBooking booking) : BaseBookingState(booking) {
    public override int Id {
        get => (int)BookingStatusCode.ReturnedLate;
        set => throw new NotImplementedException();
    }

    public override string Name => Constants.ReturnedLate;
    public override string TranslationKey => Constants.BookingStatusTranslationKeyReturnedLate;
}