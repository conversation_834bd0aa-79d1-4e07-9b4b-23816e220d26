﻿using Pool = Cartrack.EFCore.Models.Pool;
using Cartrack.Fleet.Common.IO.Sql;

namespace Cartrack.Fleet.Booking.Domain.Common;

public class BookingSettings {
    public required bool? IsAutoApprovalEnabled { get; set; } = false;
    public required TimeSpan BookInAdvanceBy { get; set; } = TimeSpan.FromDays(14);
    public required TimeSpan MaximumBookingTime { get; set; } = TimeSpan.FromHours(12);
    public required bool? CheckDriverSpecialLicense { get; set; }
    public required bool? CheckDriverLicenseClass { get; set; }
    public required bool? IsKeyCollectionEnabled { get; set; }
    public required string ActivationType { get; set; }
    public required bool? IsMultiLevelApprovalEnabled { get; set; }
    public required bool? IsDriverRequired { get; set; }
    public required TimeSpan PreDriveChecklistAvailableBy { get; set; } = TimeSpan.FromMinutes(15);
}