﻿using Cartrack.Fleet.Booking.Domain.Common.CancellationTriggers;
using Cartrack.Fleet.Booking.Domain.Common.States;

namespace Cartrack.Fleet.Booking.Domain.Common;

public abstract class BookingBase : IBooking {
    public long UserId { get; set; }
    public IBookingCancellationTrigger CancellationTrigger { get; set; } = new UnclaimedVehicleTrigger();
    public long BookingId { get; set; }
    public BookingStatusCode? BookingStatusId { get; set; }
    public long? BookingVehicleTypeId { get; set; }
    public IBookingState Status { get; set; } = new UnknownState(null!);
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public DateTime? PickupTime { get; set; }
    public DateTime? DropoffTime { get; set; }
    public string? Description { get; set; } = "";
    public string? RequestClientDriverId { get; set; } = "";
    public string? RequestClientUserId { get; set; } = "";
    public string? RequestedForClientUserId { get; set; } = "";
    public RequestPurpose Purpose { get; set; } = RequestPurpose.Unknown();
    public long BookingPurposeId { get; set; }
    public string? BookingPurposeDescription { get; set; } = "";
    public long? PickupSiteLocationId { get; set; }
    public long? ReturnSiteLocationId { get; set; }
    public Driver? Driver { get; set; }
    public RequestLocation PickupLocation { get; set; } = RequestLocation.Empty();
    public RequestVehicleType VehicleCategory { get; set; } = RequestVehicleType.Empty();
    public IBookingTarget? Target { get; set; }
    public IList<IBookingAccessory> Accessories { get; } = [];
    public DateTime? KeyCollectionTs { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? UpdatedDate { get; set; }
    public BookingType Type { get; set; } = BookingType.Standard;
    public ClientUser RequestedBy { get; set; } = ClientUser.Empty();
    public ClientUser RequestedFor { get; set; } = ClientUser.Empty();
    //public Fleet.Driver.Domain.Common.Driver? RequestedClientDriver { get; set; }
    //public ClientUser? RequestedClientUser { get; set; }
    public DateTime RequestedDate { get; set; }
    public bool IsApproved { get; set; }
    public IList<ClientUser> ApprovedBy { get; set; } = [];
    public IList<ClientUser> RejectedBy { get; set; } = [];
    public IList<IBookingRule> PreConditions { get; } = [];
    public IBookingActivationTrigger ActivationTrigger { get; set; } = new ManualActivationTrigger();
    public string? BookingReference { get; set; }
    public bool IsAutoApproveEnabled { get; set; }
    public DateTime? KeyReturnTs { get; set; }
    
    public BookingType BookingType { get; set; } = BookingType.Standard;
    public string? Remarks { get; set; }
    public DateTime? CancellationDate { get; set; }
    public ClientUser CancelledBy { get; set; } = ClientUser.Empty();
    public string? CancellationNotes { get; set; } = "";
    public long? CancellationReasonId { get; set; } = 0;

    public abstract long? VehicleId { get; set; }
    public long BookingRejectReasonId { get; set; }
    public string BookingRejectReason { get; set; } = string.Empty;
    public bool IsBookingTimeConflicting { get; set; }
    public bool IsBookingTimeConflictingForDriver { get; set; }
    public bool IsSubUserLoginAccountAbleToEditBooking { get; set; }
    public string? BookingAuditReference { get; set; }
    public abstract Task Validate();
}

public class BookingsResult {
    public VehicleBookingBase[] Bookings { get; set; } = [];
    public int Total { get; set; }
    public Dictionary<long, int> StatusCounts { get; set; } = new();
}

public record InputEquipmentAttachment(string Guid, string Extension, string RealFilename);