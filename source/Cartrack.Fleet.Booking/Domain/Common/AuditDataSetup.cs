﻿using PoolDb = Cartrack.EFCore.Models.Pool;
using CtDb = Cartrack.EFCore.Models.CT;
using FleetDb = Cartrack.EFCore.Models.Fleet;
using Cartrack.EFCore.Models.TfmsCustom;

namespace Cartrack.Fleet.Booking.Domain.Common;

public class AuditDataSetup {
    public List<CtDb.Vehicle>? Vehicles;
    public List<FleetDb.ClientUser>? Users;
    public List<FleetDb.ClientDriver>? Drivers;
    public List<FleetDb.SiteLocation>? SiteLocations;
    public List<PoolDb.BookingVehicleType>? BookingVehicleTypes;
    public List<PoolDb.BookingPurpose>? BookingPurpose;
    public List<PoolDb.BookingCancelReason>? CancelReason;
    public List<PoolDb.BookingRejectReason>? RejectReason;
    public List<PoolDb.BookingForceTerminateReason>? ForceTerminateReason;
}