﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.Domain.Common;
using Db = Cartrack.EFCore.Models.Fleet;

namespace Cartrack.Fleet.Booking.Domain.Common;

public record ClientUser {
    public DateTime? UpdatedDate { get; init; }

    public DateTime? CreatedDate { get; init; }

    public required string? ClientUserId { get; init; } = "";

    public string? PasswordHash { get; init; } = "";

    public bool? IsDeleted { get; init; }

    public string? Email { get; init; } = "";
    public string? Username { get; init; } = "";
    public List<UserDepartment> Departments { get; set; } = [];

    /// <summary>
    ///     Convert the ClientUser data access entity to the ClientUser BL entity
    /// </summary>
    public static ClientUser From(Db.ClientUser dbClientUser) {
        Requires.IsTrue(() => !dbClientUser.IsRole, () => "ClientUser should not be a role");

        var clientUser = new ClientUser {
            Email = dbClientUser.EMail,
            IsDeleted = dbClientUser.IsDeleted,
            PasswordHash = dbClientUser.PasswordHash,
            ClientUserId = dbClientUser.ClientUserId,
            CreatedDate = dbClientUser.Cts,
            UpdatedDate = dbClientUser.Uts,
            Username = dbClientUser.UserName
        };

        return clientUser;
    }

    public static ClientUser Empty() {
        return new ClientUser {
            ClientUserId = string.Empty,
            Email = string.Empty,
            CreatedDate = DateTime.MinValue,
            UpdatedDate = DateTime.MinValue,
            IsDeleted = false,
            PasswordHash = string.Empty,
            Username = string.Empty
        };
    }
}