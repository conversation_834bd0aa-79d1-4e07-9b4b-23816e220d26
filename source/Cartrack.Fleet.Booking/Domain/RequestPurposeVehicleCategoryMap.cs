﻿namespace Cartrack.Fleet.Booking.Domain;

public record RequestPurposeVehicleCategoryMap {
    public required long Id { get; init; }
    public required string Title { get; init; }
    public required string? Description { get; init; }
    public List<RequestVehicleType>? VehicleTypes { get; init; }

    /*public static RequestPurpose Unknown() {
        return new RequestPurpose { Id = 0, Title = "", Description = "" };
    }*/
}