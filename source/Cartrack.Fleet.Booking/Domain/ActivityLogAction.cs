﻿namespace Cartrack.Fleet.Booking.Domain;

public record ActivityLogAction {
    public required string StatusId { get; set; }
    public string? StatusName { get; set; }
    public string? Reason { get; set; } = "N/A";
    public string? Remarks { get; set; }
    public string? VehicleRegistration {get; set;}
    public string? AssignedDriver { get; set; } = "N/A";
    public string? AssignedVehicleCommander { get; set; } = "N/A";
    public DateTime? PickupDateTime { get; set; } = null;
    public DateTime? ActualPickupDateTime { get; set; } = null;
    public DateTime? DropOffDateTime { get; set; } = null;
    public DateTime? ActualDropoffDateTime { get; set; } = null;
    public List<ActivityLogDataField> Changes{get;set;}
}