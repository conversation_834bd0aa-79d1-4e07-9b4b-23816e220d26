﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Spf.Rules;

public class MustMatchDriverAndVehicleQdlLicense(BookingSettings settings) : IBookingRule {
    const string DescriptionRequired = "Description is required when Request Purpose is 'Others'";

    public string Name => nameof(MustMatchDriverAndVehicleQdlLicense);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = DescriptionRequired;

    public (bool, string) Execute(IBooking booking) {
        var spfVehicleBooking = (SpfVehicleBooking)booking;
        bool pass = spfVehicleBooking.IsDriverAndVehicleQdlLicesnsesMatched;
        return (pass, pass ? string.Empty : $"Validation failed. {this.Description}");
    }
}