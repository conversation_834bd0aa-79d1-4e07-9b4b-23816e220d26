﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Spf.Rules;

public class MustHaveValidBookingTime(BookingSettings settings) : IBookingRule {
    const string ConflictingBookingTime =
        "The booking time conflicts with another booking for the specific driver / vehicle.";

    public string Name => nameof(MustHaveValidBookingTime);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = ConflictingBookingTime;

    public (bool, string) Execute(IBooking booking) {
        var spfVehicleBooking = (SpfVehicleBooking)booking;
        var pass = !spfVehicleBooking.IsBookingTimeConflicting;
        return (pass, pass ? string.Empty : $"Validation failed. {this.Description}");
    }
}