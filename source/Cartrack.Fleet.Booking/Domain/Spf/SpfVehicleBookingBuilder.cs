﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Driver.IO.Sql;

using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Booking.Domain.Spf;

//public interface IVehicleBookingBuilder {
//    Task<VehicleBookingBase?> Build(long bookingId);
//}
public class SpfVehicleBookingBuilder : VehicleBookingBuilder {

    public SpfVehicleBookingBuilder(
        AppSettings appSettings,
        IMediator mediator,
        IDriverRepository driverRepo,
        AppCtDbContext ctDbContext,
        AppFleetDbContext fleetDbContext,
        AppPoolDbContext poolDbContext,
        AppTfmsCustomDbContext tfmsCustomDbContext,
        IHttpContextAccessor contextAccessor) : base(appSettings, mediator, driverRepo, ctDbContext, fleetDbContext, poolDbContext, tfmsCustomDbContext, contextAccessor) {
    }

    public override async Task<VehicleBookingBase?> Build(long id) {
        await this.Start(id);
        await this.IncludeVehicle();
        await this.IncludePickupLocation();
        await this.IncludePurpose();
        await this.IncludeCategory();
        await this.IncludeDriver();
        await this.IncludeRequestedBy();
        await this.IncludeApprovedBy();
        await this.IncludeRejectedBy();
        return this.VehicleBooking;
    }

    public override VehicleBookingBase Create() {
        return new SpfVehicleBooking();
    }

    public override async Task Start(long bookingId) {
        await base.Start(bookingId);
    }

    //public async Task IncludeAdditionalInfo() {
    //    Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

    //    // Get vehicle commander

    //    if (this._additionalInfo != null && !string.IsNullOrEmpty(this._additionalInfo.VehicleCommanderClientUserId)) {
    //        var clientUser = await this._fleetDbContext.ClientUsers
    //            .Where(cu => cu.ClientUserId == this._additionalInfo.VehicleCommanderClientUserId)
    //            .Select(cu => new ClientUserRecord(cu.ClientUserId, cu.UserName))
    //            .FirstOrDefaultAsync();
    //        if (clientUser is not null)
    //            this._clientUsers.Add(clientUser);
    //    }

    //    // Adding other additional info for scdf booking
    //    if (this._additionalInfo != null) {
    //        this.VehicleBooking!.RequestedForClientUserId = this._additionalInfo.RequestedForClientUserId;
    //        this.VehicleBooking.LocationType = this._additionalInfo.LocationType;
    //        this.VehicleBooking.DriverType = this._additionalInfo.DriverType;
    //        this.VehicleBooking.VehicleCommanderType = this._additionalInfo.VehicleCommanderType;
    //        this.VehicleBooking.EquipmentType = this._additionalInfo.EquipmentType;
    //        this.VehicleBooking.NumberOfPassengers = this._additionalInfo.PassengerCount;
    //    }
    //}
}