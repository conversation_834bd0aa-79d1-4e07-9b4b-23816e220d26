﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain;

public interface IBooking {
    long BookingId { get; }
    IBookingState Status { get; set; }
    BookingStatusCode? BookingStatusId { get; set; }
    DateTime StartDate { get; }
    DateTime EndDate { get; }
    string? Description { get; }
    public RequestPurpose Purpose { get; set; }
    IBookingTarget Target { get; }
    DateTime CreatedDate { get; }
    DateTime? UpdatedDate { get; }
    BookingType Type { get; }
   // Driver.Domain.Common.Driver? RequestedClientDriver { get; }
    //ClientUser? RequestedClientUser { get; set; }
    ClientUser RequestedBy { get; }
    ClientUser RequestedFor { get; }
    DateTime RequestedDate { get; }
    IList<ClientUser> ApprovedBy { get; }
    IList<ClientUser> RejectedBy { get; }
    IList<IBookingRule> PreConditions { get; }
    IBookingActivationTrigger ActivationTrigger { get; }
    //BookingType BookingType { get; set; }
    string? Remarks { get; }
    DateTime? CancellationDate { get; set; } 
    ClientUser CancelledBy { get; set; }
    string? CancellationNotes { get; set; }
    long? CancellationReasonId { get; set; }
    
    // Vehicle Booking
    long? VehicleId { get; set; }
    Common.Driver? Driver { get; set; }
    long? BookingVehicleTypeId { get; set; }
    //bool IsVehicleCommonPoolRuleAllowed{get;set;}
    //bool IsDriverAndVehiclePdpLicesnsesMatched { get; set; }
    //bool IsDriverAndVehicleQdlLicesnsesMatched { get; set; }
    //bool IsBookingTimeConflicting { get; set; }
    //bool IsBookingPurposeOthersRequired {get;set;}
    //bool IsSubUserLoginAccountAbleToEditBooking { get; set; }
    Task Validate();
}