﻿using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Http.CustomAuthorization;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;

namespace Cartrack.Fleet.Booking;

public static class BookingStartup {
    public static void Register(IServiceCollection builderServices, AppSettings appSetting) {
        //builderServices.AddScoped<IAuthRepository, AuthRepository>();

        builderServices.AddMediatR(cfg => {
            cfg.RegisterServicesFromAssemblyContaining<BookingController>();
            cfg.RegisterServicesFromAssemblyContaining<SpfBookingController>();
            cfg.RegisterServicesFromAssemblyContaining<ScdfBookingController>();
        });

        builderServices.AddControllers()
            .AddApplicationPart(typeof(BookingController).Assembly)
            .AddApplicationPart(typeof(SpfBookingController).Assembly)
            .AddApplicationPart(typeof(ScdfBookingController).Assembly);
        
        builderServices.AddScoped<ScdfBookingRepository>();
        builderServices.AddScoped<SpfBookingRepository>();
        builderServices.AddScoped<IBookingRepository, BookingRepository>();
        builderServices.AddScoped<IBookingRuleRepository, BookingRuleRepository>();
        builderServices.AddScoped<IBookingActivityLogsRepository, BookingActivityLogsRepository>();
        builderServices.AddScoped<IBookingFilterService, BookingFilterService>();
        
        builderServices.AddScoped<ScdfVehicleBookingBuilder>();
        builderServices.AddScoped<SpfVehicleBookingBuilder>();
        builderServices.AddScoped<Func<string, IVehicleBookingBuilder>>(serviceProvider => key => {
            
            if (key == appSetting.ScdfAccount ) {
                return serviceProvider.GetService<ScdfVehicleBookingBuilder>()!;
            }
            else if (key == appSetting.SpfAccount) {
                return serviceProvider.GetService<SpfVehicleBookingBuilder>()!;
            }

            throw new NotImplementedException($"Agency {key} not supported");
        });
        
        builderServices.AddScoped<IAuthorizationHandler, AuthorizeIssuanceHandler>();

    }
}