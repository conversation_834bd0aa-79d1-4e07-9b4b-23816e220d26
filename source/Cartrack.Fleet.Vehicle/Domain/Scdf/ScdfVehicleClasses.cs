﻿namespace Cartrack.Fleet.Vehicle.Domain.Scdf;

public enum ScfVehicleLicenseClass {
    Class3,
    Class4,
    Class5,
    Class2,
    Class2A,
    ClassEV2,
    ClassEV2A,
    ClassEV4F,
    ClassEVA
}
public static class ScdfVehicleClasses{

    public static readonly string[] Class3 =  ["3", "3A", "3C", "3CA"];
    public static readonly string[] Class4 = ["4", "4A", ScdfSpecialLicenses.RDL4];
    public static readonly string[] Class2 = ["2"];
    public static readonly string[] Class2A = ["2", "2A"];
    public static string[] Class5 = ["5", ScdfSpecialLicenses.RDL5, ScdfSpecialLicenses.EV5];
    
    public static readonly string[] ClassEV2 = ["2", ScdfSpecialLicenses.EV2];
    public static readonly string[] ClassEV2A = ["2A", ScdfSpecialLicenses.EV2A];
    public static readonly string[] ClassEV4F = ["4", "4A", ScdfSpecialLicenses.RDL4, ScdfSpecialLicenses.EV4F, ScdfSpecialLicenses.EV4FM];
    public static readonly string[] ClassEVA = ["3", "3A", "3C", "3CA", ScdfSpecialLicenses.RDL4, ScdfSpecialLicenses.EV4, ScdfSpecialLicenses.EV4A, ScdfSpecialLicenses.EV3];

    public static bool IsClass3(string[] normalLicenses) {
        return normalLicenses.Intersect(Class3).Any();
    }
    public static bool IsClass4(string[] normalLicenses, string[] specialLicenses) {
        return normalLicenses.Intersect(Class4).Any() || specialLicenses.Intersect(Class4).Any();
    }
    public static bool IsClass2(string[] normalLicenses) {
        return normalLicenses.Intersect(Class2).Any();
    }
    public static bool IsClass2A(string[] normalLicenses) {
        return normalLicenses.Intersect(Class2A).Any();
    }
    public static bool IsClassEV2(string[] normalLicenses, string[] specialLicenses) {
        return normalLicenses.Intersect(ClassEV2).Any() && specialLicenses.Intersect(ClassEV2).Any();
    }
    
    public static bool IsClassEV2A(string[] normalLicenses, string[] specialLicenses) {
        return normalLicenses.Intersect(ClassEV2A).Any() && specialLicenses.Intersect(ClassEV2A).Any();
    }
    public static bool IsClassEV4F(string[] normalLicenses, string[] specialLicenses) {
        return normalLicenses.Intersect(ClassEV4F).Any() || specialLicenses.Intersect(ClassEV4F).Any();
    }
    public static bool IsClassEVA(string[] normalLicenses, string[] specialLicenses) {
        return normalLicenses.Intersect(ClassEVA).Any() || specialLicenses.Intersect(ClassEVA).Any();
    }
    public static bool IsClass5(string[] normalLicenses, string[] specialLicenses) {
        return normalLicenses.Intersect(Class5).Any() || specialLicenses.Intersect(Class5).Any();
    }

    public static ScfVehicleLicenseClass GetLicenseClass(string[] normalLicenses, string[] specialLicenses) {
        if (IsClass5(normalLicenses, specialLicenses))
            return ScfVehicleLicenseClass.Class5;
        if (IsClassEVA(normalLicenses, specialLicenses))
            return ScfVehicleLicenseClass.ClassEVA;
        if (IsClassEV4F(normalLicenses, specialLicenses))
            return ScfVehicleLicenseClass.ClassEV4F;
        if (IsClassEV2A(normalLicenses, specialLicenses))
            return ScfVehicleLicenseClass.ClassEV2A;
        if (IsClassEV2(normalLicenses, specialLicenses))
            return ScfVehicleLicenseClass.ClassEV2;
        if (IsClass2A(normalLicenses)) 
            return ScfVehicleLicenseClass.Class2A;
        if (IsClass2(normalLicenses))
            return ScfVehicleLicenseClass.Class2;
        if (IsClass3(normalLicenses))
            return ScfVehicleLicenseClass.Class3;
        if (IsClass4(normalLicenses, specialLicenses))
            return ScfVehicleLicenseClass.Class4;

        throw new ArgumentException("Invalid license class");
    }
}