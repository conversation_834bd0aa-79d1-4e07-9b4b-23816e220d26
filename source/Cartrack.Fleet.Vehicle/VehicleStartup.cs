﻿using Cartrack.Fleet.Vehicle.IO.Http;
using Cartrack.Fleet.Vehicle.IO.Sql;
using Cartrack.Fleet.Common;
using Microsoft.Extensions.DependencyInjection;

namespace Cartrack.Fleet.Vehicle;

public static class VehicleStartup {
    public static void Register(IServiceCollection builderServices, AppSettings appSetting) {
        //builderServices.AddScoped<IAuthRepository, AuthRepository>();
        builderServices.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<VehicleController>());
        builderServices.AddControllers().AddApplicationPart(typeof(VehicleController).Assembly);
        builderServices.AddScoped<IVehicleRepository, VehicleRepository>();
    }
}