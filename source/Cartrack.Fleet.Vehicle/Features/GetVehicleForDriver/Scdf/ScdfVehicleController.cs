﻿using Cartrack.Fleet.Vehicle.Features.GetVehicleForDriver;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Vehicle.Features.GetVehicleForDriver.Scdf;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class ScdfVehicleController {
    [HttpGet]
    [Route("vehicle-for-driver")]
    
    public async Task<ActionResult<GetVehicleForDriverResponse>> GetVehicleForDriver([FromQuery] string clientDriverId) {
        var request = new GetVehicleForDriverRequest(clientDriverId);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.ClientUserId = authClaims.ClientUserId;
       
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetVehicleForDriverResponse, IO.Http.Vehicle[]>(this.HttpContext);
    }
}