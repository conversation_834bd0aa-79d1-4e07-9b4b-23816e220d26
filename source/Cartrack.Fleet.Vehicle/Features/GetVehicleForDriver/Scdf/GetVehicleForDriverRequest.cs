﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Vehicle.Features.GetVehicleForDriver.Scdf;

public record GetVehicleForDriverRequest(string ClientDriverId) : IRequest<GetVehicleForDriverResponse> {
    [JsonIgnore] public string Account { get; set; } = "";
    [JsonIgnore] public long UserId { get; set; }
    [JsonIgnore] public string ClientUserId { get; set; } = "";
}