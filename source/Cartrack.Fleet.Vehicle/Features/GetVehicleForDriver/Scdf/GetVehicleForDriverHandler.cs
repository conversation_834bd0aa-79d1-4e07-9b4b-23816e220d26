﻿using Cartrack.AppHost;
using Cartrack.Fleet.Driver.Features.GetDriver;
using Cartrack.Fleet.License.Features.IsLicenseMatching;
using Cartrack.Fleet.License.Features.ValidateLicenses;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Vehicle.Domain.Scdf;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Http.Filters;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehicleForDriver.Scdf;

public class GetVehicleForDriverHandler(IMediator mediator, IVehicleRepository repo, IHttpContextAccessor context, ILogger<GetVehicleForDriverHandler> logger)
    : IRequestHandler<GetVehicleForDriverRequest, GetVehicleForDriverResponse> {
   
    public async Task<GetVehicleForDriverResponse> Handle(GetVehicleForDriverRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving all vehicle groups", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var permissionsContext = context.GetPermissionsContext();
            var getDriverRequest = new GetDriverRequest(request.ClientDriverId) { UserId = request.UserId, Account = request.Account };
            var getDriverResponse = await mediator.Send(getDriverRequest);
            var driver = getDriverResponse.Value; 
            Requires.NotNull(driver, nameof(driver));
            
            var driverSpecialLicenses = driver!.SpecialDriverLicenses
                .Where(t => !string.IsNullOrWhiteSpace(t.LicenseName))
                .Select(t => t.LicenseName!).ToArray() ;
            var driverNormalLicenses = driver.DriverLicenses
                .Where(t => !string.IsNullOrWhiteSpace(t.LicenseName))
                .Select(t => t.LicenseName!).ToArray();
            //get all vehicles
            var driverId = driver.ClientDriverId;
            var vehicles = await repo.GetAllVehicles(request.UserId);
            var vehiclesWithLicenses = vehicles.Where(v => v.DriverLicenses.Count > 0 || v.SpecialDriverLicenses.Count > 0);
            var licenseCheckTasks = vehiclesWithLicenses.Select(async v => {
                var req = new ValidateLicensesRequest(
                    v.Registration,
                    driverId,
                    driverNormalLicenses,
                    driverSpecialLicenses,
                    v.DriverLicenses.Where(t => !string.IsNullOrWhiteSpace(t.LicenseName)).Select(t => t.LicenseName!).ToArray(),
                    v.SpecialDriverLicenses.Where(t => !string.IsNullOrWhiteSpace(t.LicenseName)).Select(t => t.LicenseName!).ToArray());
                var resp = await mediator.Send(req, cancellationToken);
                return new { Vehicle = v, IsMatch = resp.IsOk && resp.Value!.IsMatch };
            }).ToArray();
            
            await Task.WhenAll(licenseCheckTasks);
            var allowedVehicles = licenseCheckTasks.Select(t => t.Result);
            var apiVehicles = allowedVehicles.Where(t => t.IsMatch).Select(v => v.Vehicle.ToHttpVehicle()).ToArray();  
            return new GetVehicleForDriverResponse(apiVehicles);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Vehicle Group List", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehicleForDriverResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Vehicle Group List", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehicleForDriverResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}