﻿using Cartrack.Fleet.Vehicle.Features.GetVehiclePdpLicenses;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Vehicle.Domain;
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Driver.Features.GetDriver;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("pdplicenses/{vehicleId:long}")]
    public async Task<ActionResult<GetVehiclePdpLicensesResponse>> GetPdpLicenses(long vehicleId) {
        var request = new GetVehiclePdpLicensesRequest(vehicleId);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetVehiclePdpLicensesResponse, VehiclePdpLicenses>(this.HttpContext);
    }
}