﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.Features.GetVehiclePdpLicenses;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehiclePdpLicenses;

public class GetVehiclePdpLicensesHandler(IVehicleRepository repo, IHttpContextAccessor context, ILogger<GetVehiclePdpLicensesHandler> logger)
    : IRequestHandler<GetVehiclePdpLicensesRequest, GetVehiclePdpLicensesResponse> {
    public async Task<GetVehiclePdpLicensesResponse> Handle(GetVehiclePdpLicensesRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.Id > 0, "Accessory ID must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving the PDP Licenses with VehicleId={VehicleId}", context.HttpContext?.TraceIdentifier ?? "", request.Account,
                request.VehicleId);

            var pdpLicenses = await repo.GetPdpLicensesById(request.UserId, request.VehicleId);
            var apiVehicle = pdpLicenses?.ToHttpPdpLicenses();
            return new GetVehiclePdpLicensesResponse(apiVehicle);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Vehicle PDP Licenses", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehiclePdpLicensesResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Vehicle PDP Licenses", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehiclePdpLicensesResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}