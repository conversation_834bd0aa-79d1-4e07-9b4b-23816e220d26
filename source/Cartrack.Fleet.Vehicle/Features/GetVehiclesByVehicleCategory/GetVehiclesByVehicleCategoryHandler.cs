﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleCategory;

public class GetVehiclesByVehicleCategoryHandler(
    IVehicleRepository repo, IHttpContextAccessor context,
    ILogger<GetVehiclesByVehicleCategoryHandler> logger)
    : IRequestHandler<GetVehiclesByVehicleCategoryRequest, GetVehiclesByVehicleCategoryResponse> {
    public async Task<GetVehiclesByVehicleCategoryResponse> Handle(GetVehiclesByVehicleCategoryRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.VehicleCategoryId > 0, "Vehicle Category ID must be greater than zero");
            //Requires.NotNullOrEmpty(request.VehicleId, nameof(request.Id));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving the vehicle with Id={VehicleId}", context.HttpContext?.TraceIdentifier ?? "", request.Account,
                request.VehicleCategoryId);

            var vehicleCategory = await repo.GetVehiclesByVehicleCategory(request.UserId, request.VehicleCategoryId,
                request.Page, request.PageSize);
            var total = await repo.GetTotalVehiclesByVehicleCategory(request.UserId, request.VehicleCategoryId);
            var apiVehicle = vehicleCategory?.ToHttpVehicles(total);
            return new GetVehiclesByVehicleCategoryResponse(apiVehicle);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Vehicle by Vehicle Category", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehiclesByVehicleCategoryResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Vehicle by Vehicle Category", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehiclesByVehicleCategoryResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}