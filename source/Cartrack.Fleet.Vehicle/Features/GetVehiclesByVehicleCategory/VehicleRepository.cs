﻿using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using VehicleAdditionalInfo = Cartrack.Fleet.Vehicle.Domain.Common.VehicleAdditionalInfo;
using VehicleDepartment = Cartrack.Fleet.Vehicle.Domain.Common.VehicleDepartment;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class VehicleRepository {
    public async Task<List<VehicleBase>> GetVehiclesByVehicleCategory(long userId, long bookingVehicleTypeId, int page,
        int pageSize) {
        var fleetHardwareTypeLink = await fleetDbContext.FleetHardwareTypeLinks
            .Select(x => x.HardwareTypeId)
            .ToListAsync();

        var vehicles = await (from v in ctDbContext.Vehicles
                              join terminal in ctDbContext.Terminals on v.TerminalId equals terminal.TerminalId into vehicleTerminals
                              from terminal in vehicleTerminals.DefaultIfEmpty()
                              join contract in ctDbContext.Contracts on v.CurrentContractId equals contract.ContractId into
                                  vehicleContracts
                              from contract in vehicleContracts.DefaultIfEmpty()
                              join productPackage in ctDbContext.ProductPackages on contract.ProductPackageId equals productPackage
                                  .ProductPackageId into contractPackages
                              from productPackage in contractPackages.DefaultIfEmpty()
                              where (productPackage == null
                                     || productPackage.HideFleetData == false
                                     || productPackage.AllowSvrAccess == true)
                                    && v.UserId == userId
                                    && contract.ContractStateId == 2
                                    && fleetHardwareTypeLink.Contains(terminal.HardwareTypeId)
                              select new {
                                  v.VehicleId,
                                  v.Registration
                              }).ToListAsync();

        var clientVehicles = await (from vai in fleetDbContext.VehicleAdditionalInfos
                                    where vehicles.Select(v => v.VehicleId).Contains(vai.VehicleId)
                                          && vai.BookingVehicleTypeId == bookingVehicleTypeId
                                    join vd in fleetDbContext.VehicleDepartments on vai.VehicleId equals vd.VehicleId into vdJoin
                                    from vd in vdJoin.DefaultIfEmpty()
                                    join dept in fleetDbContext.Departments on vd.DepartmentId equals dept.DepartmentId into deptJoin
                                    from dept in deptJoin.DefaultIfEmpty()
                                    join qdl in fleetDbContext.VehicleDriverLicenses on vai.VehicleId equals qdl.VehicleId into qdlJoin
                                    from qdl in qdlJoin.DefaultIfEmpty()
                                    join qdlname in fleetDbContext.DriverLicenseTypes on qdl.DriverLicenseTypeId equals qdlname
                                        .DriverLicenseTypeId into qdlnameJoin
                                    from qdlname in qdlnameJoin.Where(x => x.UserId == userId || x.UserId == null).DefaultIfEmpty()
                                    join pdp in fleetDbContext.VehicleSpecialLicenses on vai.VehicleId equals pdp.VehicleId into pdpJoin
                                    from pdp in pdpJoin.DefaultIfEmpty()
                                    join pdpname in fleetDbContext.DriverSpecialLicenseTypes on pdp.SpecialLicenseTypeId equals pdpname
                                        .DriverSpecialLicenseTypeId into pdpnameJoin
                                    from pdpname in pdpnameJoin.Where(x => x.UserId == userId || x.UserId == null).DefaultIfEmpty()
                                    group new {
                                        vai,
                                        vd,
                                        dept,
                                        pdp,
                                        pdpname,
                                        qdl,
                                        qdlname
                                    } by vai
                into g
                                    select new {
                                        g.Key.VehicleId,
                                        //g.Key.UserId,
                                        //g.Key.Registration,
                                        AdditionalInfo = g.Select(x => new VehicleAdditionalInfo {
                                            VehicleName = x.vai.VehicleName,
                                            ClientVehicleDescription = x.vai.ClientVehicleDescription,
                                            FuelTargetConsumption = x.vai.FuelTargetConsumption,
                                            VehicleEngineTypeId = x.vai.VehicleEngineTypeId,
                                            MaintenanceId = x.vai.MaintenanceId,
                                            DefaultSiteLocationId = x.vai.DefaultSiteLocationId,
                                            IsPoolActive = x.vai.IsPoolActive,
                                            MaintenanceStatusId = x.vai.MaintenanceStatusId,
                                            BookingVehicleTypeId = x.vai.BookingVehicleTypeId,
                                            VehicleStatusOptionId = x.vai.VehicleStatusOptionId,
                                            BookingAllocationPriority = x.vai.BookingAllocationPriority,
                                            SpeedSourceId = x.vai.SpeedSourceId,
                                            DefaultDriver = x.vai.DefaultDriver,
                                            CreatedTs = x.vai.Cts, // cts
                                            UpdatedTs = x.vai.Uts,
                                        }).FirstOrDefault(),
                                        Departments =
                                            g.Select(x => new VehicleDepartment {
                                                Id = x.vd.DepartmentId,
                                                Name = x.dept.DepartmentName
                                            })
                                                .Distinct().ToList(),
                                        DriverLicenses =
                                            g.GroupBy(a => a.qdl.DriverLicenseTypeId).Select(x =>
                                                    new VehicleQdlLicense {
                                                        LicenseTypeId = x.Key,
                                                        LicenseName = x.First().qdlname.Name
                                                    })
                                                .ToList(),
                                        SpecialDriverLicenses = g.GroupBy(x => x.pdp.SpecialLicenseTypeId).Select(x =>
                                                new VehiclePdpLicense {
                                                    LicenseTypeId = x.Key,
                                                    LicenseName = x.First().pdpname.LicenseName
                                                })
                                            .ToList()
                                    }
            )
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        var vehicle = await ctDbContext.Vehicles.Where(x => x.UserId == userId)
            .FirstOrDefaultAsync();

        List<VehicleBase> vehiclesList = [];
        vehiclesList.AddRange(clientVehicles
            .Select(v => new VehicleBase {
                VehicleId = v.VehicleId,
                Registration =
                    vehicles.Where(b => b.VehicleId == v.VehicleId).Select(x => x.Registration).FirstOrDefault(),
                VehicleName = v.AdditionalInfo.VehicleName ?? "",
                ClientVehicleDescription =
                    v.AdditionalInfo.ClientVehicleDescription ?? "", // client_vehicle_description
                FuelTargetConsumption = v.AdditionalInfo.FuelTargetConsumption, // fuel_target_consumption
                VehicleEngineTypeId = v.AdditionalInfo.VehicleEngineTypeId, // vehicle_engine_type_id
                MaintenanceId = v.AdditionalInfo.MaintenanceId,
                DefaultSiteLocationId = v.AdditionalInfo.DefaultSiteLocationId,
                IsPoolActive = v.AdditionalInfo.IsPoolActive,
                MaintenanceStatusId = v.AdditionalInfo.MaintenanceStatusId,
                BookingVehicleTypeId = v.AdditionalInfo.BookingVehicleTypeId,
                VehicleStatusOptionId = v.AdditionalInfo.VehicleStatusOptionId,
                BookingAllocationPriority = v.AdditionalInfo.BookingAllocationPriority,
                SpeedSourceId = v.AdditionalInfo.SpeedSourceId,
                DefaultDriver = v.AdditionalInfo.DefaultDriver,
                /*Gender = clientDriver.Gender switch {
                    1 => Gender.Male,
                    2 => Gender.Female,
                    _ => Gender.Unknown
                },*/
                CreatedTs = v.AdditionalInfo.CreatedTs,
                UpdatedTs = v.AdditionalInfo.UpdatedTs,
                Departments = v.Departments,
                DriverLicenses = v.DriverLicenses,
                SpecialDriverLicenses = v.SpecialDriverLicenses
            })
        );

        return vehiclesList;
    }

    public async Task<int> GetTotalVehiclesByVehicleCategory(long userId, long bookingVehicleTypeId) {
        var fleetHardwareTypeLink = await fleetDbContext.FleetHardwareTypeLinks
            .Select(x => x.HardwareTypeId)
            .ToListAsync();

        var vehicles = await (from v in ctDbContext.Vehicles
                              join terminal in ctDbContext.Terminals on v.TerminalId equals terminal.TerminalId into vehicleTerminals
                              from terminal in vehicleTerminals.DefaultIfEmpty()
                              join contract in ctDbContext.Contracts on v.CurrentContractId equals contract.ContractId into
                                  vehicleContracts
                              from contract in vehicleContracts.DefaultIfEmpty()
                              join productPackage in ctDbContext.ProductPackages on contract.ProductPackageId equals productPackage
                                  .ProductPackageId into contractPackages
                              from productPackage in contractPackages.DefaultIfEmpty()
                              where (productPackage == null
                                     || productPackage.HideFleetData == false
                                     || productPackage.AllowSvrAccess == true)
                                    && v.UserId == userId
                                    && contract.ContractStateId == 2
                                    && fleetHardwareTypeLink.Contains(terminal.HardwareTypeId)
                              select new {
                                  v.VehicleId
                              }).ToListAsync();

        return await fleetDbContext.VehicleAdditionalInfos
            .Where(c => c.BookingVehicleTypeId == bookingVehicleTypeId &&
                        vehicles.Select(v => v.VehicleId).Contains(c.VehicleId))
            .CountAsync();
    }

    public async Task<List<VehicleBase>> GetAllVehiclesByVehicleCategory(long userId, long bookingVehicleTypeId) {
        var fleetHardwareTypeLink = await fleetDbContext.FleetHardwareTypeLinks
            .Select(x => x.HardwareTypeId)
            .ToListAsync();

        var vehicles = await (from v in ctDbContext.Vehicles
                              join terminal in ctDbContext.Terminals on v.TerminalId equals terminal.TerminalId into vehicleTerminals
                              from terminal in vehicleTerminals.DefaultIfEmpty()
                              join contract in ctDbContext.Contracts on v.CurrentContractId equals contract.ContractId into
                                  vehicleContracts
                              from contract in vehicleContracts.DefaultIfEmpty()
                              join productPackage in ctDbContext.ProductPackages on contract.ProductPackageId equals productPackage
                                  .ProductPackageId into contractPackages
                              from productPackage in contractPackages.DefaultIfEmpty()
                              where (productPackage == null
                                     || productPackage.HideFleetData == false
                                     || productPackage.AllowSvrAccess == true)
                                    && v.UserId == userId
                                    && contract.ContractStateId == 2
                                    && fleetHardwareTypeLink.Contains(terminal.HardwareTypeId)
                              select new {
                                  v.VehicleId,
                                  v.Registration
                              }).ToListAsync();

        var clientVehicles = await (
                                    from vai in fleetDbContext.VehicleAdditionalInfos
                                    where vehicles.Select(v => v.VehicleId).Contains(vai.VehicleId)
                                          && vai.BookingVehicleTypeId == bookingVehicleTypeId
                                    group vai by vai.VehicleId into g
                                    select new {
                                        VehicleId = g.Key,
                                        AdditionalInfo = g.Select(x => new VehicleAdditionalInfo {
                                            VehicleName = x.VehicleName,
                                            ClientVehicleDescription = x.ClientVehicleDescription,
                                            FuelTargetConsumption = x.FuelTargetConsumption,
                                            VehicleEngineTypeId = x.VehicleEngineTypeId,
                                            MaintenanceId = x.MaintenanceId,
                                            DefaultSiteLocationId = x.DefaultSiteLocationId,
                                            IsPoolActive = x.IsPoolActive,
                                            CommonPool = x.CommonPool,
                                            MaintenanceStatusId = x.MaintenanceStatusId,
                                            BookingVehicleTypeId = x.BookingVehicleTypeId,
                                            VehicleStatusOptionId = x.VehicleStatusOptionId,
                                            BookingAllocationPriority = x.BookingAllocationPriority,
                                            SpeedSourceId = x.SpeedSourceId,
                                            DefaultDriver = x.DefaultDriver,
                                            CreatedTs = x.Cts,
                                            UpdatedTs = x.Uts
                                        }).FirstOrDefault()
                                    }
                                ).ToListAsync();

        List<VehicleBase> vehiclesList = [];
        vehiclesList.AddRange(clientVehicles
            .Select(v => new VehicleBase {
                VehicleId = v.VehicleId,
                Registration =
                    vehicles.Where(b => b.VehicleId == v.VehicleId).Select(x => x.Registration).FirstOrDefault(),
                VehicleName = v.AdditionalInfo.VehicleName ?? "",
                ClientVehicleDescription =
                    v.AdditionalInfo.ClientVehicleDescription ?? "", // client_vehicle_description
                FuelTargetConsumption = v.AdditionalInfo.FuelTargetConsumption, // fuel_target_consumption
                VehicleEngineTypeId = v.AdditionalInfo.VehicleEngineTypeId, // vehicle_engine_type_id
                MaintenanceId = v.AdditionalInfo.MaintenanceId,
                DefaultSiteLocationId = v.AdditionalInfo.DefaultSiteLocationId,
                IsPoolActive = v.AdditionalInfo.IsPoolActive,
                MaintenanceStatusId = v.AdditionalInfo.MaintenanceStatusId,
                BookingVehicleTypeId = v.AdditionalInfo.BookingVehicleTypeId,
                VehicleStatusOptionId = v.AdditionalInfo.VehicleStatusOptionId,
                BookingAllocationPriority = v.AdditionalInfo.BookingAllocationPriority,
                SpeedSourceId = v.AdditionalInfo.SpeedSourceId,
                DefaultDriver = v.AdditionalInfo.DefaultDriver,
                CreatedTs = v.AdditionalInfo.CreatedTs,
                UpdatedTs = v.AdditionalInfo.UpdatedTs,
                CommonPool = v.AdditionalInfo.CommonPool
            })
        );

        return vehiclesList;
    }
}