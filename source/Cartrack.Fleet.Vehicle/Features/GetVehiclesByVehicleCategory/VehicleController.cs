﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.Features.GetDriver;
using Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleCategory;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("category/{vehicleCategoryId:long}")]
    public async Task<ActionResult<GetVehiclesByVehicleCategoryResponse>> GetVehiclesByVehicleCategory(
        long vehicleCategoryId, [FromQuery] int page = 1, [FromQuery] int pageSize = 10) {
        var request = new GetVehiclesByVehicleCategoryRequest(vehicleCategoryId, page, pageSize);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetVehiclesByVehicleCategoryResponse, Vehicles>(this.HttpContext);
    }
}