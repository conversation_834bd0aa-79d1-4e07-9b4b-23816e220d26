﻿using Cartrack.Fleet.Common.Domain;
using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class VehicleRepository {

    private static ConcurrentCache<long, VehicleRecord[]> CachedVehicles = new();
    
    public record VehicleRecord(long VehicleId, long UserId, string Registration, VehicleAdditionalInfo AdditionalInfo, List<VehicleDepartment> Departments, List<VehicleQdlLicense> DriverLicenses, List<VehiclePdpLicense> SpecialDriverLicenses) {
        public override string ToString() {
            return $"{{ VehicleId = {VehicleId}, UserId = {UserId}, Registration = {Registration}, AdditionalInfo = {AdditionalInfo}, Departments = {Departments}, DriverLicenses = {DriverLicenses}, SpecialDriverLicenses = {SpecialDriverLicenses} }}";
        }
    }

    public async Task<List<VehicleBase>> GetAllVehicles(long userId) {
        var records = await CachedVehicles.GetOrAddAsync(userId, async i => (await this.FetchVehicleData(i)).ToArray());
        return records.Select(ToVehicle).ToList();
    }

    public async Task<List<VehicleBase>> GetVehicles(long userId, int page, int pageSize) {
        var clientVehicles = await this.FetchVehicleData(userId);
        var clientVehicles2 = clientVehicles
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToList();
         
        List<VehicleBase> vehiclesList = [];
        vehiclesList.AddRange(clientVehicles2.Select(ToVehicle));
        return vehiclesList;
    }

    private static VehicleBase ToVehicle(VehicleRecord v) {
        return new VehicleBase() {
            VehicleId = v.VehicleId,
            Registration = v.Registration,
            VehicleName = v.AdditionalInfo?.VehicleName,
            ClientVehicleDescription = v.AdditionalInfo?.ClientVehicleDescription, // client_vehicle_description
            FuelTargetConsumption = v.AdditionalInfo?.FuelTargetConsumption, // fuel_target_consumption
            VehicleEngineTypeId = v.AdditionalInfo?.VehicleEngineTypeId, // vehicle_engine_type_id
            MaintenanceId = v.AdditionalInfo?.MaintenanceId,
            DefaultSiteLocationId = v.AdditionalInfo?.DefaultSiteLocationId,
            IsPoolActive = v.AdditionalInfo?.IsPoolActive,
            MaintenanceStatusId = v.AdditionalInfo?.MaintenanceStatusId,
            BookingVehicleTypeId = v.AdditionalInfo?.BookingVehicleTypeId,
            VehicleStatusOptionId = v.AdditionalInfo?.VehicleStatusOptionId,
            BookingAllocationPriority = v.AdditionalInfo?.BookingAllocationPriority,
            SpeedSourceId = v.AdditionalInfo?.SpeedSourceId,
            DefaultDriver = v.AdditionalInfo?.DefaultDriver,
            CreatedTs = v.AdditionalInfo?.CreatedTs,
            UpdatedTs = v.AdditionalInfo?.UpdatedTs,
            Departments = v.Departments,
            DriverLicenses = v.DriverLicenses,
            SpecialDriverLicenses = v.SpecialDriverLicenses
        };
    }
    private async Task<IEnumerable<VehicleRecord>> FetchVehicleData(long userId) {
        var departments =
            await fleetDbContext.Departments
                .Where(y => y.UserId == userId)
                .Select(x => new {
                    x.DepartmentId,
                    x.DepartmentName,
                    x.Description
                })
                .ToListAsync();

        var qdlLicenses = await fleetDbContext.DriverLicenseTypes
            .Where(x => x.UserId == userId || x.UserId == null)
            .Select(x => new {
                x.DriverLicenseTypeId, x.Name
            })
            .ToListAsync();

        var pdpLicenses = await fleetDbContext.DriverSpecialLicenseTypes
            .Where(x => x.UserId == userId || x.UserId == null)
            .Select(x => new {
                x.DriverSpecialLicenseTypeId, x.LicenseName
            })
            .ToListAsync();

        var fleetHardwareTypeLink = await fleetDbContext.FleetHardwareTypeLinks
            .Select(x => x.HardwareTypeId)
            .ToListAsync();

        var vehicles = await (from v in ctDbContext.Vehicles
            join terminal in ctDbContext.Terminals on v.TerminalId equals terminal.TerminalId into vehicleTerminals
            from terminal in vehicleTerminals.DefaultIfEmpty()
            join contract in ctDbContext.Contracts on v.CurrentContractId equals contract.ContractId into
                vehicleContracts
            from contract in vehicleContracts.DefaultIfEmpty()
            join productPackage in ctDbContext.ProductPackages on contract.ProductPackageId equals productPackage
                .ProductPackageId into contractPackages
            from productPackage in contractPackages.DefaultIfEmpty()
            where (productPackage == null
                   || productPackage.HideFleetData == false
                   || productPackage.AllowSvrAccess == true)
                  && v.UserId == userId
                  && contract.ContractStateId == 2
                  && fleetHardwareTypeLink.Contains(terminal.HardwareTypeId)
            select new {
                v.VehicleId,
                v.UserId,
                v.Registration
            }).ToListAsync();


        var clientVehicles = (from v in vehicles
                join vai in fleetDbContext.VehicleAdditionalInfos on v.VehicleId equals vai.VehicleId into vaiJoin
                from vai in vaiJoin.DefaultIfEmpty()
                join vd in fleetDbContext.VehicleDepartments on v.VehicleId equals vd.VehicleId into vdJoin
                from vd in vdJoin.DefaultIfEmpty()
                join qdl in fleetDbContext.VehicleDriverLicenses on v.VehicleId equals qdl.VehicleId into qdlJoin
                from qdl in qdlJoin.DefaultIfEmpty()
                join pdp in fleetDbContext.VehicleSpecialLicenses on v.VehicleId equals pdp.VehicleId into pdpJoin
                from pdp in pdpJoin.DefaultIfEmpty()
                group new {
                    v,
                    vai,
                    vd,
                    pdp,
                    qdl
                } by v
                into g
                select new VehicleRecord(g.Key.VehicleId, g.Key.UserId, g.Key.Registration, 
                    g.Select(x => x.vai).Where(x => x != null).Select(vai =>
                        new VehicleAdditionalInfo {
                            VehicleName = vai.VehicleName,
                            ClientVehicleDescription = vai.ClientVehicleDescription,
                            FuelTargetConsumption = vai.FuelTargetConsumption,
                            VehicleEngineTypeId = vai.VehicleEngineTypeId,
                            MaintenanceId = vai.MaintenanceId,
                            DefaultSiteLocationId = vai.DefaultSiteLocationId,
                            IsPoolActive = vai.IsPoolActive,
                            MaintenanceStatusId = vai.MaintenanceStatusId,
                            BookingVehicleTypeId = vai.BookingVehicleTypeId,
                            VehicleStatusOptionId = vai.VehicleStatusOptionId,
                            BookingAllocationPriority = vai.BookingAllocationPriority,
                            SpeedSourceId = vai.SpeedSourceId,
                            DefaultDriver = vai.DefaultDriver,
                            CreatedTs = vai.Cts,
                            UpdatedTs = vai.Uts,
                        }).FirstOrDefault() ?? null, g.Where(x => x.vd != null).GroupBy(x => x.vd.DepartmentId).Select(x =>
                        new VehicleDepartment {
                            Id = x.Key,
                            Name =
                                departments.Where(a => a.DepartmentId == x.Key).Select(b => b.DepartmentName)
                                    .FirstOrDefault()
                        }).ToList(), g.Where(x => x.qdl != null).GroupBy(x => x.qdl.DriverLicenseTypeId).Select(x =>
                        new VehicleQdlLicense {
                            LicenseTypeId = x.Key,
                            LicenseName =
                                qdlLicenses.Where(a => a.DriverLicenseTypeId == x.Key).Select(b => b.Name)
                                    .FirstOrDefault()
                        }).ToList(), g.Where(x => x.pdp != null).GroupBy(x => x.pdp.SpecialLicenseTypeId)
                        .Select(x => new VehiclePdpLicense {
                            LicenseTypeId = x.Key,
                            LicenseName =
                                pdpLicenses.Where(a => a.DriverSpecialLicenseTypeId == x.Key).Select(b => b.LicenseName)
                                    .FirstOrDefault()
                        }).ToList())
            );
        
        return clientVehicles;
    }

    public async Task<int> GetTotal(long userId) {
        var fleetHardwareTypeLink = await fleetDbContext.FleetHardwareTypeLinks
            .Select(x => x.HardwareTypeId)
            .ToListAsync();

        return await (from v in ctDbContext.Vehicles
            join terminal in ctDbContext.Terminals on v.TerminalId equals terminal.TerminalId into vehicleTerminals
            from terminal in vehicleTerminals.DefaultIfEmpty()
            join contract in ctDbContext.Contracts on v.CurrentContractId equals contract.ContractId into
                vehicleContracts
            from contract in vehicleContracts.DefaultIfEmpty()
            join productPackage in ctDbContext.ProductPackages on contract.ProductPackageId equals productPackage
                .ProductPackageId into contractPackages
            from productPackage in contractPackages.DefaultIfEmpty()
            where (productPackage == null
                   || productPackage.HideFleetData == false
                   || productPackage.AllowSvrAccess == true)
                  && v.UserId == userId
                  && contract.ContractStateId == 2
                  && fleetHardwareTypeLink.Contains(terminal.HardwareTypeId)
            select new {
                v.VehicleId, v.Registration
            }).CountAsync();
    }
}