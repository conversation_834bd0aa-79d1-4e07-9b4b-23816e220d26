﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehicles;

public class GetVehiclesHandler(IVehicleRepository repo, IHttpContextAccessor context, ILogger<GetVehiclesHandler> logger)
    : IRequestHandler<GetVehiclesRequest, GetVehiclesResponse> {
    public async Task<GetVehiclesResponse> Handle(GetVehiclesRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving all vehicles", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var vehicles = await repo.GetVehicles(request.UserId, request.Page, request.PageSize);
            var total = await repo.GetTotal(request.UserId);
            var apiVehicle = vehicles?.ToHttpVehicles(total);
            return new GetVehiclesResponse(apiVehicle);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Vehicles List", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehiclesResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Vehicles List", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehiclesResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}