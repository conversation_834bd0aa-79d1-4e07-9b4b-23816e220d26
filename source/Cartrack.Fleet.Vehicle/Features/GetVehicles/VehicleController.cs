﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.Features.GetDriver;
using Cartrack.Fleet.Vehicle.Features.GetVehicles;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    public async Task<ActionResult<GetVehiclesResponse>> GetVehicles([FromQuery] int page = 1,
        [FromQuery] int pageSize = 10) {
        var request = new GetVehiclesRequest(page, pageSize);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetVehiclesResponse, Vehicles>(this.HttpContext);
    }
}