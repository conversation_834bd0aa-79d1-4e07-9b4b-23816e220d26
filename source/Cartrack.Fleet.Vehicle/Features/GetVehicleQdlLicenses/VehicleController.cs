﻿using Cartrack.Fleet.Vehicle.Features.GetVehicleQdlLicenses;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Driver.Features.GetDriver;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("qdllicenses/{vehicleId:long}")]
    public async Task<ActionResult<GetVehicleQdlLicensesResponse>> GetQdlLicenses(long vehicleId) {
        var request = new GetVehicleQdlLicensesRequest(vehicleId);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetVehicleQdlLicensesResponse, VehicleQdlLicenses>(this.HttpContext);
    }
}