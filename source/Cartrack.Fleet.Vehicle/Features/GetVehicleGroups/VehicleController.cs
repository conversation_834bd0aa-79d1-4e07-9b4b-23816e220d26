﻿using Cartrack.Fleet.Vehicle.Features.GetVehicleGroups;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Driver.Features.GetDriver;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("groups")]
    public async Task<ActionResult<GetVehicleGroupsResponse>> GetVehicleGroups([FromQuery] int page = 1,
        [FromQuery] int pageSize = 10) {
        var request = new GetVehicleGroupsRequest(page, pageSize);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetVehicleGroupsResponse, VehicleGroups>(this.HttpContext);
    }
}