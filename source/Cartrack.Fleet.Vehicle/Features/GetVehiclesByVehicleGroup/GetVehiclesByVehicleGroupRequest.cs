﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleGroup;

public record GetVehiclesByVehicleGroupRequest(long VehicleGroupId, int Page, int PageSize)
    : IRequest<GetVehiclesByVehicleGroupResponse> {
    [JsonIgnore] public string Account { get; set; } = "";

    [JsonIgnore] public long UserId { get; set; }

    //public string ClientDriverId { get; set; }
}