﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleGroup;

public class GetVehiclesByVehicleGroupHandler(IVehicleRepository repo, IHttpContextAccessor context, ILogger<GetVehiclesByVehicleGroupHandler> logger)
    : IRequestHandler<GetVehiclesByVehicleGroupRequest, GetVehiclesByVehicleGroupResponse> {
    public async Task<GetVehiclesByVehicleGroupResponse> Handle(GetVehiclesByVehicleGroupRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.VehicleGroupId > 0, "Vehicle Group ID must be greater than zero");
            //Requires.NotNullOrEmpty(request.VehicleId, nameof(request.Id));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving the vehicle with Group Id={VehicleGroupId}", context.HttpContext?.TraceIdentifier ?? "", request.Account,
                request.VehicleGroupId);

            var vehicle = await repo.GetVehiclesByVehicleGroup(request.UserId, request.VehicleGroupId, request.Page,
                request.PageSize);
            var total = await repo.GetTotalVehicleByVehicleGroups(request.UserId, request.VehicleGroupId);
            var apiVehicle = vehicle?.ToHttpVehicles(total);
            return new GetVehiclesByVehicleGroupResponse(apiVehicle);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Vehicle by Vehicle Group", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehiclesByVehicleGroupResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Vehicle by Vehicle Group", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehiclesByVehicleGroupResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}