﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.Features.GetDriver;
using Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleGroup;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("group/{vehicleGroupId:long}")]
    public async Task<ActionResult<GetVehiclesByVehicleGroupResponse>> GetVehicleByVehicleGroup(long vehicleGroupId,
        [FromQuery] int page = 1, [FromQuery] int pageSize = 10) {
        var request = new GetVehiclesByVehicleGroupRequest(vehicleGroupId, page, pageSize);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetVehiclesByVehicleGroupResponse, Vehicles>(this.HttpContext);
    }
}