﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehicleByRegistration;

public class GetVehicleByRegistrationHandler(IVehicleRepository repo, IHttpContextAccessor context, ILogger<GetVehicleByRegistrationHandler> logger)
    : IRequestHandler<GetVehicleByRegistrationRequest, GetVehicleByRegistrationResponse> {
    public async Task<GetVehicleByRegistrationResponse> Handle(GetVehicleByRegistrationRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.VehicleId > 0, "Vehicle ID must be greater than zero");
            Requires.NotNullOrEmpty(request.RegistrationNumber, nameof(request.RegistrationNumber));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving the vehicle with Registration Number={VehicleRegistration}",
                context.HttpContext?.TraceIdentifier ?? "",
                request.Account, 
                request.RegistrationNumber);

            var vehicle = await repo.GetVehicleByRegistration(request.UserId, request.RegistrationNumber);
            var apiVehicle = vehicle?.ToHttpVehicle();
            return new GetVehicleByRegistrationResponse(apiVehicle);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Vehicle by Registration Number.", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehicleByRegistrationResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Vehicle by Registration Number.", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehicleByRegistrationResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}