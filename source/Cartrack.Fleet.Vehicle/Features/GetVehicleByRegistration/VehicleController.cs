﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.Features.GetDriver;
using Cartrack.Fleet.Vehicle.Features.GetVehicleByRegistration;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("registration/{id}")]
    public async Task<ActionResult<GetVehicleByRegistrationResponse>> GetVehicleByRegistration(string id = "FBB1234X") {
        var request = new GetVehicleByRegistrationRequest(id);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetVehicleByRegistrationResponse, Vehicle>(this.HttpContext);
    }
}