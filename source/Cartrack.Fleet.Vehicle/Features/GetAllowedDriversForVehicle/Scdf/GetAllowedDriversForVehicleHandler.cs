﻿using Cartrack.AppHost;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.Features.GetDriversByUserId;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.License.Features.ValidateLicenses;
using Cartrack.Fleet.Vehicle.Features.GetVehicleById;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetDriversForVehicle.Scdf;

public class GetAllowedDriversForVehicleHandler(IDriverRepository repo, IMediator mediator, IHttpContextAccessor context, AppSettings appSettings, ILogger<GetAllowedDriversForVehicleHandler> logger)
    : IRequestHandler<GetAllowedDriversForVehicleRequest, GetAllowedDriversForVehicleResponse> {
    public async Task<GetAllowedDriversForVehicleResponse> Handle(GetAllowedDriversForVehicleRequest request, CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.Id > 0, "Accessory ID must be greater than zero");
            Requires.IsTrue(() => request.VehicleId > 0, () => "VehicleId must be greater than zero");
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving the drivers for vehicle with Id={Id}", context.HttpContext?.TraceIdentifier ?? "", request.Account,
                request.VehicleId);

            var getVehicleRequest = new GetVehicleByIdRequest(request.VehicleId) { Account = request.Account, UserId = request.UserId };
            var getVehicleResponse = await mediator.Send(getVehicleRequest, cancellationToken);
            Requires.IsTrue(() => getVehicleResponse.Value is not null, () => $"Vehicle with Id {request.VehicleId} not found");

            var vehicle = getVehicleResponse.Value!;
            Requires.IsTrue(
                () => (vehicle.DriverLicenses.Count > 0 || vehicle.SpecialDriverLicenses.Count > 0),
                () => $"Vehicle  with id {request.VehicleId} hs no licenses");
            
            var activeDriversRequest = new GetDriversByUserIdRequest() { UserId = request.UserId, Account = request.Account, };
            var activeDriversResponse = await mediator.Send(activeDriversRequest, cancellationToken);
            Requires.IsTrue(
                () => activeDriversResponse.Value is not null && activeDriversResponse.Value.Any(),
                () => $"Active drivers for user with Id {request.UserId} not found. {activeDriversResponse.Error}");

            var registration = vehicle.Registration;
            var drivers = activeDriversResponse.Value!;
            var licenseCheckTasks = drivers.Select(async v => {
                var req = new ValidateLicensesRequest(
                    registration,
                    v.ClientDriverId,
                    v.NormalLicenses.Where(t => !string.IsNullOrWhiteSpace(t.LicenseName)).Select(t => t.LicenseName!).ToArray(),
                    v.SpecialLicenses.Where(t => !string.IsNullOrWhiteSpace(t.LicenseName)).Select(t => t.LicenseName!).ToArray(),
                    vehicle.DriverLicenses.Where(t => !string.IsNullOrWhiteSpace(t.LicenseName)).Select(t => t.LicenseName!).ToArray(),
                    vehicle.SpecialDriverLicenses.Where(t => !string.IsNullOrWhiteSpace(t.LicenseName)).Select(t => t.LicenseName!).ToArray());
                var resp = await mediator.Send(req, cancellationToken);
                return new { Driver = v, IsMatch = resp.IsOk && resp.Value!.IsMatch };
            }).ToArray();
            
            await Task.WhenAll(licenseCheckTasks);
            var apiDriver = licenseCheckTasks
                .Where(t => !t.IsFaulted && t.Result.IsMatch)
                .Select(t => t.Result.Driver)
                .ToArray();
             
             return new GetAllowedDriversForVehicleResponse(apiDriver);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Driver", context.HttpContext?.TraceIdentifier ?? "");
            return new GetAllowedDriversForVehicleResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Driver", context.HttpContext?.TraceIdentifier ?? "");
            return new GetAllowedDriversForVehicleResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}