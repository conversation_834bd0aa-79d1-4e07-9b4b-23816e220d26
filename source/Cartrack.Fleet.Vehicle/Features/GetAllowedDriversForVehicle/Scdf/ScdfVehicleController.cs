﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Driver.IO.Http;
using Cartrack.Fleet.Vehicle.Features.GetDriversForVehicle.Scdf;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class ScdfVehicleController {
    [HttpGet]
    [Route("{vehicleId}/allowed-drivers")]
    public async Task<ActionResult<GetAllowedDriversForVehicleResponse>> GetDriversForVehicle(long vehicleId) {
        var request = new GetAllowedDriversForVehicleRequest(vehicleId);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
       
        var resp = await this._mediator.Send(request);
        return BaseResponseExtensions.ToContentResult<GetAllowedDriversForVehicleResponse, DriverSlim[]>(resp, this.HttpContext);
    }
}