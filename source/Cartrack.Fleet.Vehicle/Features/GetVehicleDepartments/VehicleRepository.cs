﻿using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class VehicleRepository {
    public async Task<List<VehicleDepartment>> GetDepartments(long userId, long vehicleId) {
        var vehicleDepartments = await (
            from vd in fleetDbContext.VehicleDepartments
            join dept in fleetDbContext.Departments on vd.DepartmentId equals dept.DepartmentId into deptJoined
            from dept in deptJoined.Where(x => !x.IsDeleted).DefaultIfEmpty()
            where vd.VehicleId == vehicleId
            select new {
                VehicleDepartment = vd, Department = dept ?? null
            }
        ).ToListAsync();

        List<VehicleDepartment> vehicleDepartmentsList = [];
        vehicleDepartmentsList.AddRange(vehicleDepartments
            .Where(p => p.Department != null)
            .Select(c => new VehicleDepartment() {
                Id = c.VehicleDepartment.DepartmentId, Name = c.Department.DepartmentName,
            })
        );

        return vehicleDepartmentsList;
    }
}