﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.Features.GetVehicleDepartments;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehicleDepartments;

public class GetVehicleDepartmentsHandler(IVehicleRepository repo, IHttpContextAccessor context, ILogger<GetVehicleDepartmentsHandler> logger)
    : IRequestHandler<GetVehicleDepartmentsRequest, GetVehicleDepartmentsResponse> {
    public async Task<GetVehicleDepartmentsResponse> Handle(GetVehicleDepartmentsRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving all driver department belong to {VehicleId}", context.HttpContext?.TraceIdentifier ?? "", request.Account,
                request.VehicleId);

            var vehicleDepartments = await repo.GetDepartments(request.UserId, request.VehicleId);
            var apiVehicle = vehicleDepartments?.ToHttpVehicleDepartments();
            return new GetVehicleDepartmentsResponse(apiVehicle);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Vehicle Departments", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehicleDepartmentsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Vehicle Department", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehicleDepartmentsResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}