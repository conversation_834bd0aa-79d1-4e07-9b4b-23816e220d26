﻿using Cartrack.Fleet.Vehicle.Features.GetVehicleDepartments;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Driver.Features.GetDriver;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("departments/{vehicleId:long}")]
    public async Task<ActionResult<GetVehicleDepartmentsResponse>> GetDepartments(long vehicleId = 0) {
        var request = new GetVehicleDepartmentsRequest(vehicleId);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetVehicleDepartmentsResponse, VehicleDepartments>(this.HttpContext);
    }
}