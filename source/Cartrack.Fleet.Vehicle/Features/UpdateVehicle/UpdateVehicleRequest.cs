﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Vehicle.Features.UpdateVehicle;

public record UpdateVehicleRequest(long VehicleId) : IRequest<UpdateVehicleResponse> {
    [JsonIgnore] public string Account { get; set; } = "";

    [JsonIgnore] public long UserId { get; set; }

    [JsonIgnore] public long VehicleId { get; set; } = VehicleId;
    public string? VehicleName { get; set; }
    public string? ClientVehicleDescription { get; set; }
    public decimal? FuelTargetConsumption { get; set; }
    public long? VehicleEngineTypeId { get; set; }
    public long? VehicleCategoryId { get; set; } // booking_vehicle_type_id
    public string? DefaultDriver { get; set; }
    public long? DefaultSiteLocationId { get; set; }
    public decimal? BookingAllocationPriority { get; set; }
    public long? VehicleStatusOptionId { get; set; }
    public int? MonthlyMileageLimit { get; set; }
    public List<long>? DepartmentIds { get; set; }
    public List<long>? DriverLicenseIds { get; set; }

    public List<long>? SpecialDriverLicenseIds { get; set; }

    // if there is anymore, please feel free to insert!
    public bool? IsPoolActive { get; set; }
}