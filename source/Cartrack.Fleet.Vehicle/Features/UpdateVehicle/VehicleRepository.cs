﻿using Db = Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Microsoft.EntityFrameworkCore;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Sql;

public partial class VehicleRepository {
    public async Task UpdateVehicle(long userId, long vehicleId, VehicleBase vehicle) {
        // Check if the driver already exists
        var existingVehicleCt = await ctDbContext.Vehicles
            .FirstOrDefaultAsync(d => d.UserId == userId && d.VehicleId == vehicleId);

        var vehicleAdditionalInfo = await fleetDbContext.VehicleAdditionalInfos
            .FirstOrDefaultAsync(d => d.VehicleId == vehicleId);

        if (existingVehicleCt != null && vehicleAdditionalInfo != null) {
            if (!string.IsNullOrWhiteSpace(vehicle.VehicleName))
                vehicleAdditionalInfo.VehicleName = vehicle.VehicleName;
            if (!string.IsNullOrWhiteSpace(vehicle.ClientVehicleDescription))
                vehicleAdditionalInfo.ClientVehicleDescription = vehicle.ClientVehicleDescription;
            //vehicleAdditionalInfo.FuelAvgConsumptionCombined = null;
            if (!string.IsNullOrWhiteSpace(vehicle.FuelTargetConsumption.ToString()))
                vehicleAdditionalInfo.FuelTargetConsumption = vehicle.FuelTargetConsumption;
            if (!string.IsNullOrWhiteSpace(vehicle.VehicleEngineTypeId.ToString()))
                vehicleAdditionalInfo.VehicleEngineTypeId = vehicle.VehicleEngineTypeId;
            //vehicleAdditionalInfo.VehicleFuelTypeId = null;

            //Vehicle Settings Section
            if (!string.IsNullOrWhiteSpace(vehicle.MonthlyMileageLimit.ToString()))
                vehicleAdditionalInfo.MonthlyMileageLimit = vehicle.MonthlyMileageLimit;
            if (!string.IsNullOrWhiteSpace(vehicle.BookingVehicleTypeId.ToString()))
                vehicleAdditionalInfo.BookingVehicleTypeId = vehicle.BookingVehicleTypeId; //Category
            if (!string.IsNullOrWhiteSpace(vehicle.DefaultDriver))
                vehicleAdditionalInfo.DefaultDriver = vehicle.DefaultDriver;
            if (!string.IsNullOrWhiteSpace(vehicle.DefaultSiteLocationId.ToString()))
                vehicleAdditionalInfo.DefaultSiteLocationId = vehicle.DefaultSiteLocationId; // Location

            if (!string.IsNullOrWhiteSpace(vehicle.BookingAllocationPriority.ToString()))
                vehicleAdditionalInfo.BookingAllocationPriority = vehicle.BookingAllocationPriority ?? 1;
            if (!string.IsNullOrWhiteSpace(vehicle.VehicleStatusOptionId.ToString()))
                vehicleAdditionalInfo.VehicleStatusOptionId = vehicle.VehicleStatusOptionId ?? 0;
            if (!string.IsNullOrWhiteSpace(vehicle.IsPoolActive.ToString()))
                vehicleAdditionalInfo.IsPoolActive = vehicle.IsPoolActive ?? false;
            vehicleAdditionalInfo.Uts = vehicle.UpdatedTs;
        }
        else {
            throw new Exception("Vehicle is not found. Please create a vehicle first.");
        }

        //////////////////////////////////////////////
        // To update Vehicle Departments into DB -- Full Sync
        ///////////////////////////////////////
        if (vehicle.Departments != null) {
            List<long> departmentIds = vehicle.Departments.Select(d => d.Id).ToList();
            // Step 1: Get current vehicle-department links
            var existingLinks = await fleetDbContext.VehicleDepartments
                .Where(vd => vd.VehicleId == vehicleId)
                .ToListAsync();

            var existingDeptIds = existingLinks.Select(vd => vd.DepartmentId).ToHashSet();
            var newDeptIds = departmentIds.ToHashSet();

            // Step 2: Find which to remove (in existing, but not in new)
            var toRemove = existingLinks
                .Where(link => !newDeptIds.Contains(link.DepartmentId))
                .ToList();

            // Step 3: Find which to add (in new, but not in existing)
            var toAdd = newDeptIds
                .Where(id => !existingDeptIds.Contains(id))
                .Select(id => new Db.VehicleDepartment {
                    VehicleId = vehicleId, DepartmentId = id
                })
                .ToList();

            // Step 4: Apply changes
            if (toRemove.Count != 0)
                fleetDbContext.VehicleDepartments.RemoveRange(toRemove);

            if (toAdd.Count != 0)
                await fleetDbContext.VehicleDepartments.AddRangeAsync(toAdd);
        }

        //////////////////////////////////////////////
        // To update Vehicle - Driver Licenses into DB -- Full Sync
        ///////////////////////////////////////
        if (vehicle.DriverLicenses != null) {
            var validDriverLicenseIds = await fleetDbContext.DriverLicenseTypes
                .Where(d => vehicle.DriverLicenses.Select(d => d.LicenseTypeId).Contains(d.DriverLicenseTypeId)
                            && (d.UserId == userId || d.UserId == null))
                .Select(d => d.DriverLicenseTypeId)
                .ToListAsync();

            // Step 1: Get current vehicle-driver license links
            var existingLinks = await fleetDbContext.VehicleDriverLicenses
                .Where(vd => vd.VehicleId == vehicleId)
                .ToListAsync();

            var existingDriverLicenseIds = existingLinks.Select(vd => vd.DriverLicenseTypeId).ToHashSet();
            var newDriverLicenseIds = validDriverLicenseIds.ToHashSet();

            // Step 2: Find which to remove (in existing, but not in new)
            var toRemove = existingLinks
                .Where(link => !newDriverLicenseIds.Contains(link.DriverLicenseTypeId))
                .ToList();

            // Step 3: Find which to add (in new, but not in existing)
            var toAdd = newDriverLicenseIds
                .Where(id => !existingDriverLicenseIds.Contains(id))
                .Select(id => new Db.VehicleDriverLicense {
                    VehicleId = vehicleId, DriverLicenseTypeId = id
                })
                .ToList();

            // Step 4: Apply changes
            if (toRemove.Count != 0)
                fleetDbContext.VehicleDriverLicenses.RemoveRange(toRemove);

            if (toAdd.Count != 0)
                await fleetDbContext.VehicleDriverLicenses.AddRangeAsync(toAdd);
        }

        //////////////////////////////////////////////
        // To update Vehicle - Special Driver Licenses into DB -- Full Sync
        ///////////////////////////////////////
        if (vehicle.SpecialDriverLicenses != null) {
            var validSpecialDriverLicenseIds = await fleetDbContext.DriverSpecialLicenseTypes
                .Where(d => vehicle.SpecialDriverLicenses.Select(d => d.LicenseTypeId)
                                .Contains(d.DriverSpecialLicenseTypeId)
                            && (d.UserId == userId || d.UserId == null))
                .Select(d => d.DriverSpecialLicenseTypeId)
                .ToListAsync();

            // Step 1: Get current vehicle-driver license links
            var existingLinks = await fleetDbContext.VehicleSpecialLicenses
                .Where(vd => vd.VehicleId == vehicleId)
                .ToListAsync();

            var existingDriverLicenseIds = existingLinks.Select(vd => vd.SpecialLicenseTypeId).ToHashSet();
            var newSpecialLicenseIds = validSpecialDriverLicenseIds.ToHashSet();

            // Step 2: Find which to remove (in existing, but not in new)
            var toRemove = existingLinks
                .Where(link => !newSpecialLicenseIds.Contains(link.SpecialLicenseTypeId))
                .ToList();

            // Step 3: Find which to add (in new, but not in existing)
            var toAdd = newSpecialLicenseIds
                .Where(id => !existingDriverLicenseIds.Contains(id))
                .Select(id => new Db.VehicleSpecialLicense() {
                    VehicleId = vehicleId, SpecialLicenseTypeId = id
                })
                .ToList();

            // Step 4: Apply changes
            if (toRemove.Count != 0)
                fleetDbContext.VehicleSpecialLicenses.RemoveRange(toRemove);

            if (toAdd.Count != 0)
                await fleetDbContext.VehicleSpecialLicenses.AddRangeAsync(toAdd);
        }

        await fleetDbContext.SaveChangesAsync();
    }
}