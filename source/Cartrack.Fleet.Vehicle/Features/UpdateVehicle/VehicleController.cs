﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.Features.GetDriver;
using Cartrack.Fleet.Vehicle.Features.UpdateVehicle;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpPatch]
    [Route("{vehicleId:long}")]
    public async Task<ActionResult<UpdateVehicleResponse>> UpdateVehicle(long vehicleId,
        [FromBody] UpdateVehicleRequest request) {
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        request.VehicleId = vehicleId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<UpdateVehicleResponse, Vehicle>(this.HttpContext);
    }
}