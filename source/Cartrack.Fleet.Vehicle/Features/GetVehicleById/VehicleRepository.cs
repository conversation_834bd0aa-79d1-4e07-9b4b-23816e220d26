﻿using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using VehicleAdditionalInfo = Cartrack.Fleet.Vehicle.Domain.Common.VehicleAdditionalInfo;
using VehicleDepartment = Cartrack.Fleet.Vehicle.Domain.Common.VehicleDepartment;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class VehicleRepository {
    public async Task<VehicleBase?> GetVehicleById(long userId, long vehicleId) {
        var vehicle = await ctDbContext.Vehicles.Where(x => x.VehicleId == vehicleId)
            .FirstOrDefaultAsync();

        if (vehicle is null || vehicle?.UserId != userId) {
            return null;
        }

        var clientVehicle = await (from vai in fleetDbContext.VehicleAdditionalInfos
                where vai.VehicleId == vehicleId
                join vd in fleetDbContext.VehicleDepartments on vai.VehicleId equals vd.VehicleId into vdJoin
                from vd in vdJoin.DefaultIfEmpty()
                join dept in fleetDbContext.Departments on vd.DepartmentId equals dept.DepartmentId into deptJoin
                from dept in deptJoin.DefaultIfEmpty()
                join qdl in fleetDbContext.VehicleDriverLicenses on vai.VehicleId equals qdl.VehicleId into qdlJoin
                from qdl in qdlJoin.DefaultIfEmpty()
                join qdlname in fleetDbContext.DriverLicenseTypes on qdl.DriverLicenseTypeId equals qdlname
                    .DriverLicenseTypeId into qdlnameJoin
                from qdlname in qdlnameJoin.Where(x => x.UserId == userId || x.UserId == null).DefaultIfEmpty()
                join pdp in fleetDbContext.VehicleSpecialLicenses on vai.VehicleId equals pdp.VehicleId into pdpJoin
                from pdp in pdpJoin.DefaultIfEmpty()
                join pdpname in fleetDbContext.DriverSpecialLicenseTypes on pdp.SpecialLicenseTypeId equals pdpname
                    .DriverSpecialLicenseTypeId into pdpnameJoin
                from pdpname in pdpnameJoin.Where(x => x.UserId == userId || x.UserId == null).DefaultIfEmpty()
                group new {
                    vai,
                    vd,
                    dept,
                    qdl,
                    qdlname,
                    pdp,
                    pdpname
                } by vai
                into g
                select new {
                    g.Key.VehicleId,
                    //g.Key.UserId,
                    //g.Key.Registration,
                    AdditionalInfo = g.Select(x => new VehicleAdditionalInfo {
                        VehicleName = x.vai.VehicleName,
                        ClientVehicleDescription = x.vai.ClientVehicleDescription,
                        FuelTargetConsumption = x.vai.FuelTargetConsumption,
                        VehicleEngineTypeId = x.vai.VehicleEngineTypeId,
                        MaintenanceId = x.vai.MaintenanceId,
                        DefaultSiteLocationId = x.vai.DefaultSiteLocationId,
                        IsPoolActive = x.vai.IsPoolActive,
                        CommonPool = x.vai.CommonPool,
                        MaintenanceStatusId = x.vai.MaintenanceStatusId,
                        BookingVehicleTypeId = x.vai.BookingVehicleTypeId,
                        VehicleStatusOptionId = x.vai.VehicleStatusOptionId,
                        BookingAllocationPriority = x.vai.BookingAllocationPriority,
                        SpeedSourceId = x.vai.SpeedSourceId,
                        DefaultDriver = x.vai.DefaultDriver,
                        CreatedTs = x.vai.Cts, // cts
                        UpdatedTs = x.vai.Uts,
                    }).FirstOrDefault(),
                    Departments =
                        g.Select(x => new VehicleDepartment {
                                Id = x.vd.DepartmentId, Name = x.dept.DepartmentName
                            })
                            .Distinct().ToList(),
                    DriverLicenses =
                        g.GroupBy(a => a.qdl.DriverLicenseTypeId).Select(x =>
                                new VehicleQdlLicense {
                                    LicenseTypeId = x.Key, LicenseName = x.First().qdlname.Name
                                })
                            .ToList(),
                    SpecialDriverLicenses = g.GroupBy(x => x.pdp.SpecialLicenseTypeId).Select(x =>
                            new VehiclePdpLicense {
                                LicenseTypeId = x.Key, LicenseName = x.First().pdpname.LicenseName
                            })
                        .ToList()
                }
            ).FirstOrDefaultAsync();

        if (clientVehicle is null) {
            return null;
        }

        var vehicleRepo = new VehicleBase {
            VehicleId = vehicle.VehicleId,
            UserId = vehicle.UserId,
            Registration = vehicle.Registration,
            VehicleName = clientVehicle.AdditionalInfo.VehicleName ?? "",
            ClientVehicleDescription = clientVehicle.AdditionalInfo.ClientVehicleDescription ?? "",
            FuelTargetConsumption = clientVehicle.AdditionalInfo.FuelTargetConsumption,
            VehicleEngineTypeId = clientVehicle.AdditionalInfo.VehicleEngineTypeId,
            MaintenanceId = clientVehicle.AdditionalInfo.MaintenanceId,
            DefaultSiteLocationId = clientVehicle.AdditionalInfo.DefaultSiteLocationId,
            IsPoolActive = clientVehicle.AdditionalInfo.IsPoolActive,
            CommonPool = clientVehicle.AdditionalInfo.CommonPool,
            MaintenanceStatusId = clientVehicle.AdditionalInfo.MaintenanceStatusId,
            BookingVehicleTypeId = clientVehicle.AdditionalInfo.BookingVehicleTypeId,
            VehicleStatusOptionId = clientVehicle.AdditionalInfo.VehicleStatusOptionId,
            BookingAllocationPriority = clientVehicle.AdditionalInfo.BookingAllocationPriority,
            SpeedSourceId = clientVehicle.AdditionalInfo.SpeedSourceId,
            DefaultDriver = clientVehicle.AdditionalInfo.DefaultDriver,
            CreatedTs = clientVehicle.AdditionalInfo.CreatedTs,
            UpdatedTs = clientVehicle.AdditionalInfo.UpdatedTs,
            Departments = clientVehicle.Departments,
            DriverLicenses = clientVehicle.DriverLicenses,
            SpecialDriverLicenses = clientVehicle.SpecialDriverLicenses
        };

        return vehicleRepo;
    }
}