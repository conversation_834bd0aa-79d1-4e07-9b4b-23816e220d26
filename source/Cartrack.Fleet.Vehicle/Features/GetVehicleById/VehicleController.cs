﻿using Cartrack.Fleet.Vehicle.Features.GetVehicleById;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Driver.Features.GetDriver;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("id/{id}")]
    public async Task<ActionResult<GetVehicleByIdResponse>> GetDriverById(long id = 0) {
        var request = new GetVehicleByIdRequest(id);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetVehicleByIdResponse, Vehicle>(this.HttpContext);
    }
}