﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.Features.GetVehicleCategories;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehicleCategories;

public class GetVehicleCategoriesHandler(IVehicleRepository repo, IHttpContextAccessor context, ILogger<GetVehicleCategoriesHandler> logger)
    : IRequestHandler<GetVehicleCategoriesRequest, GetVehicleCategoriesResponse> {
    public async Task<GetVehicleCategoriesResponse> Handle(GetVehicleCategoriesRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{TraceId}] [{Agency}] Retrieving all vehicles categories", context.HttpContext?.TraceIdentifier ?? "", request.Account);

            var vehicleCategories = await repo.GetVehicleCategories(request.UserId, request.Page, request.PageSize);
            var total = await repo.GetTotalVehicleCategories(request.UserId);
            var apiVehicleCategory = vehicleCategories?.ToHttpVehicleCategories(total);
            return new GetVehicleCategoriesResponse(apiVehicleCategory);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "[{TraceId}] Invalid request value. Error retrieving Vehicles Categories List", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehicleCategoriesResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "[{TraceId}] Error retrieving Vehicles Categories List", context.HttpContext?.TraceIdentifier ?? "");
            return new GetVehicleCategoriesResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}