﻿using Cartrack.Fleet.Vehicle.Features.GetVehicleCategories;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using Cartrack.Fleet.Driver.Features.GetDriver;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("categories")]
    public async Task<ActionResult<GetVehicleCategoriesResponse>> GetVehicleCategories([FromQuery] int page = 1,
        [FromQuery] int pageSize = 10) {
        var request = new GetVehicleCategoriesRequest(page, pageSize);
        var authClaims = AuthClaims.From(this.User);
        request.Account = authClaims.Account;
        request.UserId = authClaims.UserId;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<GetVehicleCategoriesResponse, VehicleCategories>(this.HttpContext);
    }
}