﻿using Cartrack.EFCore.Models.Pool;
using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Vehicle.IO.Http;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Vehicle.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class VehicleRepository {
    public async Task<List<VehicleCategory>> GetVehicleCategories(long userId, int page, int pageSize) {
        var vehicleCategories = await poolDbContext.BookingVehicleTypes
            .Where(c => c.UserId == userId && !c.IsDeleted)
            .Select(x => new VehicleCategory {
                Id = x.BookingVehicleTypeId, Name = x.BookingVehicleType1
            })
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        var categoryIds = vehicleCategories.Select(c => c.Id).ToList();

        var vehicleCounts = await fleetDbContext.VehicleAdditionalInfos
            .Where(v => categoryIds.Contains((long)v.BookingVehicleTypeId))
            .GroupBy(v => v.BookingVehicleTypeId)
            .Select(g => new {
                CategoryId = g.Key, Count = g.Count()
            })
            .ToListAsync();

        foreach (var category in vehicleCategories) {
            var countEntry = vehicleCounts.FirstOrDefault(vc => vc.CategoryId == category.Id);
            category.VehicleCount = countEntry?.Count ?? 0;
        }

        return vehicleCategories;
    }

    public async Task<int> GetTotalVehicleCategories(long userId) {
        return await poolDbContext.BookingVehicleTypes
            .Where(c => c.UserId == userId && !c.IsDeleted)
            .CountAsync();
    }
}