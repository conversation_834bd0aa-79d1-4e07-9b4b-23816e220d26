﻿using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Api = Cartrack.Fleet.Vehicle.IO.Http;

namespace Cartrack.Fleet.Vehicle.IO;

public static class VehicleApiExtensions {
    public static Api.Vehicle? ToHttpVehicle(this VehicleBase? entity) {
        if (entity == null) return null;

        return new Api.Vehicle {
            VehicleId = entity.VehicleId,
            Registration = entity.Registration ?? string.Empty,
            VehicleName = entity.VehicleName,
            ClientVehicleDescription = entity.ClientVehicleDescription,
            FuelTargetConsumption = entity.FuelTargetConsumption,
            VehicleEngineTypeId = entity.VehicleEngineTypeId,
            MaintenanceId = entity.MaintenanceId,
            DefaultSiteLocationId = entity.DefaultSiteLocationId,
            IsPoolActive = entity.IsPoolActive,
            MaintenanceStatusId = entity.MaintenanceStatusId,
            BookingVehicleTypeId = entity.BookingVehicleTypeId,
            VehicleStatusOptionId = entity.VehicleStatusOptionId,
            BookingAllocationPriority = entity.BookingAllocationPriority,
            SpeedSourceId = entity.SpeedSourceId,
            DefaultDriver = entity.DefaultDriver,
            CreatedTs = entity.CreatedTs,
            UpdatedTs = entity.UpdatedTs,
            Departments = entity.Departments,
            DriverLicenses = entity.DriverLicenses,
            SpecialDriverLicenses = entity.SpecialDriverLicenses
        };
    }

    public static Api.Vehicles ToHttpVehicles(this List<VehicleBase>? entities, int totalCount) {
        if (entities == null)
            return new Api.Vehicles {
                TotalCount = totalCount, Data = []
            };

        var vehicles = entities
            .Select(e => e.ToHttpVehicle())
            .Where(d => d != null)
            .ToList();

        return new Api.Vehicles {
            TotalCount = totalCount, Data = vehicles!
        };
    }

    public static Api.VehicleCategories ToHttpVehicleCategories(this List<Api.VehicleCategory>? entities,
        int totalCount) {
        if (entities == null) return null;

        return new Api.VehicleCategories {
            TotalCount = totalCount, Data = entities ?? null,
        };
    }

    public static Api.VehicleGroups ToHttpVehicleGroups(this List<Api.VehicleGroup>? entities, int totalCount) {
        if (entities == null) return null;

        return new Api.VehicleGroups {
            TotalCount = totalCount, Data = entities ?? null,
        };
    }

    public static Api.VehicleDepartments ToHttpVehicleDepartments(this List<VehicleDepartment>? entities) {
        if (entities == null) return null;

        return new Api.VehicleDepartments {
            Data = entities ?? null,
        };
    }

    public static Api.VehiclePdpLicenses? ToHttpPdpLicenses(this List<VehiclePdpLicense>? entities) {
        if (entities == null) return null;

        return new Api.VehiclePdpLicenses {
            Data = entities ?? null,
        };
    }

    public static Api.VehicleQdlLicenses? ToHttpQdlLicenses(this List<VehicleQdlLicense>? entities) {
        if (entities == null) return null;

        return new Api.VehicleQdlLicenses {
            Data = entities ?? null,
        };
    }
}