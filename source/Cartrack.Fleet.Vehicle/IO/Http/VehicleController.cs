﻿using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.IO.Http;

[ApiController]
[Route("vehicle")]
public partial class VehicleController : ControllerBase {
    private readonly AppSettings _appSettings;
    private readonly ILogger<VehicleController> _logger;
    private readonly IMediator _mediator;

    public VehicleController(ILogger<VehicleController> logger, IMediator mediator, AppSettings appSettings) {
        this._logger = logger;
        this._mediator = mediator;
        this._appSettings = appSettings;
    }
}