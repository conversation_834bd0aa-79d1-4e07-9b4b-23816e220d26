﻿using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;

namespace Cartrack.Fleet.Vehicle.IO.Http;

public class Vehicle {
    public required long VehicleId { get; set; }
    public required string Registration { get; set; }
    public string? VehicleName { get; set; }
    public string? ClientVehicleDescription { get; set; }
    public decimal? FuelTargetConsumption { get; set; }
    public long? VehicleEngineTypeId { get; set; }
    public short? MaintenanceId { get; set; }
    public long? DefaultSiteLocationId { get; set; }
    public bool? IsPoolActive { get; set; }
    public long? MaintenanceStatusId { get; set; }
    public long? BookingVehicleTypeId { get; set; }
    public long? VehicleStatusOptionId { get; set; }
    public decimal? BookingAllocationPriority { get; set; }
    public int? SpeedSourceId { get; set; }
    public string? DefaultDriver { get; set; }
    public DateTime? CreatedTs { get; set; }
    public DateTimeOffset? UpdatedTs { get; set; }
    public List<VehicleDepartment> Departments { get; set; } = [];
    public List<VehicleQdlLicense> DriverLicenses { get; set; } = [];
    public List<VehiclePdpLicense> SpecialDriverLicenses { get; set; } = [];
}

public record VehicleStatus(long VehicleId, string Status);