﻿using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Vehicle.IO.Http;
using System.Linq.Expressions;

namespace Cartrack.Fleet.Vehicle.IO.Sql;

public interface IVehicleRepository {
    Task<VehicleBase?> GetVehicleById(long userId, long vehicleId);
    Task<VehicleBase?> GetVehicleByRegistration(long userId, string registrationNumber);
    Task<List<VehicleBase>> GetVehiclesByVehicleGroup(long userId, long vehicleGroupId, int page, int pageSize);
    Task<int> GetTotalVehicleByVehicleGroups(long userId, long vehicleGroupId);
    Task<List<VehicleBase>> GetVehiclesByVehicleCategory(long userId, long bookingVehicleTypeId, int page, int pageSize);
    Task<int> GetTotalVehiclesByVehicleCategory(long userId, long bookingVehicleTypeId);
    Task<List<VehicleBase>> GetAllVehiclesByVehicleCategory(long userId, long bookingVehicleTypeId);
    Task<List<VehicleGroup>> GetVehicleGroups(long userId, int page, int pageSize);
    Task<int> GetTotalVehicleGroups(long userId);
    Task<List<VehicleCategory>> GetVehicleCategories(long userId, int page, int pageSize);
    Task<int> GetTotalVehicleCategories(long userId);
    Task<List<VehicleBase>> GetVehicles(long userId, int page, int pageSize);
    Task<List<VehicleBase>> GetAllVehicles(long userId);

    Task<int> GetTotal(long userId);

    //Task GetVehicleGroups(int userId, long vehicleGroupId);
    Task<List<VehicleDepartment>> GetDepartments(long userId, long vehicleId);
    Task<List<VehiclePdpLicense>> GetPdpLicensesById(long userId, long vehicleId);
    Task<List<VehicleQdlLicense>> GetQdlLicensesById(long userId, long vehicleId);

    Task UpdateVehicle(long userId, long vehicleId, VehicleBase driver);
    //Task DeleteLicenses(int userId, long vehicleId);
    //Task AddLicenses(int userId, long vehicleId);
}