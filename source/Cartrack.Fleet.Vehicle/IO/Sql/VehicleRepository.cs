﻿using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Vehicle.IO.Sql;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Vehicle.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class VehicleRepository(
    AppCtDbContext ctDbContext,
    AppFleetDbContext fleetDbContext,
    AppPoolDbContext poolDbContext) : IVehicleRepository {
    /*public async Task UpdateStatus(long bookingId, BookingStatusCode bookingStatusId) {
        var booking = await poolDbContext.Bookings.FirstOrDefaultAsync(b => b.BookingId == bookingId);

        if (booking is not null) {
            var stat = await poolDbContext.BookingStatuses.FirstOrDefaultAsync(s => s.BookingStatusId == (long)bookingStatusId);
            if (stat is not null) {
                booking.BookingStatusId = stat.BookingStatusId;
                await poolDbContext.SaveChangesAsync();
            }
        }
    }*/
}