﻿using Cartrack.Fleet.Vehicle.Domain.Common;
using Db = Cartrack.EFCore.Models.Fleet;

namespace Cartrack.Fleet.Driver.IO;

public static class VehicleSqlExtensions {
    /*
     public static VehicleBase ToBooking(this Db.ClientDriver dbBooking) {
        throw new NotImplementedException("TODO");
    }


    public static Db.ClientDriver FromBooking(this DriverBase booking) {
        throw new NotImplementedException("TODO");
    }
    */
}