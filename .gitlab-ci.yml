stages:
  - dotnet_build
  - dotnet_test
  - build
  - push
  - deploy

.default_rules:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

default:
  image: gitlab.cartrack.com:5050/cartrack-base/cartrack-infrastructure-devops/cartrack-base-images/leap:15.6

include:
  - project: cartrack-base/cartrack-infrastructure-devops/cartrack-kubernetes/gitlab-shared-libraries
    file:
      - /shared-config/.gitlab-*********************.yml
      - /shared-config/.gitlab-**********************.yml
      - /dotnet/microservice/alerts/dev/.dotnet-build.yml
      - /dotnet/microservice/alerts/dev/.dotnet-test.yml

dotnet_build:
  tags:
    - test-nuget
  rules:
    - !reference [.default_rules, rules]
  stage: dotnet_build
  script:
    - cd source/Cartrack.Fleet.Host
    - !reference [.dotnet-build, script]

dotnet_test:
  tags:
    - test-nuget
  rules:
    - !reference [.default_rules, rules]
  needs:
    [dotnet_build]
  stage: dotnet_test
  script:
    - cd source/Cartrack.Fleet.Host
    - !reference [.dotnet-test, script]

build_image_as:
  stage: build
  tags:
    - test-nuget
  image: docker:latest
  services:
    - docker:dind
  variables:
    GIT_SSH_COMMAND: 'ssh -o StrictHostKeyChecking=no'
  before_script:
    - git submodule sync --recursive
    - git submodule update --init --recursive
  script:
    - !reference [.gitlab-*********************, script]
    - pwd
    - cd source
    - docker build . -t as-docker-registry.cartrack.com/cartrack.tfms.services/api:$CI_COMMIT_TAG -f Cartrack.Fleet.Host/Dockerfile
    - !reference [.gitlab-**********************, script]
    - echo $REGISTRY_PASSWORD | docker login as-docker-registry.cartrack.com -u $REGISTRY_USER --password-stdin
    - docker push as-docker-registry.cartrack.com/cartrack.tfms.services/api:$CI_COMMIT_TAG
  only:
    - tags
     
deploy-dev:
  stage: deploy
  image: ictu/sshpass
  script:
    - cd source/Cartrack.Fleet.Host
    - sed -i 's/TAG_PLACEHOLDER/'"$CI_COMMIT_TAG"'/' deployment/docker-compose-dev.yaml
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "mkdir -p -m 777 /opt/tfms_services_api/logs"
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "mkdir -p -m 777 /opt/tfms_services_api/certs"
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "mkdir -p -m 777 /opt/tfms_services_api/trace/clickhouse/data"
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "mkdir -p -m 777 /opt/tfms_services_api/trace/clickhouse/logs"
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "mkdir -p -m 777 /opt/tfms_services_api/trace/clickhouse/users"
    - sshpass -p $password scp -r -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no deployment/default-user.xml <EMAIL>:/opt/tfms_services_api/trace/clickhouse/users/default-user.xml
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "mkdir -p -m 777 /opt/tfms_services_api/trace/apphost-trace/logs"
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "mkdir -p -m 777 /opt/tfms_services_api/trace/apphost-trace/db"
    - sshpass -p $password scp -r -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=<NAME_EMAIL>:/opt/tfms_services_api/
    - sshpass -p $password scp -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no deployment/docker-compose-dev.yaml <EMAIL>:/opt/tfms_services_api/docker-compose.yaml
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "echo $REGISTRY_PASSWORD | docker login as-docker-registry.cartrack.com -u $REGISTRY_USER --password-stdin"
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "docker-compose -f /opt/tfms_services_api/docker-compose.yaml up -d"
  only:
    - tags
  when: manual
  tags:
    - as_runner 

deploy-db:
  stage: deploy
  image: ictu/sshpass
  script:
    - cd tests/DatabaseRefreshBuild/
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "rm -rf /storage/gitlab/tfms_qa_db"
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "mkdir -p -m 755 /storage/gitlab/tfms_qa_db/db-compressed/"
    - sshpass -p $password scp -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no -r ./* <EMAIL>:/storage/gitlab/tfms_qa_db/
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "cd /storage/gitlab/tfms_qa_db/db-compressed/ && ln -s /storage/tfms_qa_db/build/db-compressed/pg_data.7z pg_data.7z"
    - sshpass -p $password ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no <EMAIL> "cd /storage/gitlab/tfms_qa_db/ && sh deploy.sh"
  when: manual
  tags:
    - as_runner 