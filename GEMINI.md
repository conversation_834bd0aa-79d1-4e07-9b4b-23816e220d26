# Gemini Code Assistant Workspace Configuration

This document provides guidance for the Gemini Code Assistant on how to effectively interact with the `cartrack.fleet.services` project.

## Project Overview

This project is a .NET-based **Modular Monolith**. The solution file is located at `source/Cartrack.Fleet.Services.sln`. The application is composed of several distinct modules (e.g., `Accessory`, `Audit`, `User`, `Vehicle`) that are developed as independent projects but are deployed together as a single service.

The `Cartrack.Fleet.Host` project is the main entry point that assembles the various modules into a single ASP.NET Core web application.

## Architectural Principles

The solution follows the **Modular Monolith** architecture. This approach combines the development-time benefits of microservices (separation of concerns, independent modules) with the operational simplicity of a monolith (single deployment unit).

-   **High Cohesion:** Each module is responsible for a specific business domain (e.g., `Cartrack.Fleet.User` handles user-related concerns).
-   **Low Coupling:** Modules are designed to be independent and communicate through well-defined interfaces or a shared mediator (in this case, MediatR). Direct dependencies between modules are minimized.
-   **Unified Deployment:** All modules are compiled and deployed as part of a single host application (`Cartrack.Fleet.Host`).

## Module Structure

The modules (e.g., `Cartrack.Fleet.Accessory`, `Cartrack.Fleet.User`) share a consistent internal structure, indicating a domain-centric design approach:

-   `{Module}Startup.cs`: The entry point for the module, containing a `Register` method to add the module's services (controllers, repositories, etc.) to the main application's dependency injection container.
-   `Domain/`: Contains the core business logic and entities for the module. These are plain C# objects that are persistence-ignorant.
-   `Features/`: Implements the application's use cases, likely following Command Query Responsibility Segregation (CQRS) principles. Each feature (e.g., `CreateAccessory`, `GetUser`) is handled in its own folder.
-   `IO/`: (Input/Output) Contains adapters for external concerns.
    -   `Http/`: Defines the API controllers that expose the module's features over HTTP.
    -   `Sql/`: Contains the data access logic (Repositories) for interacting with the database.
-   `Infrastructure/`: (Optional) Contains implementations of interfaces defined in the domain, such as services for sending emails or other external interactions.

## Web Application Setup

The project is set up as an ASP.NET Core web application, orchestrated by the `Cartrack.Fleet.Host` project.

1.  **Entry Point**: Execution starts in `Program.cs`, which creates and runs an instance of `FleetHost`.
2.  **Host Configuration**: `FleetHost.cs` is responsible for configuring the web application. It sets up the Kestrel web server, configures CORS, enables Swagger for API documentation, and sets up middleware.
3.  **Module Registration**: The key step is the dynamic registration of modules. `FleetHost` reads application settings to determine which services are enabled (e.g., `IsUserServiceEnabled`). For each enabled service, it calls the corresponding `Register` method from the module's `*Startup.cs` file (e.g., `UserStartup.Register(builder.Services, appSetting)`).
4.  **Dependency Injection**: Each module's `Register` method adds its specific components (controllers, repositories, MediatR handlers) to the application's service container. This allows the `Host` to discover and use the controllers from all registered modules.
5.  **Request Pipeline**: `FleetHost` configures the HTTP request pipeline, including authentication, authorization, and logging middleware. It then maps the controllers, making the API endpoints from all registered modules available.

## Build and Test Commands

The GitLab CI/CD configuration (`.gitlab-ci.yml`) defines the build and test processes. The following commands can be used to build and test the project from the root directory:

**Build the project:**

```bash
dotnet build source/Cartrack.Fleet.Services.sln
```

**Run tests:**

```bash
dotnet test source/Cartrack.Fleet.Services.sln
```

These commands should be executed from the root of the project directory.