﻿using Microsoft.EntityFrameworkCore.Query;
using System.Linq.Expressions;

internal class TestAsyncQueryProvider<TEntity> : IAsyncQueryProvider {
    private readonly IQueryProvider _inner;

    internal TestAsyncQueryProvider(IQueryProvider inner) {
        this._inner = inner;
    }

    public IQueryable CreateQuery(Expression expression) {
        return new TestAsyncEnumerable<TEntity>(expression);
    }

    public IQueryable<TElement> CreateQuery<TElement>(Expression expression) {
        return new TestAsyncEnumerable<TElement>(expression);
    }

    public object Execute(Expression expression) {
        return this._inner.Execute(expression) ?? throw new InvalidOperationException();
    }

    public TResult Execute<TResult>(Expression expression) {
        return this._inner.Execute<TResult>(expression);
    }

    // public IAsyncEnumerable<TResult> ExecuteAsync<TResult>(Expression expression) {
    //     return new TestAsyncEnumerable<TResult>(expression);
    // }

    public TResult ExecuteAsync<TResult>(Expression expression, CancellationToken cancellationToken) {
        return this.Execute<TResult>(expression);
    }
}