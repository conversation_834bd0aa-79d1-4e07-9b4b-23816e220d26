﻿internal class TestAsyncEnumerator<T> : IAsyncEnumerator<T> {
    private readonly IEnumerator<T> _inner;

    public TestAsyncEnumerator(IEnumerator<T> inner) {
        this._inner = inner;
    }

    public void Dispose() {
        this._inner.Dispose();
    }

    public ValueTask<bool> MoveNextAsync() {
        return ValueTask.FromResult(this._inner.MoveNext());
    }

    public T Current {
        get {
            return this._inner.Current;
        }
    }

    public Task<bool> MoveNext(CancellationToken cancellationToken) {
        return Task.FromResult(this._inner.MoveNext());
    }

    public ValueTask DisposeAsync() {
        return ValueTask.CompletedTask;
    }
}