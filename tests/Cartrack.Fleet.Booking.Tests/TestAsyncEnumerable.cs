﻿// Cartrack.Fleet.Booking/Tests/ScdfVehicleBookingBuilderTests.cs

using Cartrack.Fleet.Booking.IO.Sql;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Cartrack.Fleet.Booking.Tests
{
}

internal class TestAsyncEnumerable<T> : EnumerableQuery<T>, IAsyncEnumerable<T>, IQueryable<T> {
    public TestAsyncEnumerable(IEnumerable<T> enumerable)
        : base(enumerable) {
    }

    public TestAsyncEnumerable(Expression expression)
        : base(expression) {
    }

    public IAsyncEnumerator<T> GetEnumerator() {
        return new TestAsyncEnumerator<T>(this.AsEnumerable().GetEnumerator());
    }

    IQueryProvider IQueryable.Provider {
        get { return new TestAsyncQueryProvider<T>(this); }
    }

    public IAsyncEnumerator<T> GetAsyncEnumerator(CancellationToken cancellationToken = new CancellationToken()) {
        return this.GetEnumerator();
    }
}