﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Features.GetBooking.Scdf;
using Cartrack.Fleet.Booking.IO.Http.Filters;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Domain.Permissions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System.Security.Claims;
using Constants = Cartrack.Fleet.Common.Constants;

namespace Cartrack.Fleet.Booking.Tests.Scdf;

[TestFixture]
public class ScdfGetBookingsTest {
    [SetUp]
    public void Setup() {
        var testData = new BookingTestData();
        this._testData = testData;
    }

    private BookingTestData? _testData;

    [Test]
    public async Task Can_GetBooking_When_BookingId_Is_Valid() {
        //Create 5 bookings
        this._testData!.Create(5, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
        var context = this._testData.CreateHttpContextAccessor(new EFCore.Models.Fleet.ClientUser() { UserId = 1234, UserName = "TEST00001", ClientUserId = Guid.NewGuid().ToString() });
        var repo = this.CreateScdfBookingRepository(builder, context);
        var logger = new Mock<ILogger<ScdfGetBookingHandler>>();

        foreach (var b in this._testData.MockPoolDbContext.Object.Bookings) {
            var bookingId = b.BookingId;
            var clientUserId = b.RequestClientUserId!;

            var handler = new ScdfGetBookingHandler(repo, this._testData.MockHttpContextAccessor.Object, logger.Object);
            var request = new ScdfGetBookingRequest(bookingId) { UserId = this._testData.UserId, Account = this._testData.Account, ClientUserId = clientUserId };
            var resp = await handler.Handle(request, CancellationToken.None);

            Assert.That(resp, Is.Not.Null);
            Assert.That(resp.Error, Is.Null);
            Assert.That(resp.IsOk, Is.True);
            Assert.That(resp.Value, Is.Not.Null);
            Assert.That(resp.Value!.Id, Is.EqualTo(bookingId));
            Assert.That(resp.Value!.RequestClientUserId, Is.EqualTo(clientUserId));
            Assert.That(resp.Value!.BookingPurposeId, Is.EqualTo(b.BookingPurposeId));
            Assert.That(resp.Value!.VehicleId, Is.EqualTo(b.VehicleId));
            Assert.That(resp.Value!.Accessories.Length, Is.EqualTo(1));

            var a = this._testData.MockPoolDbContext.Object.Accessories.FirstOrDefault(t => t.Id == resp.Value!.Accessories[0].Id);
            Assert.That(a, Is.Not.Null);
        }
    }

    [Test]
    public async Task Fail_When_BookingId_Is_Invalid() {
        this._testData!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
        var context = this._testData.CreateHttpContextAccessor(new EFCore.Models.Fleet.ClientUser() { UserId = 1234, UserName = "TEST00001", ClientUserId = Guid.NewGuid().ToString() });
        var repo = this.CreateScdfBookingRepository(builder, context);
        var logger = new Mock<ILogger<ScdfGetBookingHandler>>();

        var handler = new ScdfGetBookingHandler(repo, this._testData.MockHttpContextAccessor.Object, logger.Object);
        var request = new ScdfGetBookingRequest(-1) { UserId = this._testData.UserId, Account = this._testData.Account, ClientUserId = "" };
        var resp = await handler.Handle(request, CancellationToken.None);

        Assert.That(resp.IsOk, Is.False);
        Assert.That(resp.Error, Is.Not.Null);
        Assert.That(resp.Error, Is.TypeOf(typeof(RequiresException)));
    }

    [Test]
    public async Task Fail_When_BookingId_Is_Missing() {
        this._testData!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
        var context = this._testData.CreateHttpContextAccessor(new EFCore.Models.Fleet.ClientUser() { UserId = 1234, UserName = "TEST00001", ClientUserId = Guid.NewGuid().ToString() });
        var repo = this.CreateScdfBookingRepository(builder, context);
        var logger = new Mock<ILogger<ScdfGetBookingHandler>>();

        var missingBookingId = 9999;
        var handler = new ScdfGetBookingHandler(repo, this._testData.MockHttpContextAccessor.Object, logger.Object);
        var request = new ScdfGetBookingRequest(missingBookingId) { UserId = this._testData.UserId, Account = this._testData.Account, ClientUserId = "" };
        var resp = await handler.Handle(request, CancellationToken.None);

        Assert.That(resp.IsOk, Is.False);
        Assert.That(resp.Value, Is.Null);
        Assert.That(resp.Error, Is.Not.Null);
        Assert.That(resp.Error, Is.TypeOf(typeof(NotFoundException)));
    }

    private ScdfBookingRepository CreateScdfBookingRepository(ScdfVehicleBookingBuilder builder, IHttpContextAccessor context) {
        var repo = new ScdfBookingRepository(
            this._testData!.MockAppSettings.Object,
            this._testData.MockTfmsCustomDbContext.Object,
            this._testData.MockFleetDbContext.Object,
            this._testData.MockCtDbContext.Object,
            this._testData.MockPoolDbContext.Object,
            this._testData.MockMinIo.Object,
            this._testData.MockBookingFilterService.Object,
            context,
            _ => builder,
            new Mock<ILogger<ScdfBookingRepository>>().Object
        );
        return repo;
    }

    private ScdfVehicleBookingBuilder CreateScdfVehicleBookingBuilder() {
        var builder = new ScdfVehicleBookingBuilder(
            this._testData!.MockAppSettings.Object,
            this._testData.MockMediator.Object,
            this._testData.MockDriverRepo.Object,
            this._testData.MockCtDbContext.Object,
            this._testData.MockFleetDbContext.Object,
            this._testData.MockPoolDbContext.Object,
            this._testData.MockHttpContextAccessor.Object,
            this._testData.MockTfmsCustomDbContext.Object
        ) { CreateAppFleetDb = _ => this._testData.MockFleetDbContext.Object };
        return builder;
    }



}