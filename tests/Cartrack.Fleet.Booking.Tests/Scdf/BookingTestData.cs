﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.EFCore.Models.Pool;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Booking.IO.Http.Filters;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.User.Domain.Permissions;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Minio;
using Moq;
using System.Security.Claims;
using BookingAccessory = Cartrack.EFCore.Models.Pool.BookingAccessory;
using BookingApproval = Cartrack.EFCore.Models.Pool.BookingApproval;
using ClientUser = Cartrack.EFCore.Models.Fleet.ClientUser;

namespace Cartrack.Fleet.Booking.Tests.Scdf;

public class BookingTestData {
    private readonly List<Accessory> _mockAccessories;
    private readonly List<ScdfBookingAdditionalInfo> _mockAdditionalInfo;
    private readonly List<BookingApproval> _mockApprovals;
    private readonly List<BookingAccessory> _mockBookingAccessories;
    private readonly List<BookingPurpose> _mockBookingPurposes;
    private readonly List<EFCore.Models.Pool.Booking> _mockBookings;
    private readonly List<BookingVehicleType> _mockCategories;
    private readonly List<ClientDriver> _mockClientDriver;
    private readonly List<ClientUser> _mockClientUsers;
    private readonly List<BookingJourney> _mockJourneys;
    private readonly List<SiteLocation> _mockSiteLocations;
    private readonly List<EFCore.Models.CT.User> _mockUsers;
    private readonly List<VehicleAdditionalInfo> _mockVehicleAdditionInfo;
    private readonly List<EFCore.Models.CT.Vehicle> _mockVehicles;
    private readonly List<UserBookingRule> _mockRules;

    public BookingTestData() {
        this.MockAppSettings = new Mock<AppSettings>(() => new AppSettings(new MockEnvVars()));
        this.MockMediator = new Mock<IMediator>();
        this.MockDriverRepo = new Mock<IDriverRepository>();
        this.MockRuleRepo = new Mock<IBookingRuleRepository>();
        this.MockCtDbContext = new Mock<AppCtDbContext>(() => new AppCtDbContext(this.MockAppSettings.Object));
        this.MockFleetDbContext = new Mock<AppFleetDbContext>(() => new AppFleetDbContext(this.MockAppSettings.Object.ConnectionString));
        this.MockPoolDbContext = new Mock<AppPoolDbContext>(() => new AppPoolDbContext(this.MockAppSettings.Object.ConnectionString));
        this.MockTfmsCustomDbContext = new Mock<AppTfmsCustomDbContext>(() => new AppTfmsCustomDbContext(this.MockAppSettings.Object.ConnectionString));
        this.MockHttpContextAccessor = new Mock<IHttpContextAccessor>();       
        this.MockMinIo = new Mock<IMinioClient>();
        this.MockBookingFilterService = new Mock<IBookingFilterService>();
        this.UserId = 12345L;
        this.Account = "TEST00001";

        this._mockApprovals = new List<BookingApproval>();
        this._mockBookings = new List<EFCore.Models.Pool.Booking>();
        this._mockBookingPurposes = new List<BookingPurpose>();
        this._mockCategories = new List<BookingVehicleType>();
        this._mockBookingAccessories = new List<BookingAccessory>();
        this._mockAccessories = new List<Accessory>();
        this._mockSiteLocations = new List<SiteLocation>();
        this._mockClientUsers = new List<ClientUser>();
        this._mockVehicles = new List<EFCore.Models.CT.Vehicle>();
        this._mockUsers = new List<EFCore.Models.CT.User> { new() { UserId = this.UserId, UserName = this.Account } };
        this._mockAdditionalInfo = new List<ScdfBookingAdditionalInfo>();
        this._mockJourneys = new List<BookingJourney>();
        this._mockClientDriver = new List<ClientDriver>();
        this._mockVehicleAdditionInfo = new List<VehicleAdditionalInfo>();
        this._mockRules = new List<UserBookingRule>();
        this.MockEnv = new MockEnvVars();
    }

    public MockEnvVars MockEnv { get;  }

    public Mock<IHttpContextAccessor> MockHttpContextAccessor { get; set; }

    public Mock<AppSettings> MockAppSettings { get; }
    public Mock<IMediator> MockMediator { get; }
    public Mock<IDriverRepository> MockDriverRepo { get; }
    
    public Mock<AppCtDbContext> MockCtDbContext { get; }
    public Mock<AppFleetDbContext> MockFleetDbContext { get; }
    public Mock<AppPoolDbContext> MockPoolDbContext { get; }
    public Mock<AppTfmsCustomDbContext> MockTfmsCustomDbContext { get; }
    public Mock<IMinioClient> MockMinIo { get; }
    public Mock<IBookingFilterService> MockBookingFilterService { get; }
    public Mock<IBookingRuleRepository> MockRuleRepo { get; }

    public string Account { get; set; }

    public long UserId { get; set; }

    public List<EFCore.Models.Pool.Booking> Create(int count, DateTime bookingStart) {
        for (var i = 1; i <= count; i++) {
            var bookingPurposeId = i;
            this.AddBookingPurpose(bookingPurposeId);
            var bookingVehicleTypeId = i;
            this.AddBookingVehicleType(bookingVehicleTypeId);
            var clientUserId = Guid.NewGuid().ToString();
            this.AddClientUser(clientUserId, i);
            var clientDriverId = Guid.NewGuid().ToString();
            this.AddClientDriver(clientUserId, clientDriverId, i);
            var vehicleId = i;
            this.AddVehicle(vehicleId);
            var vehicleAdditionalInfoId = i;
            var defaultSiteLocationId = i;
            this.AddVehicleAdditionalInfo(vehicleId, defaultSiteLocationId);
            var bookingId = i;
            var bookingEnd = bookingStart.AddHours(1);
            this.AddBooking(bookingStart, bookingId, bookingPurposeId, bookingVehicleTypeId, i, clientUserId, clientDriverId, bookingEnd, vehicleId);
            var accessoryId = i;
            this.AddAccessory(bookingId, accessoryId);
            var siteLocationId = i;
            var siteLocation = new SiteLocation { SiteLocationId = siteLocationId, SiteLocationName = "Location" + siteLocationId };
            this._mockSiteLocations.Add(siteLocation);
            var additionInfoId = i;
            this.AddAdditionalInfo(additionInfoId, bookingId);
            this.AddJourney(bookingStart, bookingId, bookingEnd, siteLocation);
            bookingStart = bookingEnd.AddMinutes(1);
        }

        this.SetupDbMocks();
        return this._mockBookings;
    }

    public void AddBookingRules() {
        this._mockRules.Add(new UserBookingRule() { UserBookingRuleId = 1, BookingRuleId = 103, Value = "0", Unit = "bool", Status = true });
        this._mockRules.Add(new UserBookingRule() { UserBookingRuleId = 1, BookingRuleId = 104, Value = "0", Unit = "bool", Status = true });
        this._mockRules.Add(new UserBookingRule() { UserBookingRuleId = 1, BookingRuleId = 101, Value = "14", Unit = "days", Status = true });
        this._mockRules.Add(new UserBookingRule() { UserBookingRuleId = 1, BookingRuleId = 102, Value = "12", Unit = "hours", Status = true });
    }
    public void SetupDbMocks() {
        this.MockPoolDbContext.Setup(db => db.Bookings).Returns(MockDbSet(this._mockBookings).Object);
        this.MockPoolDbContext.Setup(db => db.BookingApprovals).Returns(MockDbSet(this._mockApprovals).Object);
        this.MockPoolDbContext.Setup(db => db.BookingPurposes).Returns(MockDbSet(this._mockBookingPurposes).Object);
        this.MockPoolDbContext.Setup(db => db.BookingVehicleTypes).Returns(MockDbSet(this._mockCategories).Object);
        this.MockPoolDbContext.Setup(db => db.BookingJourneys).Returns(MockDbSet(this._mockJourneys).Object);
        this.MockPoolDbContext.Setup(db => db.BookingAccessories).Returns(MockDbSet(this._mockBookingAccessories).Object);
        this.MockPoolDbContext.Setup(db => db.Accessories).Returns(MockDbSet(this._mockAccessories).Object);
        this.MockPoolDbContext.Setup(db => db.UserBookingRules).Returns(MockDbSet(this._mockRules).Object);

        // ----
        // Mock FLEET DB Context
        // ----
        //var this._mockLocations = new List<SiteLocation>() { new SiteLocation() { SiteLocationId = siteLocationId, SiteLocationName = "Location1" } };
        this.MockFleetDbContext.Setup(db => db.SiteLocations).Returns(MockDbSet(this._mockSiteLocations).Object);
        this.MockFleetDbContext.Setup(db => db.ClientUsers).Returns(MockDbSet(this._mockClientUsers).Object);
        this.MockFleetDbContext.Setup(db => db.VehicleAdditionalInfos).Returns(MockDbSet(this._mockVehicleAdditionInfo).Object);
        this.MockFleetDbContext.Setup(db => db.ClientDrivers).Returns(MockDbSet(this._mockClientDriver).Object);

        // ----
        // Mock CT DB Context
        // ----
        this.MockCtDbContext.Setup(db => db.Vehicles).Returns(MockDbSet(this._mockVehicles).Object);
        this.MockCtDbContext.Setup(db => db.Users).Returns(MockDbSet(this._mockUsers).Object);

        // ----
        // Mock TFMSCUSTOM DB Context
        // ----
        this.MockTfmsCustomDbContext.Setup(db => db.ScdfBookingAdditionalInfos).Returns(MockDbSet(this._mockAdditionalInfo).Object);
    }

    public void AddJourney(DateTime bookingStart, int bookingId, DateTime bookingEnd, SiteLocation siteLocation) {
        var journey = new BookingJourney {
            BookingId = bookingId,
            Id = 1,
            StartTs = bookingStart,
            EndTs = bookingEnd,
            Location = siteLocation.SiteLocationName
        };
        this._mockJourneys.Add(journey);
    }

    public void AddAdditionalInfo(int additionInfoId, int bookingId) {
        var additionalInfo = new ScdfBookingAdditionalInfo { Id = additionInfoId, BookingId = bookingId, PassengerCount = 100 };
        this._mockAdditionalInfo.Add(additionalInfo);
    }

    public void AddAccessory(int bookingId, int accessoryId) {
        var bookingAccessory = new BookingAccessory { BookingId = bookingId, AccessoryId = accessoryId };
        this._mockBookingAccessories.Add(bookingAccessory);
        var accessory = new Accessory { Id = accessoryId, Name = "Accessory" + accessoryId, BookingAccessories = [bookingAccessory], AccessoryTypeId = accessoryId };
        this._mockAccessories.Add(accessory);
    }

    public EFCore.Models.Pool.Booking AddBooking(DateTime bookingStart, int bookingId, int bookingPurposeId, int bookingVehicleTypeId, int i, string clientUserId, string clientDriverId, DateTime bookingEnd,
        int vehicleId) {
        var booking = new EFCore.Models.Pool.Booking {
            BookingId = bookingId,
            BookingPurposeId = bookingPurposeId,
            BookingVehicleTypeId = bookingVehicleTypeId,
            PickupSiteLocationId = i,
            RequestClientUserId = clientUserId,
            RequestClientDriverId = clientDriverId,
            StartTs = bookingStart,
            EndTs = bookingEnd,
            UserId = this.UserId,
            VehicleId = vehicleId,
            BookingStatusId = (int)BookingStatusCode.Requested
        };
        this._mockBookings.Add(booking);
        return booking;
    }

    public VehicleAdditionalInfo AddVehicleAdditionalInfo(int vehicleId, int defaultSiteLocationId) {
        var vehicleAdditionalInfo = new VehicleAdditionalInfo { VehicleId = vehicleId, CommonPool = true, DefaultSiteLocationId = defaultSiteLocationId, IsPoolActive = true };
        this._mockVehicleAdditionInfo.Add(vehicleAdditionalInfo);
        return vehicleAdditionalInfo;
    }
    
    public void AddApproval(long bookingId, bool isApproved, string unitManagerId) {
        var approval = new BookingApproval() { BookingId = bookingId, IsApproved = isApproved, UnitManagerId = unitManagerId };
        this._mockApprovals.Add(approval);
    }

    public EFCore.Models.CT.Vehicle AddVehicle(int vehicleId) {
        var vehicle = new EFCore.Models.CT.Vehicle { VehicleId = vehicleId, Registration = "TESTREG" + vehicleId };
        this._mockVehicles.Add(vehicle);
        return vehicle;
    }

    public ClientDriver AddClientDriver(string clientUserId, string clientDriverId, int i=1) {
        var clientDriver = new ClientDriver { ClientUserId = clientUserId, ClientDriverId = clientDriverId, DriverName = "DriverName" + i, EMail = "driver" + i + "@test.com" };
        this._mockClientDriver.Add(clientDriver);
        return clientDriver;
    }

    public ClientUser AddClientUser(string clientUserId, int i=1) {
        var clientUsr = new ClientUser { ClientUserId = clientUserId, UserName = "clientUserName" + i, EMail = "clientUserName" + i + "@test.com" };
        this._mockClientUsers.Add(clientUsr);
        return clientUsr;
    }

    public BookingVehicleType AddBookingVehicleType(int bookingVehicleTypeId) {
        var bookingVehicleType = new BookingVehicleType { BookingVehicleTypeId = bookingVehicleTypeId, BookingVehicleType1 = "FastCar" + bookingVehicleTypeId };
        this._mockCategories.Add(bookingVehicleType);
        return bookingVehicleType;
    }

    public BookingPurpose AddBookingPurpose(int bookingPurposeId) {
        var bookingPurpose = new BookingPurpose { BookingPurposeId = bookingPurposeId, BookingPurpose1 = "Purpose" + bookingPurposeId };
        this._mockBookingPurposes.Add(bookingPurpose);
        return bookingPurpose;
    }
    
    public IHttpContextAccessor CreateHttpContextAccessor(EFCore.Models.Fleet.ClientUser clientUser) {
        var mockHttpContext = new Mock<HttpContext>();
        var authClaims = new AuthClaims(this.Account, this.UserId, clientUser.UserName, clientUser.ClientUserId, false);
        var bookingSettings = new BookingSettings {
            IsAutoApprovalEnabled = false,
            ActivationType = "Manual",
            BookInAdvanceBy = TimeSpan.FromDays(7),
            CheckDriverLicenseClass = true,
            CheckDriverSpecialLicense = true,
            IsDriverRequired = true,
            IsKeyCollectionEnabled = false,
            IsMultiLevelApprovalEnabled = false,
            MaximumBookingTime = TimeSpan.FromHours(12),
            PreDriveChecklistAvailableBy = TimeSpan.Zero
        };

        var principalMock = new Mock<ClaimsPrincipal>();
        var claims = new List<Claim>();
        claims.Add(new Claim("Account", authClaims.Account));
        claims.Add(new Claim("AccountId", authClaims.UserId.ToString()));
        claims.Add(new Claim(ClaimTypes.Name, authClaims.UserName));
        claims.Add(new Claim(ClaimTypes.NameIdentifier, authClaims.ClientUserId));
        
        principalMock.Setup(x => x.Claims).Returns(claims);
        
        var bookingContext = new BookingContext(authClaims, bookingSettings, SetOfPermissions.FullAccess());
        var items = new Dictionary<object, object>();
        items.Add(Booking.Domain.Common.Constants.BookingContext, bookingContext);
        mockHttpContext.Setup(c => c.Items).Returns(items);
        mockHttpContext.Setup(c => c.User).Returns(principalMock.Object);
        
        var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
        mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(mockHttpContext.Object);
        return mockHttpContextAccessor.Object;
    }

    private static Mock<DbSet<T>> MockDbSet<T>(List<T> list) where T : class {
        var foo = new TestAsyncEnumerable<T>(list).AsQueryable();
        var dbSet = new Mock<DbSet<T>>();
        dbSet.As<IQueryable<T>>().Setup(m => m.Provider).Returns(foo.Provider);
        dbSet.As<IQueryable<T>>().Setup(m => m.Expression).Returns(foo.Expression);
        dbSet.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(foo.ElementType);
        dbSet.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(() => foo.GetEnumerator());
        return dbSet;
    }
}