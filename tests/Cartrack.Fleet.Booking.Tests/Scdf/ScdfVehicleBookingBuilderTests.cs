﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Features.GetBooking.Scdf;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;

namespace Cartrack.Fleet.Booking.Tests.Scdf;

[TestFixture]
public class ScdfVehicleBookingBuilderTests {
    [SetUp]
    public void Setup() {
        var testData = new BookingTestData();
        this._testBookings = testData;
    }

    private BookingTestData? _testBookings;

    [Test]
    public async Task Vehicle_Should_Be_Created_When_VehicleId_Is_Valid() {
        //Create 5 bookings
        this._testBookings!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
         
        var bookingId = 1;
        var vehicleBooking = await builder.Build(bookingId);
        Assert.That(vehicleBooking, Is.Not.Null);
        Assert.That(vehicleBooking!.Vehicle, Is.Not.Null);
        Assert.That(vehicleBooking.VehicleId, Is.GreaterThan(0));
        Assert.That(vehicleBooking.VehicleId, Is.EqualTo(vehicleBooking.Vehicle.VehicleId));
        Assert.That(vehicleBooking.Vehicle.Registration, Is.Not.Empty);
    }
    
    [Test]
    public async Task VehicleCategory_Should_Be_Created_When_VehicleTypeId_Is_Valid() {
        //Create 5 bookings
        this._testBookings!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
        
        var bookingId = 1;
        var vehicleBooking = await builder.Build(bookingId);
        Assert.That(vehicleBooking, Is.Not.Null);
        Assert.That(vehicleBooking!.Vehicle, Is.Not.Null);
        Assert.That(vehicleBooking.VehicleCategory, Is.Not.Null);
        Assert.That(vehicleBooking.VehicleCategory.Id, Is.EqualTo(vehicleBooking.BookingVehicleTypeId));
        Assert.That(vehicleBooking.VehicleCategory.Name, Is.Not.Empty);
    }
    [Test]
    public async Task Purpose_Should_Be_Created_When_BookingPurposeId_Is_Valid() {
        //Create 5 bookings
        this._testBookings!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
        
        var bookingId = 1;
        var vehicleBooking = await builder.Build(bookingId);
        Assert.That(vehicleBooking, Is.Not.Null);
        Assert.That(vehicleBooking!.Purpose, Is.Not.Null);
        Assert.That(vehicleBooking.Purpose.Id, Is.EqualTo(vehicleBooking.BookingPurposeId));
        Assert.That(vehicleBooking.Purpose.Title, Is.Not.Empty);
    }
    
    [Test]
    public async Task Driver_Should_Be_Created_When_ClientDriverId_Is_Valid() {
        this._testBookings!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
         
        var bookingId = 1;
        var vehicleBooking = await builder.Build(bookingId);
        Assert.That(vehicleBooking, Is.Not.Null);
        Assert.That(vehicleBooking!.Driver, Is.Not.Null);
        Assert.That(vehicleBooking.RequestClientDriverId, Is.EqualTo(vehicleBooking.Driver!.DriverId));
    }
    
    [Test]
    public async Task Journey_Should_Be_Created() {
        this._testBookings!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
         
        var bookingId = 1;
        var vehicleBooking = await builder.Build(bookingId);
        Assert.That(vehicleBooking, Is.Not.Null);
        Assert.That(vehicleBooking!.Journeys, Is.Not.Null);
        Assert.That(vehicleBooking.Journeys.Count, Is.EqualTo(1));
    }
    
    [Test]
    public async Task Accessories_Should_Be_Created() {
        this._testBookings!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
         
        var bookingId = 1;
        var vehicleBooking = await builder.Build(bookingId);
        Assert.That(vehicleBooking, Is.Not.Null);
        Assert.That(vehicleBooking!.DetailedAccessories, Is.Not.Null);
        Assert.That(vehicleBooking.DetailedAccessories.Count, Is.EqualTo(1));
        Assert.That(vehicleBooking!.DetailedAccessories[0].TypeId, Is.EqualTo(1));
        Assert.That(vehicleBooking!.DetailedAccessories[0].Id, Is.EqualTo(1));
    }
    
    [Test]
    public async Task PickupLocation_Should_Be_Created_When_PickupSiteLocationId_Is_Valid() {
        this._testBookings!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
        
        var bookingId = 1;
        var vehicleBooking = await builder.Build(bookingId);
        Assert.That(vehicleBooking, Is.Not.Null);
        Assert.That(vehicleBooking!.PickupLocation, Is.Not.Null);
        Assert.That(vehicleBooking!.PickupLocation.Name, Is.Not.Empty);
        Assert.That(vehicleBooking.PickupSiteLocationId, Is.EqualTo(vehicleBooking.PickupLocation.Id));
    }
    
    [Test]
    public async Task RequestedFor_Should_Be_Created_When_ClientUserId_Is_Valid() {
        this._testBookings!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();

        var requestedForClientUserId = Guid.NewGuid().ToString();
        this._testBookings.AddClientUser(requestedForClientUserId, 1);
        
        var bookingId = 1;
        var vehicleBooking = await builder.Build(bookingId);
        await builder.IncludeRequestedFor(requestedForClientUserId);
        
        Assert.That(vehicleBooking, Is.Not.Null);
        Assert.That(vehicleBooking!.RequestedFor, Is.Not.Null);
        Assert.That(vehicleBooking.RequestedFor.ClientUserId, Is.EqualTo(requestedForClientUserId));
        Assert.That(vehicleBooking.RequestedForClientUserId, Is.EqualTo(vehicleBooking.RequestedFor.ClientUserId));
    }

    [Test]
    public async Task Approval_Should_Be_Created_When_Booking_Is_Approved() {
        var bookings = this._testBookings!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
        var bookingId = bookings[0].BookingId;
        
        var approverClientUserId = Guid.NewGuid().ToString();
        this._testBookings.AddClientUser(approverClientUserId, 1);
        this._testBookings.AddApproval(bookingId, true, approverClientUserId);
        bookings[0].IsApproved = true;
        
        var vehicleBooking = await builder.Build(bookingId);
        Assert.That(vehicleBooking, Is.Not.Null);
        Assert.That(vehicleBooking!.RejectedBy, Is.Empty);
        Assert.That(vehicleBooking!.ApprovedBy, Is.Not.Null);
        Assert.That(vehicleBooking!.ApprovedBy[0].ClientUserId, Is.EqualTo(approverClientUserId));
    }
    
    [Test]
    public async Task RejectedBy_Should_Be_Created_When_Booking_Is_Not_Approved() {
        var bookings = this._testBookings!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
        var bookingId = bookings[0].BookingId;
        
        var rejectedByClientUserId = Guid.NewGuid().ToString();
        this._testBookings.AddClientUser(rejectedByClientUserId, 1);
        this._testBookings.AddApproval(bookingId, false, rejectedByClientUserId);
        bookings[0].IsApproved = false;
        
        var vehicleBooking = await builder.Build(bookingId);
        Assert.That(vehicleBooking, Is.Not.Null);
        Assert.That(vehicleBooking!.ApprovedBy, Is.Empty);
        Assert.That(vehicleBooking!.RejectedBy, Is.Not.Null);
        Assert.That(vehicleBooking!.RejectedBy[0].ClientUserId, Is.EqualTo(rejectedByClientUserId));
    }
    
    [Test]
    public async Task RequestedBy_Should_Be_Created_When_ClientUserId_Is_Valid() {
        this._testBookings!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
        
        var bookingId = 1;
        var vehicleBooking = await builder.Build(bookingId);
        Assert.That(vehicleBooking, Is.Not.Null);
        Assert.That(vehicleBooking!.RequestedBy, Is.Not.Null);
        Assert.That(vehicleBooking!.RequestedBy.ClientUserId, Is.Not.Empty);
        Assert.That(vehicleBooking.RequestedBy.ClientUserId, Is.EqualTo(vehicleBooking.RequestClientUserId));
    }
    
    [Test]
    public async Task CancelledBy_Should_Be_Created_When_CancelledClientUserId_Is_Valid() {
        var bookings = this._testBookings!.Create(1, DateTime.Now);
        var builder = this.CreateScdfVehicleBookingBuilder();
        var bookingId = bookings[0].BookingId;

        var now = DateTime.UtcNow;
        var cancelledByUserId = Guid.NewGuid().ToString();
        var notes = $"Cancelled by {cancelledByUserId}";
        this._testBookings.AddClientUser(cancelledByUserId);
        bookings[0].CanceledClientUserId = cancelledByUserId;
        bookings[0].CanceledTs = now;
        bookings[0].BookingCancelNotes = notes;
        
        var vehicleBooking = await builder.Build(bookingId);
        await builder.IncludeCancellation();
        Assert.That(vehicleBooking, Is.Not.Null);
        Assert.That(vehicleBooking!.CancellationDate, Is.EqualTo(now));
        Assert.That(vehicleBooking!.CancellationNotes, Is.EqualTo(notes));
        Assert.That(vehicleBooking.CancelledBy, Is.Not.Null);
        Assert.That(vehicleBooking.CancelledBy.ClientUserId, Is.EqualTo(cancelledByUserId));
    }
    
    [Test]
    public async Task Default_BookingInstance_Is_Created_When_BookingId_Is_Zero() {
        var builder = this.CreateScdfVehicleBookingBuilder();
      
        var bookingId = 0;
        await builder.Start(bookingId);
        
        Assert.That(builder.VehicleBooking, Is.Not.Null);
        Assert.That(builder.VehicleBooking!.PickupLocation, Is.Not.Null);
        Assert.That(builder.VehicleBooking.PickupLocation.Name, Is.Empty);
        Assert.That(builder.VehicleBooking.VehicleCategory, Is.Not.Null);
        Assert.That(builder.VehicleBooking.VehicleCategory.Name, Is.Empty);
        Assert.That(builder.VehicleBooking.RequestedBy, Is.Not.Null);
        Assert.That(builder.VehicleBooking.RequestedBy.ClientUserId, Is.Empty);
        Assert.That(builder.VehicleBooking.RequestedFor, Is.Not.Null);
        Assert.That(builder.VehicleBooking.RequestedFor.ClientUserId, Is.Empty);
        Assert.That(builder.VehicleBooking.BookingType, Is.EqualTo(BookingType.Standard));
        Assert.That(builder.VehicleBooking.Vehicle, Is.Not.Null);
        Assert.That(builder.VehicleBooking.Vehicle.VehicleId, Is.Zero);
        Assert.That(builder.VehicleBooking.Vehicle.Registration, Is.Empty);
        Assert.That(builder.VehicleBooking.BookingId, Is.EqualTo(0));
    }
     
    private ScdfVehicleBookingBuilder CreateScdfVehicleBookingBuilder() {
        var builder = new ScdfVehicleBookingBuilder(
            this._testBookings!.MockAppSettings.Object,
            this._testBookings.MockMediator.Object,
            this._testBookings.MockDriverRepo.Object,
            this._testBookings.MockCtDbContext.Object,
            this._testBookings.MockFleetDbContext.Object,
            this._testBookings.MockPoolDbContext.Object,
            this._testBookings.MockHttpContextAccessor.Object,
            this._testBookings.MockTfmsCustomDbContext.Object
        ) { CreateAppFleetDb = _ => this._testBookings.MockFleetDbContext.Object };
        return builder;
    }
}