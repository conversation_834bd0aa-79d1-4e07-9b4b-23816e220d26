using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using Moq;
using NUnit.Framework;
using Xunit;
using FluentAssertions;
using System.Linq;
using System.Collections.Generic;

namespace Cartrack.Fleet.Booking.Tests.Scdf;

[TestFixture]
public class BookingFilterServiceTests {
    private readonly Mock<AppTfmsCustomDbContext> _mockTfmsCustomDbContext;
    private readonly Mock<AppPoolDbContext> _mockPoolDbContext;
    private readonly Mock<AppFleetDbContext> _mockFleetDbContext;
    private readonly Mock<AppCtDbContext> _mockCtDbContext;
    private readonly BookingFilterService _service;
    private readonly IQueryable<EFCore.Models.Pool.Booking> _testBookings;

    public BookingFilterServiceTests() {
        _mockTfmsCustomDbContext = new Mock<AppTfmsCustomDbContext>();
        _mockPoolDbContext = new Mock<AppPoolDbContext>();
        _mockFleetDbContext = new Mock<AppFleetDbContext>();
        _mockCtDbContext = new Mock<AppCtDbContext>();

        _service = new BookingFilterService(
            _mockTfmsCustomDbContext.Object,
            _mockPoolDbContext.Object,
            _mockFleetDbContext.Object,
            _mockCtDbContext.Object
        );

        // Setup test data
        _testBookings = CreateTestBookings();
    }

    [Fact]
    public void ApplyFilterItem_WithNullStringValue_ShouldReturnOriginalQuery() {
        // Arrange
        var filterItem = new SingleStringStringFilterItem {
            Value = null,
            ParsedField = StringFilterField.vehicle,
            StringOperator = StringOperators.contains,
            Case = FilterCaseSensitivity.insensitive
        };

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().BeSameAs(_testBookings);
    }

    [Fact]
    public void ApplyFilterItem_WithEmptyStringValue_ShouldReturnOriginalQuery() {
        // Arrange
        var filterItem = new SingleStringStringFilterItem {
            Value = "",
            ParsedField = StringFilterField.vehicle,
            StringOperator = StringOperators.contains,
            Case = FilterCaseSensitivity.insensitive
        };

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().BeSameAs(_testBookings);
    }

    [Fact]
    public void ApplyFilterItem_WithEmptyArrayValue_ShouldReturnOriginalQuery() {
        // Arrange
        var filterItem = new ArrayStringFilterItem {
            Value = new string[0],
            ParsedField = StringFilterField.vehicle,
            Case = FilterCaseSensitivity.insensitive
        };

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().BeSameAs(_testBookings);
    }

    [Fact]
    public void ApplyFilterItem_WithValidSingleStringFilter_ShouldCallApplySingleStringFilter() {
        // Arrange
        var filterItem = new SingleStringStringFilterItem {
            Value = "ABC123",
            ParsedField = StringFilterField.vehicle,
            StringOperator = StringOperators.contains,
            Case = FilterCaseSensitivity.insensitive
        };

        SetupVehicleMocks();

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().NotBeSameAs(_testBookings);
    }

    [Fact]
    public void ApplyFilterItem_WithValidArrayStringFilter_ShouldCallApplyArrayStringFilter() {
        // Arrange
        var filterItem = new ArrayStringFilterItem {
            Value = new[] {
                "ABC123", "XYZ789"
            },
            ParsedField = StringFilterField.vehicle,
            Case = FilterCaseSensitivity.insensitive
        };

        SetupVehicleMocks();

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().NotBeSameAs(_testBookings);
    }

    [Fact]
    public void ApplyFilterItem_WithEmptyStringFilter_ShouldCallApplyEmptyStringFilter() {
        // Arrange
        var filterItem = new EmptyStringFilterItem {
            EmptyOperator = EmptyOperators.isEmpty, ParsedField = StringFilterField.vehicle
        };

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().NotBeSameAs(_testBookings);
    }

    [Fact]
    public void ApplyFilterItem_WithDateRangeFilter_ShouldCallApplyDateRangeFilter() {
        // Arrange
        var filterItem = new DateRangeFilterItem {
            After = new DateRangeValue {
                Value = DateTime.UtcNow.AddDays(-30), Strategy = Strategy.inclusive
            },
            Before = new DateRangeValue {
                Value = DateTime.UtcNow, Strategy = Strategy.inclusive
            },
            ParsedField = DateRangeFilterField.requestDate,
            OperatorType = LogicOperator.And
        };

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().NotBeSameAs(_testBookings);
    }

    [Theory]
    [InlineData(StringOperators.contains, "ABC", true)]
    [InlineData(StringOperators.equals, "ABC123", true)]
    [InlineData(StringOperators.startsWith, "ABC", true)]
    [InlineData(StringOperators.endsWith, "123", true)]
    public void ApplyVehicleFilter_WithDifferentOperators_ShouldFilterCorrectly(
        StringOperators op, string value, bool isCaseSensitive) {
        // Arrange
        SetupVehicleMocks();
        var filterItem = new SingleStringStringFilterItem {
            Value = value,
            ParsedField = StringFilterField.vehicle,
            StringOperator = op,
            Case = isCaseSensitive ? FilterCaseSensitivity.sensitive : FilterCaseSensitivity.insensitive
        };

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().NotBeNull();
    }

    [Fact]
    public void ApplyEmptyStringFilter_WithIsEmptyVehicle_ShouldFilterCorrectly() {
        // Arrange
        var filterItem = new EmptyStringFilterItem {
            EmptyOperator = EmptyOperators.isEmpty, ParsedField = StringFilterField.vehicle
        };

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().NotBeNull();
        // Additional assertions would depend on the actual test data structure
    }

    [Fact]
    public void ApplyEmptyStringFilter_WithIsNotEmptyVehicle_ShouldFilterCorrectly() {
        // Arrange
        var filterItem = new EmptyStringFilterItem {
            EmptyOperator = EmptyOperators.isNotEmpty, ParsedField = StringFilterField.vehicle
        };

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().NotBeNull();
    }

    [Fact]
    public void ApplyDateRangeFilter_WithAndOperator_ShouldFilterCorrectly() {
        // Arrange
        var startDate = DateTime.UtcNow.AddDays(-10);
        var endDate = DateTime.UtcNow.AddDays(-5);

        var filterItem = new DateRangeFilterItem {
            After = new DateRangeValue {
                Value = startDate, Strategy = Strategy.inclusive
            },
            Before = new DateRangeValue {
                Value = endDate, Strategy = Strategy.inclusive
            },
            ParsedField = DateRangeFilterField.requestDate,
            OperatorType = LogicOperator.And
        };

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().NotBeNull();
    }

    [Fact]
    public void ApplyDateRangeFilter_WithOrOperator_ShouldFilterCorrectly() {
        // Arrange
        var startDate = DateTime.UtcNow.AddDays(-10);
        var endDate = DateTime.UtcNow.AddDays(-5);

        var filterItem = new DateRangeFilterItem {
            After = new DateRangeValue {
                Value = startDate, Strategy = Strategy.inclusive
            },
            Before = new DateRangeValue {
                Value = endDate, Strategy = Strategy.inclusive
            },
            ParsedField = DateRangeFilterField.requestDate,
            OperatorType = LogicOperator.Or
        };

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().NotBeNull();
    }

    [Theory]
    [InlineData(StringFilterField.driver)]
    [InlineData(StringFilterField.driverEmail)]
    [InlineData(StringFilterField.vehicleType)]
    [InlineData(StringFilterField.purpose)]
    [InlineData(StringFilterField.vehicleCommanderEmail)]
    [InlineData(StringFilterField.requestorEmail)]
    public void ApplySingleStringFilter_WithDifferentFields_ShouldHandleAllFields(StringFilterField field) {
        // Arrange
        SetupAllMocks();
        var filterItem = new SingleStringStringFilterItem {
            Value = "test",
            ParsedField = field,
            StringOperator = StringOperators.contains,
            Case = FilterCaseSensitivity.insensitive
        };

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().NotBeNull();
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public void ApplySingleStringFilter_WithCaseSensitivity_ShouldHandleBothCases(bool isCaseSensitive) {
        // Arrange
        SetupVehicleMocks();
        var filterItem = new SingleStringStringFilterItem {
            Value = "ABC123",
            ParsedField = StringFilterField.vehicle,
            StringOperator = StringOperators.contains,
            Case = isCaseSensitive ? FilterCaseSensitivity.sensitive : FilterCaseSensitivity.insensitive
        };

        // Act
        var result = _service.ApplyFilterItem(_testBookings, filterItem, 1L);

        // Assert
        result.Should().NotBeNull();
    }

    private void SetupVehicleMocks() {
        var vehicles = new List<Vehicle> {
            new Vehicle {
                VehicleId = 1,
                Registration = "ABC123",
                UserId = 1
            },
            new Vehicle {
                VehicleId = 2,
                Registration = "XYZ789",
                UserId = 1
            }
        }.AsQueryable();

        var mockVehicleSet = CreateMockDbSet(vehicles);
        _mockCtDbContext.Setup(x => x.Vehicles).Returns(mockVehicleSet.Object);
    }

    private void SetupAllMocks() {
        SetupVehicleMocks();

        // Setup drivers
        var drivers = new List<ClientDriver> {
            new ClientDriver {
                ClientDriverId = "1",
                DriverName = "John",
                DriverSurname = "Doe",
                EMail = "<EMAIL>",
                UserId = 1
            }
        }.AsQueryable();
        var mockDriverSet = CreateMockDbSet(drivers);
        _mockFleetDbContext.Setup(x => x.ClientDrivers).Returns(mockDriverSet.Object);

        // Setup vehicle types
        var vehicleTypes = new List<BookingVehicleType> {
            new BookingVehicleType {
                BookingVehicleTypeId = 1, BookingVehicleType1 = "Car"
            }
        }.AsQueryable();
        var mockVehicleTypeSet = CreateMockDbSet(vehicleTypes);
        _mockPoolDbContext.Setup(x => x.BookingVehicleTypes).Returns(mockVehicleTypeSet.Object);

        // Setup purposes
        var purposes = new List<BookingPurpose> {
            new BookingPurpose {
                BookingPurposeId = 1, BookingPurpose1 = "Business"
            }
        }.AsQueryable();
        var mockPurposeSet = CreateMockDbSet(purposes);
        _mockPoolDbContext.Setup(x => x.BookingPurposes).Returns(mockPurposeSet.Object);

        // Setup client users
        var clientUsers = new List<ClientUser> {
            new ClientUser {
                ClientUserId = "1",
                EMail = "<EMAIL>",
                UserId = 1
            }
        }.AsQueryable();
        var mockClientUserSet = CreateMockDbSet(clientUsers);
        _mockFleetDbContext.Setup(x => x.ClientUsers).Returns(mockClientUserSet.Object);

        // Setup SCDF additional info
        var scdfInfo = new List<ScdfBookingAdditionalInfo> {
            new ScdfBookingAdditionalInfo {
                BookingId = 1, VehicleCommanderClientUserId = "1"
            }
        }.AsQueryable();
        var mockScdfInfoSet = CreateMockDbSet(scdfInfo);
        _mockTfmsCustomDbContext.Setup(x => x.ScdfBookingAdditionalInfos).Returns(mockScdfInfoSet.Object);
    }

    private IQueryable<EFCore.Models.Pool.Booking> CreateTestBookings() {
        return new List<EFCore.Models.Pool.Booking> {
            new EFCore.Models.Pool.Booking {
                BookingId = 1,
                VehicleId = 1,
                RequestClientDriverId = "1",
                RequestTs = DateTime.UtcNow.AddDays(-7),
                StartTs = DateTime.UtcNow.AddDays(-6),
                BookingVehicleTypeId = 1,
                BookingPurposeId = 1,
                Requestor = "<EMAIL>",
                RequestClientUserId = "1"
            },
            new EFCore.Models.Pool.Booking {
                BookingId = 2,
                VehicleId = null,
                RequestClientDriverId = null,
                RequestTs = DateTime.UtcNow.AddDays(-14),
                StartTs = DateTime.UtcNow.AddDays(-13),
                BookingVehicleTypeId = null,
                BookingPurposeId = null,
                Requestor = null,
                RequestClientUserId = null
            }
        }.AsQueryable();
    }

    private Mock<DbSet<T>> CreateMockDbSet<T>(IQueryable<T> data) where T : class {
        var mockSet = new Mock<DbSet<T>>();
        mockSet.As<IQueryable<T>>().Setup(m => m.Provider).Returns(data.Provider);
        mockSet.As<IQueryable<T>>().Setup(m => m.Expression).Returns(data.Expression);
        mockSet.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(data.ElementType);
        mockSet.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(data.GetEnumerator());
        return mockSet;
    }

    public void Dispose() {
        // Cleanup if needed
    }
}

// Mock classes for testing (these would typically be in your domain models)
public class Vehicle {
    public long VehicleId { get; set; }
    public string Registration { get; set; }
    public long UserId { get; set; }
}

public class ClientDriver {
    public string ClientDriverId { get; set; }
    public string DriverName { get; set; }
    public string DriverSurname { get; set; }
    public string EMail { get; set; }
    public long UserId { get; set; }
}

public class BookingVehicleType {
    public long BookingVehicleTypeId { get; set; }
    public string BookingVehicleType1 { get; set; }
}

public class BookingPurpose {
    public long BookingPurposeId { get; set; }
    public string BookingPurpose1 { get; set; }
}

public class ClientUser {
    public string ClientUserId { get; set; }
    public string EMail { get; set; }
    public long UserId { get; set; }
}

public class ScdfBookingAdditionalInfo {
    public long? BookingId { get; set; }
    public string VehicleCommanderClientUserId { get; set; }
}