﻿using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Features.CreateBooking.Scdf;
using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Booking.IO.Http.Filters;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.User.Domain.Permissions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Minio;
using Moq;
using NUnit.Framework;
using ClientUser = Cartrack.EFCore.Models.Fleet.ClientUser;
using Constants = Cartrack.Fleet.Booking.Domain.Common.Constants;

namespace Cartrack.Fleet.Booking.Tests.Scdf;

[TestFixture]
public class ScdfCreateBookingsTest {
    [SetUp]
    public void Setup() {
        var testData = new BookingTestData();
        this._testData = testData;
    }

    private BookingTestData? _testData;

    [Test]
    public async Task Can_Create_Booking_When_Request_Is_Valid_UseCase1() {
        /*  Use Case 1
         *   - Any Driver
         *   - Any Commander
         *   - Booking Purpose (pre-defined)
         *   - Booking For Others (false)
         *   - Return Journey
         *   - Pickup Location (defined)
         *   - No Equipment
         */

        //Create 1 booking to initialize the tables
        this._testData!.AddBookingRules();
        this._testData.Create(1, DateTime.Now);
        var start = DateTime.UtcNow;
        var end = start.AddHours(1);
        var requestedByUserId = Guid.NewGuid().ToString();
        var clientUser = this._testData.AddClientUser(requestedByUserId);
        var remarks = "Yo this is my remarks";
        var bookingDesc = "Need a vehicle for a meeting";

        var builder = this.CreateScdfVehicleBookingBuilder();
        var context = this._testData.CreateHttpContextAccessor(clientUser);        
        var repo = this.CreateScdfBookingRepository(builder, context, ValidateBooking);
        var ruleRepo = this.CreateScdfBookingRuleRepository();
        var logger = new Mock<ILogger<ScdfCreateBookingHandler>>();

        var createRequest = new ScdfCreateBookingRequest {
            Account = this._testData.Account,
            UserId = this._testData.UserId,
            BookingPurposeId = 1,
            BookingPurposeDescription = bookingDesc,
            RequestClientUserId = requestedByUserId,
            StartTs = start,
            EndTs = end,
            BookingVehicleTypeId = 1,
            DriverType = DriverType.AnyDriver,
            //RequestClientDriverId = requestedByDriverId,
            EquipmentType = EquipmentType.NoEquipment,
            Accessories = [],
            Remarks = remarks,
            PickupSiteLocationId = 1,
            JourneyType = JourneyType.Return,
            Journeys = [new JourneyLocation { Type = "freetext", Value = "Cartrack Trion Building" }],
            VehicleCommanderType = VehicleCommanderType.AnyCommander,
            NumberOfPassengers = 1,
            EquipmentAttachmentIds = [],
            EquipmentAttachments = []
        };

        var validateBookingCalled = false;

        async Task<long> ValidateBooking(VehicleBookingBase arg) {
            validateBookingCalled = true;
            await Task.Yield();

            /*  Validate this Use Case 1
             *   - Any Driver
             *   - Any Commander
             *   - Booking Purpose (pre-defined)
             *   - Booking For Others (false)
             *   - Return Journey
             *   - Pickup Location (defined)
             *   - No Equipment
             */
            Assert.That(arg.RequestedBy, Is.Not.Null);
            Assert.That(arg.RequestedBy.ClientUserId, Is.EqualTo(arg.RequestClientUserId));
            Assert.That(arg.Driver, Is.Null);
            Assert.That(arg.RequestClientDriverId, Is.Empty);
            Assert.That(arg.PickupLocation, Is.Not.Null);
            Assert.That(arg.StartDate, Is.EqualTo(start.AddSeconds(1)));
            Assert.That(arg.EndDate, Is.EqualTo(end));
            Assert.That(arg.Remarks, Is.EqualTo(remarks));
            Assert.That(arg.BookingPurposeDescription, Is.EqualTo(bookingDesc));
            Assert.That(arg.BookingPurposeId, Is.EqualTo(1));
            Assert.That(arg.VehicleCommander, Is.Null);
            Assert.That(arg.VehicleCommanderType, Is.EqualTo(VehicleCommanderType.AnyCommander));
            Assert.That(arg.Driver, Is.Null);
            Assert.That(arg.DriverType, Is.EqualTo(DriverType.AnyDriver));
            Assert.That(arg.AccessoryIds!.Count, Is.EqualTo(0));
            Assert.That(arg.JourneyType, Is.EqualTo(JourneyType.Return));
            Assert.That(arg.Journeys.Count, Is.EqualTo(1));
            Assert.That(arg.Journeys[0].Id, Is.EqualTo(0));
            return 1;
        }
 
        var handler = new ScdfCreateBookingHandler(repo, builder, ruleRepo, context, logger.Object);
        var resp = await handler.Handle(createRequest, CancellationToken.None);
        Assert.That(validateBookingCalled, Is.True);
    }
    
    [Test]
    public async Task Can_Create_Booking_When_Request_Is_Valid_UseCase2() {
        /*  Use Case 2
         *   - Self Drive
         *   - Self Commander
         *   - Booking Purpose (pre-defined)
         *   - Booking For Others (True)
         *   - Return Journey
         *   - Pickup Location (defined)
         *   - No Equipment
         */
        
        //Create 1 booking to initialize the tables
        this._testData!.AddBookingRules();
        this._testData.Create(1, DateTime.Now);
        var start = DateTime.UtcNow;
        var end = start.AddHours(1);
        var requestedByUserId = Guid.NewGuid().ToString();
        var requestedForId = Guid.NewGuid().ToString();
        var requestedByDriverId = Guid.NewGuid().ToString();
        this._testData.AddClientDriver(requestedByUserId, requestedByDriverId);
        var clientUser = this._testData.AddClientUser(requestedByUserId, 1);
        var requestedForUser = this._testData.AddClientUser(requestedForId, 2);
        //var driver = this._testData.AddClientDriver(requestedByUserId, requestedByDriverId);
        var remarks = "Yo this is my remarks";
        var bookingDesc = "Need a vehicle for a meeting";

        var builder = this.CreateScdfVehicleBookingBuilder();

        var mockContext = this._testData.CreateHttpContextAccessor(clientUser);
        var repo = this.CreateScdfBookingRepository(builder, mockContext, ValidateBooking);
        var ruleRepo = this.CreateScdfBookingRuleRepository();
        var logger = new Mock<ILogger<ScdfCreateBookingHandler>>();

        var createRequest = new ScdfCreateBookingRequest {
            Account = this._testData.Account,
            UserId = this._testData.UserId,
            BookingPurposeId = 1,
            BookingPurposeDescription = bookingDesc,
            RequestClientUserId = requestedByUserId,
            RequestedForClientUserId = requestedForId,
            StartTs = start,
            EndTs = end,
            BookingVehicleTypeId = 1,
            DriverType = DriverType.SelfDrive,
            //RequestClientDriverId = requestedByDriverId,
            EquipmentType = EquipmentType.CanFitInCarBoot,
            Accessories = [],
            Remarks = remarks,
            PickupSiteLocationId = 1,
            JourneyType = JourneyType.Single,
            Journeys = [new JourneyLocation { Type = "freetext", Value = "Cartrack Trion Building" }],
            VehicleCommanderType = VehicleCommanderType.Self,
            NumberOfPassengers = 1,
            EquipmentAttachmentIds = [],
            EquipmentAttachments = []
        };

        var validateBookingCalled = false;

        async Task<long> ValidateBooking(VehicleBookingBase arg) {
            validateBookingCalled = true;
            await Task.Yield();
           
            /*  Validate Use Case 2
             *   - Self Drive
             *   - Self Commander
             *   - Booking Purpose (pre-defined)
             *   - Booking For Others (True)
             *   - Return Journey
             *   - Pickup Location (defined)
             *   - No Equipment
             */
            
            Assert.That(arg.RequestedBy, Is.Not.Null);
            Assert.That(arg.RequestedBy.ClientUserId, Is.EqualTo(arg.RequestClientUserId));
            Assert.That(arg.PickupLocation, Is.Not.Null);
            Assert.That(arg.StartDate, Is.EqualTo(start.AddSeconds(1)));
            Assert.That(arg.EndDate, Is.EqualTo(end));
            Assert.That(arg.Remarks, Is.EqualTo(remarks));
            Assert.That(arg.BookingPurposeDescription, Is.EqualTo(bookingDesc));
            Assert.That(arg.BookingPurposeId, Is.EqualTo(1));
            Assert.That(arg.VehicleCommander, Is.Not.Null);
            Assert.That(arg.VehicleCommanderType, Is.EqualTo(VehicleCommanderType.Self));
            Assert.That(arg.VehicleCommander!.ClientUserId, Is.EqualTo(requestedByUserId));
            
            Assert.That(arg.DriverType, Is.EqualTo(DriverType.SelfDrive));
            Assert.That(arg.Driver, Is.Not.Null);
            Assert.That(arg.Driver!.DriverId, Is.EqualTo(arg.RequestClientDriverId));
            
            Assert.That(arg.AccessoryIds!.Count, Is.EqualTo(0));
            Assert.That(arg.JourneyType, Is.EqualTo(JourneyType.Single));
            Assert.That(arg.Journeys.Count, Is.EqualTo(1));
            Assert.That(arg.Journeys[0].Id, Is.EqualTo(0));
            return 1;
        }
        
        var handler = new ScdfCreateBookingHandler(repo, builder, ruleRepo, mockContext, logger.Object);
        var resp = await handler.Handle(createRequest, CancellationToken.None);
        Assert.That(validateBookingCalled, Is.True);
    }

    
    [Test]
    public async Task Can_Create_Booking_When_Request_Is_Valid_UseCase3() {
        /*  Use Case 3
         *   - Specific Driver
         *   - Specific Commander
         *   - Booking Purpose (pre-defined)
         *   - Booking For Others (False)
         *   - Multiple Journey
         *   - Pickup Location (defined)
         *   - No Equipment
         */

        //Create 1 booking to initialize the tables
        this._testData!.AddBookingRules();
        this._testData.Create(1, DateTime.Now);
        
        var start = DateTime.UtcNow;
        var end = start.AddHours(1);
        var requestedByUserId = Guid.NewGuid().ToString();
        var commanderId = Guid.NewGuid().ToString();
        var requestedDriverId = Guid.NewGuid().ToString();
        this._testData.AddClientDriver(requestedByUserId, requestedDriverId);
        var clientUser = this._testData.AddClientUser(requestedByUserId, 1);
        var vehicleCommander = this._testData.AddClientUser(commanderId, 2);
        var remarks = "Yo this is my remarks";
        var bookingDesc = "Need a vehicle for a meeting";

        var builder = this.CreateScdfVehicleBookingBuilder();
        var context = this._testData.CreateHttpContextAccessor(clientUser);
        var repo = this.CreateScdfBookingRepository(builder, context, ValidateBooking);
        var ruleRepo = this.CreateScdfBookingRuleRepository();
        var logger = new Mock<ILogger<ScdfCreateBookingHandler>>();

        var createRequest = new ScdfCreateBookingRequest {
            Account = this._testData.Account,
            UserId = this._testData.UserId,
            BookingPurposeId = 1,
            BookingPurposeDescription = bookingDesc,
            RequestClientUserId = requestedByUserId,
            RequestClientDriverId = requestedDriverId,
            StartTs = start,
            EndTs = end,
            BookingVehicleTypeId = 1,
            DriverType = DriverType.Specific,
            //RequestClientDriverId = requestedByDriverId,
            EquipmentType = EquipmentType.CanFitInCarBoot,
            Accessories = [],
            Remarks = remarks,
            PickupSiteLocationId = 1,
            JourneyType = JourneyType.MultiStop,
            Journeys = [ 
                new JourneyLocation { Type = "freetext", Value = "Cartrack Trion Building" },
                new JourneyLocation { Type = "freetext", Value = "Marina Bay Sands" },
                new JourneyLocation { Type = "freetext", Value = "Cartrack Trion Building" }],
            VehicleCommanderType = VehicleCommanderType.Specific,
            VehicleCommanderClientUserId = commanderId,
            NumberOfPassengers = 20,
            EquipmentAttachmentIds = [],
            EquipmentAttachments = []
        };

        var validateBookingCalled = false;

        async Task<long> ValidateBooking(VehicleBookingBase arg) {
            validateBookingCalled = true;
            await Task.Yield();
            
            /*  Use Case 3
             *   - Specific Driver
             *   - Specifi Commander
             *   - Booking Purpose (pre-defined)
             *   - Booking For Others (False)
             *   - Multiple Journey
             *   - Pickup Location (defined)
             *   - No Equipment
             */
             
            Assert.That(arg.RequestedBy, Is.Not.Null);
            Assert.That(arg.RequestedBy.ClientUserId, Is.EqualTo(arg.RequestClientUserId));
            Assert.That(arg.PickupLocation, Is.Not.Null);
            Assert.That(arg.StartDate, Is.EqualTo(start.AddSeconds(1)));
            Assert.That(arg.EndDate, Is.EqualTo(end));
            Assert.That(arg.Remarks, Is.EqualTo(remarks));
            Assert.That(arg.BookingPurposeDescription, Is.EqualTo(bookingDesc));
            Assert.That(arg.BookingPurposeId, Is.EqualTo(1));
            Assert.That(arg.VehicleCommander, Is.Not.Null);
            Assert.That(arg.VehicleCommanderType, Is.EqualTo(VehicleCommanderType.Specific));
            Assert.That(arg.VehicleCommander!.ClientUserId, Is.EqualTo(commanderId));
            
            Assert.That(arg.DriverType, Is.EqualTo(DriverType.Specific));
            Assert.That(arg.Driver, Is.Not.Null);
            Assert.That(arg.Driver!.DriverId, Is.EqualTo(requestedDriverId));
            Assert.That(arg.Driver!.DriverId, Is.EqualTo(arg.RequestClientDriverId));
            
            Assert.That(arg.AccessoryIds!.Count, Is.EqualTo(0));
            Assert.That(arg.JourneyType, Is.EqualTo(JourneyType.MultiStop));
            Assert.That(arg.Journeys.Count, Is.EqualTo(3));
            Assert.That(arg.Journeys[0].Order, Is.EqualTo(1));
            Assert.That(arg.Journeys[1].Order, Is.EqualTo(2));
            Assert.That(arg.Journeys[3].Order, Is.EqualTo(3));
            return 1;
        }

        var handler = new ScdfCreateBookingHandler(repo, builder, ruleRepo, context, logger.Object);
        var resp = await handler.Handle(createRequest, CancellationToken.None);
        Assert.That(validateBookingCalled, Is.True);
    }
     
    private ScdfBookingRepository CreateScdfBookingRepository(ScdfVehicleBookingBuilder builder, IHttpContextAccessor context, Func<VehicleBookingBase, Task<long>> onCreateBooking) {
        var repo = new TestScdfBookingRepository(
            this._testData!.MockAppSettings.Object,
            this._testData!.MockTfmsCustomDbContext.Object,
            this._testData.MockFleetDbContext.Object,
            this._testData.MockCtDbContext.Object,
            this._testData.MockPoolDbContext.Object,
            this._testData.MockMinIo.Object,
            this._testData.MockBookingFilterService.Object,
            context,
            _ => builder
        ) { OnCreateBooking = onCreateBooking };

        return repo;
    }

    private BookingRuleRepository CreateScdfBookingRuleRepository() {
        var repo = new BookingRuleRepository(
            new AppSettings(this._testData!.MockEnv),
            this._testData.MockPoolDbContext.Object
        );

        return repo;
    }

    private ScdfVehicleBookingBuilder CreateScdfVehicleBookingBuilder() {
        var builder = new ScdfVehicleBookingBuilder(
            this._testData!.MockAppSettings.Object,
            this._testData.MockMediator.Object,
            this._testData.MockDriverRepo.Object,
            this._testData.MockCtDbContext.Object,
            this._testData.MockFleetDbContext.Object,
            this._testData.MockPoolDbContext.Object,
            this._testData.MockHttpContextAccessor.Object,
            this._testData.MockTfmsCustomDbContext.Object
        ) { CreateAppFleetDb = _ => this._testData.MockFleetDbContext.Object };
        return builder;
    }

    private class TestScdfBookingRepository : ScdfBookingRepository {
        public TestScdfBookingRepository(
            AppSettings settings,
            AppTfmsCustomDbContext appTfmsCustomDbContext, AppFleetDbContext appFleetDbContext, AppCtDbContext appCtDbContext, AppPoolDbContext appPoolDbContext, IMinioClient minioClient,
            IBookingFilterService bookingFilterService,
            IHttpContextAccessor context,
            Func<object, ScdfVehicleBookingBuilder> func)
            : base(settings, appTfmsCustomDbContext, appFleetDbContext, appCtDbContext, appPoolDbContext, minioClient, bookingFilterService, context, func,
             new Mock<ILogger<ScdfBookingRepository>>().Object
            ) {
        }

        public Func<VehicleBookingBase, Task<long>>? OnCreateBooking { get; set; }

        public override Task<long> CreateBooking(VehicleBookingBase booking) {
            return this.OnCreateBooking!.Invoke(booking);
        }
    }
}