# Use the official PostgreSQL 14.13 image as the base
FROM postgres:14.13

### FRESH SETUP ONLY!!! ###
# Set environment variables
#ENV POSTGRES_USER=postgres
#ENV POSTGRES_PASSWORD=Cartrack@123!!
#ENV POSTGRES_DB=postgres
###########################

# Unzip offline packages and install them
RUN mkdir offline-packages
ADD ./offline-packages.tar.xz offline-packages/
RUN yes | dpkg -i --force-confnew offline-packages/*.deb

# Cleanup
RUN rm -rf /var/lib/apt/lists/* offline-packages/

# Copy configuration files
COPY ./custom_configs/postgresql.conf /etc/postgresql/postgresql.conf
COPY ./custom_configs/pg_hba.conf /etc/postgresql/pg_hba.conf

# Unzip share and lib directories
ADD ./share.tar.xz /usr/share/postgresql/14/
ADD ./lib.tar.xz /usr/lib/postgresql/14/lib/

# Ensure correct permissions
RUN chown -R postgres:postgres /usr/share/postgresql/ /usr/lib/postgresql/ /etc/postgresql/ /var/lib/postgresql/
RUN chmod -R 700 /usr/share/postgresql/ /usr/lib/postgresql/ /etc/postgresql/ /var/lib/postgresql/

# Expose the configured port
EXPOSE 5555

# Switch to postgres user
USER postgres

# Start Postgres
ENTRYPOINT ["postgres", "-c", "config_file=/etc/postgresql/postgresql.conf"]