#!/bin/sh

DEBUG=${DEBUG:-0}

# Color definitions
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Traps
trap 'printf "\n${RED}Script interrupted. Exiting with error.${NC}\n"; exit 1' SIGTERM SIGINT

# Function to print step with optional debug output
run_step() {
    MSG="$1"
    shift

    # Print the message in yellow without newline
    printf "${YELLOW}%-50s${NC}" "$MSG"

    # Run the command in the background
    if [ "$DEBUG" -eq 1 ]; then
        # When DEBUG=1, show full output of the command
        "$@" &
    else
        # When DEBUG=0, suppress output (except spinner)
        "$@" > /dev/null 2>&1 &
    fi

    CMD_PID=$!

    # Spinner animation while waiting for the command to finish
    spin='—\|/'
    i=0

    while kill -0 $CMD_PID 2>/dev/null; do
        i=$(( (i + 1) % 4 ))
        printf "\b${spin:$i:1}"   # Update spinner
        sleep 0.1
    done

    wait $CMD_PID
    EXIT_CODE=$?

    # Replace spinner with final status
    if [ $EXIT_CODE -eq 0 ]; then
        printf "\b${GREEN}[Ok]${NC}\n"
    else
        printf "\b${RED}[Failed]${NC}\n"
        exit 1
    fi
}

wait_for_pg() {
    MSG="Waiting for PostgreSQL to be ready"
    printf "${YELLOW}%-50s${NC}" "$MSG"

    ELAPSED=0
    TIMEOUT=150
    spin='—\|/'
    i=0

    while [ $ELAPSED -lt $TIMEOUT ]; do
        if docker exec tfms-qa-db pg_isready -U postgres -p 5555 > /dev/null 2>&1; then
            printf "\b${GREEN}[Ok]${NC}\n"
            return 0
        fi
        i=$(( (i + 1) % 4 ))
        printf "\b${spin:$i:1}"
        sleep 0.1
        ELAPSED=$((ELAPSED + 1))
    done

    printf "\b${RED}[Failed]${NC}\n"
    return 1
}

# Optional: DEBUG mode
printf "${YELLOW}DEBUG=$DEBUG${NC}\n"

printf "\n${CYAN}##### CLEANUP PREPARATIONS #####${NC}\n"
# Remove old instances
run_step "Stop current tfms-qa-db container" $(docker stop tfms-qa-db &>/dev/null || true)
run_step "Remove current tfms-qa-db container" $(docker rm -f tfms-qa-db &>/dev/null || true)
run_step "Remove current tfms-qa-db image" $(docker image rm -f tfms_qa_db-tfms-qa-db:latest &>/dev/null || true)
run_step "Remove current pg_data dir" rm -rf ./pg_data
printf "${CYAN}################################${NC}\n\n"

# Extract pg_data.7z
run_step "Extract pg_data.7z" 7z x db-compressed/pg_data.7z -mmt=on -bsp2

# Fix permissions and ownership
run_step "Fix permissions" chmod -R 700 pg_data/ custom_configs/*
run_step "Fix ownership" chown -R 999:999 pg_data/ custom_configs/*

# Build and run
run_step "Build image for tfms-qa-db" docker-compose build --no-cache tfms-qa-db
run_step "Run container tfms-qa-db" docker-compose up -d tfms-qa-db

# Check PostgreSQL readiness using pg_isready
wait_for_pg || exit 1