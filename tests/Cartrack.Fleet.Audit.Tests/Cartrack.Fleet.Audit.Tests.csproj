<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Cartrack.EFCore.Models.Pool" Version="1.1.0" />
        <PackageReference Include="coverlet.collector" Version="6.0.0" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
        <PackageReference Include="NUnit" Version="3.14.0" />
        <PackageReference Include="NUnit.Analyzers" Version="3.9.0" />
        <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
    </ItemGroup>

    <ItemGroup>
        <Using Include="NUnit.Framework" />
    </ItemGroup>

</Project>
